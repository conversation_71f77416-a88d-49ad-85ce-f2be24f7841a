<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评估任务列表 - 城市级关基级联的网络仿真度评估系统</title>
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Echarts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* 页面整体放大120% */
        html {
            zoom: 110%;
        }

        :root {
            --primary-color: #0096FF;
            --secondary-color: #00E0FF;
            --background-dark: #0F1520;
            --background-card: #000f217e;
            --text-color: #E0E6F0;
            --text-secondary: #A0A8B8;
            --border-color: #2A3142;
            --highlight-color: #3E9BFF;
            --success-color: #00C48C;
            --warning-color: #FFB946;
            --danger-color: #F25767;
            --info-color: #0095FF;
        }

        /* 亮色主题变量 */
        :root.light-theme {
            --primary-color: #0078D4;
            --secondary-color: #00A2E0;
            --background-dark: #FFFFFF;
            --background-card: #000f2133;
            --text-color: #333333;
            --text-secondary: #666666;
            --border-color: #E0E6ED;
            --highlight-color: #106EBE;
            --success-color: #0A9D5A;
            --warning-color: #F29D41;
            --danger-color: #E74C3C;
            --info-color: #0078D4;
        }

        /* Logo样式 */
        .logo {
	zoom: 110%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 10px;
        }

        .logo-icon {
            font-size: 24px;
            color: var(--primary-color);
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 2px rgba(0, 224, 255, 0.4));
        }

        .logo span {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 0.5px;
            background: linear-gradient(90deg, var(--text-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: transparent;
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            min-height: 100vh;
            background: transparent;
        }

        /* 顶部导航栏样式 */
        .header {
            background-color: rgba(16, 22, 36, 0.95);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        
        /* 亮色主题的导航栏调整 */
        :root.light-theme .header {
            background-color: rgba(245, 247, 250, 0.95);
            border-bottom: 1px solid var(--border-color);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 60px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .nav-item {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .nav-link {
            color: var(--text-color);
            text-decoration: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link.active {
            color: var(--primary-color);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 0 0 6px 6px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        :root.light-theme .dropdown-menu {
            background-color: var(--background-card);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            border-bottom: 1px solid var(--border-color);
        }
		
		.dropdown-menu {
			list-style-type: none; /* 去掉前面的点 */
		}

        .dropdown-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px;
        }

        .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            padding-left: 20px;
        }
        
        :root.light-theme .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }

        .dropdown-icon {
            margin-right: 8px;
        }

        .nav-tools {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-icon, .user-profile {
            position: relative;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .notification-icon:hover, .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        :root.light-theme .notification-icon:hover,
        :root.light-theme .user-profile:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-profile img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 24px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
        }

        .page-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .task-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: var(--highlight-color);
        }

        /* 搜索和筛选区域 */
        .filter-section {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
        }

        .search-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .search-input-wrapper {
            position: relative;
            flex: 1;
        }

        .search-input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            background-color: #000f2133;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.08);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .advanced-filter-toggle {
            white-space: nowrap;
            color: var(--primary-color);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 0 10px;
        }

        .advanced-filter-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .filter-select, .filter-input {
            padding: 8px 12px;
            background-color: #000f2133;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
        }
        
        /* 统计卡片区域 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 5px 0 0 5px;
        }

        .stat-card.success::before {
            background-color: var(--success-color);
        }

        .stat-card.warning::before {
            background-color: var(--warning-color);
        }

        .stat-card.danger::before {
            background-color: var(--danger-color);
        }

        .stat-card.info::before {
            background-color: var(--info-color);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .stat-title {
            font-size: 13px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stat-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
        }

        .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-description {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-trend-up {
            color: var(--success-color);
        }

        .stat-trend-down {
            color: var(--danger-color);
        }

        /* 任务列表表格 */
        .tasks-section {
            margin-bottom: 24px;
        }

        .table-responsive {
            background-color: var(--background-card);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .table-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .table-title {
            font-size: 16px;
            font-weight: 500;
        }

        .table-actions {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .view-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-right: 10px;
        }

        .bulk-select {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-right: 15px;
            border-left: 1px solid var(--border-color);
            padding-left: 20px;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
        }

        .bulk-action-btn {
            padding: 6px 12px;
            background-color: #000f2133;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .bulk-action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .bulk-action-btn.delete {
            color: var(--danger-color);
            border-color: rgba(242, 87, 103, 0.3);
        }

        .bulk-action-btn.delete:hover {
            background-color: rgba(242, 87, 103, 0.1);
            border-color: var(--danger-color);
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
        }

        .task-table th, .task-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .task-table th {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 13px;
            background-color: #000f2133;
        }

        .task-table tbody tr {
            transition: all 0.2s ease;
        }

        .task-table tbody tr:hover {
            background-color: #000f2133;
        }

        .task-table tbody tr:last-child td {
            border-bottom: none;
        }

        .checkbox-cell {
            width: 30px;
        }

        .task-id {
            font-family: monospace;
            color: var(--text-secondary);
        }

        .task-name {
            font-weight: 500;
        }

        .task-desc {
            color: var(--text-secondary);
            font-size: 13px;
            max-width: 250px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .task-status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-running {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .status-paused {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .status-failed {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .status-pending {
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--info-color);
        }

        .status-completed {
            background-color: rgba(160, 168, 184, 0.1);
            color: var(--text-secondary);
        }

        .task-actions-cell {
            text-align: right;
            white-space: nowrap;
        }

        .task-action {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: #000f2133;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 5px;
        }

        .task-action:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .task-action.play:hover {
            color: var(--success-color);
            border-color: var(--success-color);
            background-color: rgba(0, 196, 140, 0.1);
        }

        .task-action.pause:hover {
            color: var(--warning-color);
            border-color: var(--warning-color);
            background-color: rgba(255, 185, 70, 0.1);
        }

        .task-action.stop:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
            background-color: rgba(242, 87, 103, 0.1);
        }

        .task-action.edit:hover {
            color: var(--info-color);
            border-color: var(--info-color);
            background-color: rgba(0, 149, 255, 0.1);
        }

        .task-action.logs:hover {
            color: var(--text-color);
            border-color: var(--text-color);
            background-color: rgba(224, 230, 240, 0.1);
        }

        .task-action.delete:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
            background-color: rgba(242, 87, 103, 0.1);
        }

        .task-action.results:hover {
            color: var(--warning-color);
            border-color: var(--warning-color);
            background-color: rgba(255, 185, 70, 0.1);
        }

        .priority-badge {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .priority-high {
            background-color: var(--danger-color);
        }

        .priority-medium {
            background-color: var(--warning-color);
        }

        .priority-low {
            background-color: var(--success-color);
        }

        .favorite-action {
            color: var(--text-secondary);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .favorite-action:hover, .favorite-action.active {
            color: var(--warning-color);
        }

        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
        }

        .page-info {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .pagination {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .page-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .page-btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .page-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 进度条样式 */
        .progress-bar-container {
            width: 100%;
            height: 8px;
            background-color: #000f2133;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-bar {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-bar.success {
            background-color: var(--success-color);
        }

        .progress-bar.warning {
            background-color: var(--warning-color);
        }

        .progress-bar.danger {
            background-color: var(--danger-color);
        }

        .progress-bar.info {
            background-color: var(--info-color);
        }

        .progress-text {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            margin-top: 4px;
        }

        /* 标记/收藏样式 */
        .star-icon {
            transition: all 0.3s ease;
        }

        .star-active {
            color: var(--warning-color);
        }

        /* 自定义复选框 */
        .custom-checkbox {
            position: relative;
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 5px;
            vertical-align: middle;
        }

        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 18px;
            width: 18px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .custom-checkbox input:checked ~ .checkmark {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 2px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input:checked ~ .checkmark:after {
            display: block;
        }

        /* 任务执行日志卡片 */
        .task-logs-card {
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .card-header {
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .card-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .card-action:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
        }

        .card-body {
            padding: 20px;
        }

        .log-timeline {
            position: relative;
            padding-left: 24px;
            max-height: 350px;
            overflow-y: auto;
        }

        .log-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 8px;
            height: 100%;
            width: 2px;
            background-color: var(--border-color);
        }

        .log-item {
            position: relative;
            padding-bottom: 20px;
        }

        .log-item:last-child {
            padding-bottom: 0;
        }

        .log-dot {
            position: absolute;
            left: -24px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 3px solid var(--background-card);
            z-index: 1;
        }

        .log-item.success .log-dot {
            background-color: var(--success-color);
        }

        .log-item.warning .log-dot {
            background-color: var(--warning-color);
        }

        .log-item.danger .log-dot {
            background-color: var(--danger-color);
        }

        .log-item.info .log-dot {
            background-color: var(--info-color);
        }

        .log-content {
            padding-bottom: 12px;
            border-bottom: 1px dashed var(--border-color);
        }

        .log-item:last-child .log-content {
            border-bottom: none;
        }

        .log-title {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .log-detail {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
            font-family: monospace;
            background-color: rgba(255, 255, 255, 0.03);
            padding: 6px;
            border-radius: 4px;
            white-space: pre-wrap;
        }

        .log-time {
            font-size: 12px;
            color: var(--text-secondary);
        }
		
		/* 弹窗样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: var(--background-card);
            border-radius: 8px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            margin: 0 auto;
            position: relative;
            animation: modal-in 0.3s ease;
        }

        @keyframes modal-in {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
            color: var(--text-color);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            color: var(--danger-color);
        }

        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
            font-size: 14px;
        }

        .radio-group {
            display: flex;
            gap: 20px;
        }

        .radio-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .radio-label input[type="radio"] {
            margin: 0;
        }

        .form-select {
            width: 100%;
            padding: 8px 12px;
            background-color: var(--background-dark);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 149, 255, 0.1);
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: none;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--highlight-color);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: var(--background-dark);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.05);
            transform: translateY(-1px);
        }
		
		/* 日志查看弹窗 */
        .mlog-modal .modal-body {
            padding: 0;
        }

        .mlog-header {
            padding: 12px 20px;
            background-color: rgba(0, 0, 0, 0.2);
            display: flex;
            justify-content: space-between;
        }

        .mlog-filter {
            display: flex;
            gap: 10px;
        }

        .mlog-level {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mlog-level.active {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .mlog-content {
            padding: 10px 0;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 13px;
            height: 400px;
            overflow-y: auto;
            background-color: rgba(0, 0, 0, 0.3);
        }

        .mlog-line {
            padding: 4px 20px;
            display: flex;
            gap: 8px;
            border-left: 3px solid transparent;
        }

        .mlog-line:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .mlog-time {
            color: var(--text-secondary);
            min-width: 140px;
        }

        .mlog-level-indicator {
            min-width: 60px;
            text-align: center;
            border-radius: 3px;
            font-weight: 500;
        }

        .mlog-info {
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
        }

        .mlog-warning {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .mlog-error {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .mlog-debug {
            background-color: rgba(151, 96, 255, 0.1);
            color: #9760FF;
        }

        .mlog-message {
            flex: 1;
        }

        .mlog-line.error {
            border-left-color: var(--danger-color);
        }

        .mlog-line.warning {
            border-left-color: var(--warning-color);
        }

        .mlog-search {
            position: relative;
            width: 200px;
        }

        .mlog-search input {
            width: 100%;
            padding: 6px 8px 6px 28px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 12px;
        }

        .mlog-search i {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* 响应式设计 */
        @media screen and (max-width: 1200px) {
            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .advanced-filter-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 768px) {
            .stats-cards {
                grid-template-columns: 1fr;
            }

            .task-table {
                min-width: 950px;
            }

            .table-responsive {
                overflow-x: auto;
            }

            .advanced-filter-row {
                grid-template-columns: 1fr;
            }

            .bulk-actions {
                display: none;
            }
        }

        @media screen and (max-width: 576px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .task-actions {
                width: 100%;
            }

            .search-row {
                flex-direction: column;
            }
        }

        /* 动画与过渡效果 */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 149, 255, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(0, 149, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 149, 255, 0);
            }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 卡片视图样式 */
        .card-view {
            display: none; /* 默认隐藏 */
            grid-template-columns: repeat(4, 1fr); /* 改为4列 */
            gap: 15px; /* 缩小间距 */
            margin-top: 15px;
            margin-bottom: 15px;
        }

        .task-card {
            background-color: var(--background-card);
            border-radius: 8px; /* 缩小圆角 */
            padding: 15px; /* 缩小内边距 */
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            font-size: 13px; /* 整体字体缩小 */
        }

        .task-card:hover {
            transform: translateY(-3px); /* 减小悬停效果 */
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
        }

        .task-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px; /* 缩小间距 */
        }

        .task-card-title {
            font-weight: 500;
            font-size: 14px; /* 缩小字体 */
            flex: 1;
        }

        .task-card-status {
            margin-left: 8px;
        }

        .task-card-id {
            font-family: monospace;
            color: var(--text-secondary);
            font-size: 12px;
            margin-top: 3px;
        }

        .task-card-desc {
            color: var(--text-secondary);
            font-size: 12px;
            margin-bottom: 10px;
            max-height: 36px;
            overflow: hidden;
        }

        .task-card-info {
            margin-top: 8px;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .task-card-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }

        .task-card-info-label {
            color: var(--text-secondary);
        }

        .task-card-progress {
            margin-top: auto;
        }

        .task-card-actions {
            display: flex;
            justify-content: flex-end;
            gap: 6px;
            margin-top: 10px;
        }

        .task-card .task-action {
            width: 28px; /* 缩小按钮尺寸 */
            height: 28px;
        }

        @media screen and (max-width: 1400px) {
            .card-view {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media screen and (max-width: 1000px) {
            .card-view {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 768px) {
            .card-view {
                grid-template-columns: 1fr;
            }
        }

        .view-toggle-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .view-toggle-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .view-toggle-btn.active {
            background-color: rgba(62, 155, 255, 0.2);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        @media screen and (max-width: 1400px) {
            .card-view {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* 主题切换开关样式 */
        .theme-switch {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 60px;
            height: 30px;
            background: rgba(0, 149, 255, 0.15);
            border-radius: 30px;
            padding: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .theme-switch:hover {
            transform: scale(1.05);
        }
        
        .theme-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-size: 14px;
        }
        
        .theme-switch-slider {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            left: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        :root.light-theme .theme-switch-slider {
            transform: translateX(30px);
        }

        /* 亮色主题的按钮样式调整 */
        :root.light-theme .action-btn {
            background-color: #000f2133;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .action-btn.primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .action-btn.primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        /* 亮色主题的统计卡片 */
        :root.light-theme .stat-card {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .stat-card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .stat-icon {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        /* 亮色主题的表格调整 */
        :root.light-theme .table-responsive {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .table-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .bulk-action-btn {
            background-color: #000f2133;
        }
        
        :root.light-theme .bulk-action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .task-table th {
            background-color: rgba(0, 0, 0, 0.01);
        }
        
        :root.light-theme .task-table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        :root.light-theme .status-running {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .status-paused {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .status-failed {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        :root.light-theme .status-pending {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .status-completed {
            background-color: rgba(160, 168, 184, 0.08);
        }
        
        :root.light-theme .task-action {
            background-color: #000f2133;
        }
        
        :root.light-theme .task-action:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        /* 亮色主题的弹窗样式调整 */
        :root.light-theme .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.4);
        }
        
        :root.light-theme .modal-content {
            background-color: var(--background-card);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .modal-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .modal-footer {
            border-top: 1px solid var(--border-color);
        }
        
        /* 亮色主题的表单元素 */
        :root.light-theme .form-control {
            background-color: #F8FAFD;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .form-control:focus {
            border-color: var(--primary-color);
            background-color: #FFFFFF;
        }
        
        /* 亮色主题的滚动条 */
        :root.light-theme ::-webkit-scrollbar-track {
            background: #F5F7FA;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb {
            background: #D0D6E0;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
        
        /* 亮色主题的筛选器 */
        :root.light-theme .filter-section {
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .filter-row {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .filter-toggle.active {
            background-color: rgba(62, 155, 255, 0.05);
        }
    </style>
    
<body>
    <div class="container">

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题和操作按钮 -->
            <div class="page-header">
                
                <div class="task-actions">
                    <button class="action-btn">
                        <i class="fas fa-file-import"></i> 导入任务
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-file-export"></i> 导出任务
                    </button>
                    <button class="action-btn primary" id="newEvalTaskBtn">
                        <i class="fas fa-plus"></i> 新建仿真度评估任务
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-cards">
                <div class="stat-card info">
                    <div class="stat-header">
                        <div class="stat-title">评估任务总数</div>
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                    </div>
                    <div class="stat-value">50</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长10个</span>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-header">
                        <div class="stat-title">执行成功</div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">15</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长8个</span>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-header">
                        <div class="stat-title">进行中</div>
                        <div class="stat-icon">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="stat-value">15</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长3个</span>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-header">
                        <div class="stat-title">已暂停</div>
                        <div class="stat-icon">
                            <i class="fas fa-pause"></i>
                        </div>
                    </div>
                    <div class="stat-value">15</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-down stat-trend-down"></i>
                        <span>较上月减少5个</span>
                    </div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-header">
                        <div class="stat-title">执行失败</div>
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="stat-value">5</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-down"></i>
                        <span>较上月增加4个</span>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="filter-section">
                <div class="search-row">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索任务名称、ID、测评模版...">
                    </div>
                    <button class="advanced-filter-toggle">
                        <i class="fas fa-sliders-h"></i> 高级筛选
                    </button>
                </div>
                <div class="advanced-filter-row">
                    <div class="filter-group">
                        <label class="filter-label">任务状态</label>
                        <select class="filter-select">
                            <option value="">全部状态</option>
                            <option value="running">运行中</option>
                            <option value="paused">已暂停</option>
                            <option value="pending">等待中</option>
                            <option value="completed">执行成功</option>
                            <option value="failed">执行失败</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">评估类型</label>
                        <select class="filter-select">
                            <option value="">全部类型</option>
                            <option value="functional">跨行业级联风险仿真度评估</option>
                            <option value="performance">电力行业仿真度评估</option>
                            <option value="security">通信行业仿真度评估</option>
                            <option value="reliability">交通行业仿真度评估</option>
                            <option value="integrated">燃气行业仿真度评估</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">创建时间</label>
                        <select class="filter-select">
                            <option value="">全部时间</option>
                            <option value="today">今天</option>
                            <option value="yesterday">昨天</option>
                            <option value="last7days">最近7天</option>
                            <option value="last30days">最近30天</option>
                            <option value="custom">自定义范围</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">评估场景</label>
                        <select class="filter-select">
                            <option value="">全部模版</option>
                            <option value="template1">中小企业网场景</option>
                            <option value="template2">交通系统测评场景</option>
                            <option value="template3">供水系统测评场景</option>
                            <option value="template4">燃气系统测评场景</option>
                            <option value="template5">通信系统测评场景</option>
                        </select>
                    </div>
                </div>
                <div class="filter-actions">
                    <button class="action-btn">重置筛选</button>
                    <button class="action-btn primary">应用筛选</button>
                </div>
            </div>

            <!-- 任务列表 -->
            <div class="tasks-section">
                <div class="table-responsive">
                    <div class="table-header">
                        <div class="table-title">全部评估任务</div>
                        <div class="table-actions">
                            <div class="view-toggle">
                                <button class="view-toggle-btn active" id="table-view-btn">
                                    <i class="fas fa-list"></i> 表格视图
                                </button>
                                <button class="view-toggle-btn" id="card-view-btn">
                                    <i class="fas fa-th-large"></i> 卡片视图
                                </button>
                            </div>
                            <div class="bulk-select">
                                <label class="custom-checkbox">
                                    <input type="checkbox" id="select-all">
                                    <span class="checkmark"></span>
                                </label>
                                <span>全选</span>
                            </div>
                            <div class="bulk-actions">
                                <button class="bulk-action-btn">
                                    <i class="fas fa-play"></i> 批量启动
                                </button>
                                <button class="bulk-action-btn">
                                    <i class="fas fa-pause"></i> 批量暂停
                                </button>
                                <button class="bulk-action-btn">
                                    <i class="fas fa-stop"></i> 批量终止
                                </button>
                                <button class="bulk-action-btn delete">
                                    <i class="fas fa-trash-alt"></i> 批量删除
                                </button>
                            </div>
                        </div>
                    </div>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </th>
                                <th>任务名称</th>
                                <th>评估场景</th>
                                <th>状态</th>
                                <th>进度</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="task-name">城市电力系统功能仿真度测评</span>
                                    <div class="task-desc">评估城市电力系统功能性参数与实际运行对比</div>
                                </td>
                                <td>企业电力供电场景</td>
                                <td><span class="task-status-badge status-running">运行中</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 68%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：68%</span>
                                        <span>耗时：3h 45m</span>
                                    </div>
                                </td>
                                <td>2025-04-01 09:30</td>
                                <td class="task-actions-cell">
                                    <button class="task-action pause" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="task-action stop" title="终止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="task-name">城市交通系统性能仿真度测评</span>
                                    <div class="task-desc">评估交通模拟系统与实际交通流量对比分析</div>
                                </td>
                                <td>交通系统测评场景</td>
                                <td><span class="task-status-badge status-completed">执行成功</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 100%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：100%</span>
                                        <span>耗时：5h 20m</span>
                                    </div>
                                </td>
                                <td>2025-04-01 10:15</td>
                                <td class="task-actions-cell">
                                    <button class="task-action results" title="查看结果">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                    <button class="task-action play" title="重新测评">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="task-name">城市级关键基础设施级联故障仿真度测评</span>
                                    <div class="task-desc">评估电力-通信-交通行业间级联故障传播的仿真精确度</div>
                                </td>
                                <td>通信-交通级联故障测评场景</td>
                                <td><span class="task-status-badge status-completed">执行成功</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 100%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：100%</span>
                                        <span>耗时：8h 15m</span>
                                    </div>
                                </td>
                                <td>2025-04-07 14:30</td>
                                <td class="task-actions-cell">
                                    <button class="task-action results" title="查看结果">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                    <button class="task-action play" title="重新测评">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>                            
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">城市供水系统安全仿真度测评</span>
                                    <div class="task-desc">评估供水系统仿真环境安全措施与实际系统对比</div>
                                </td>
                                <td>供水系统测评场景</td>
                                <td><span class="task-status-badge status-paused">已暂停</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar warning" style="width: 42%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：42%</span>
                                        <span>耗时：2h 10m</span>
                                    </div>
                                </td>
                                <td>2025-03-30 16:20</td>
                                <td class="task-actions-cell">
                                    <button class="task-action play" title="继续">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="task-action stop" title="终止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">城市燃气系统可靠性仿真度测评</span>
                                    <div class="task-desc">评估燃气仿真系统与实际系统故障模式对比</div>
                                </td>
                                <td>燃气系统测评场景</td>
                                <td><span class="task-status-badge status-failed">执行失败</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar danger" style="width: 23%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：23%</span>
                                        <span>耗时：1h 05m</span>
                                    </div>
                                </td>
                                <td>2025-04-02 11:45</td>
                                <td class="task-actions-cell">
                                    <button class="task-action play" title="重试">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">城市通信网络综合仿真度测评</span>
                                    <div class="task-desc">评估通信网络仿真模型完整性与实际网络对比</div>
                                </td>
                                <td>通信系统测评场景</td>
                                <td><span class="task-status-badge status-pending">运行中</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar info" style="width: 0%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>等待资源分配</span>
                                        <span>预计开始：16:30</span>
                                    </div>
                                </td>
                                <td>2025-04-03 08:15</td>
                                <td class="task-actions-cell">
                                    <button class="task-action play" title="立即启动">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">电力系统异常场景响应测评</span>
                                    <div class="task-desc">评估电力仿真系统对各类异常场景的响应能力</div>
                                </td>
                                <td>电力系统测评场景</td>
                                <td><span class="task-status-badge status-completed">执行成功</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 100%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：100%</span>
                                        <span>耗时：6h 35m</span>
                                    </div>
                                </td>
                                <td>2025-04-02 22:10</td>
                                <td class="task-actions-cell">
                                    <button class="task-action results" title="查看结果">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                    <button class="task-action play" title="重新测评">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">交通信号控制系统仿真度测评</span>
                                    <div class="task-desc">评估交通信号控制仿真模型与实际系统精确度</div>
                                </td>
                                <td>交通系统测评场景</td>
                                <td><span class="task-status-badge status-running">运行中</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 15%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：15%</span>
                                        <span>耗时：1h 20m</span>
                                    </div>
                                </td>
                                <td>2025-04-03 07:45</td>
                                <td class="task-actions-cell">
                                    <button class="task-action pause" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="task-action stop" title="终止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-footer">
                        <div class="page-info">
                            显示 1 到 7 条，共 128 条记录
                        </div>
                        <div class="pagination">
                            <button class="page-btn disabled">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button class="page-btn disabled">
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">19</button>
                            <button class="page-btn">
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button class="page-btn">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 卡片视图 -->
                <div class="card-view" id="card-view">
                    <!-- 卡片1 -->
                    <div class="task-card">
                        <div class="task-card-header">
                            <div>
                                <button class="favorite-action active">
                                    <i class="fas fa-star star-icon star-active"></i>
                                </button>
                                <span class="task-card-title">城市电力系统功能仿真度测评</span>
                            </div>
                            <span class="task-status-badge status-running">运行中</span>
                        </div>
                        <div class="task-card-id">EVAL-20250401-001</div>
                        <div class="task-card-desc">评估城市电力系统功能性参数与实际运行对比</div>
                        <div class="task-card-info">
                            <div>
                                <span class="task-card-info-label">评估场景:</span>
                                <span>企业电力供电场景</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">评估类型:</span>
                                <span>电力行业仿真度评估</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">创建时间:</span>
                                <span>2025-04-01 09:30</span>
                            </div>
                        </div>
                        <div class="task-card-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar success" style="width: 68%"></div>
                            </div>
                            <div class="progress-text">
                                <span>已完成：68%</span>
                                <span>耗时：3h 45m</span>
                            </div>
                        </div>
                        <div class="task-card-actions">
                            <button class="task-action pause" title="暂停">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button class="task-action stop" title="终止">
                                <i class="fas fa-stop"></i>
                            </button>
                            <button class="task-action logs" title="查看日志">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="task-action edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-action delete" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 卡片2 -->
                    <div class="task-card">
                        <div class="task-card-header">
                            <div>
                                <button class="favorite-action active">
                                    <i class="fas fa-star star-icon star-active"></i>
                                </button>
                                <span class="task-card-title">城市交通系统性能仿真度测评</span>
                            </div>
                            <span class="task-status-badge status-completed">执行成功</span>
                        </div>
                        <div class="task-card-id">EVAL-20250402-002</div>
                        <div class="task-card-desc">评估交通模拟系统与实际交通流量对比分析</div>
                        <div class="task-card-info">
                            <div>
                                <span class="task-card-info-label">评估场景:</span>
                                <span>交通系统测评场景</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">评估类型:</span>
                                <span>交通行业仿真度评估</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">创建时间:</span>
                                <span>2025-04-01 10:15</span>
                            </div>
                        </div>
                        <div class="task-card-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar success" style="width: 100%"></div>
                            </div>
                            <div class="progress-text">
                                <span>已完成：100%</span>
                                <span>耗时：5h 20m</span>
                            </div>
                        </div>
                        <div class="task-card-actions">
                            <button class="task-action results" title="查看结果">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                            <button class="task-action play" title="重新测评">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button class="task-action logs" title="查看日志">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="task-action edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-action delete" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 卡片3 -->
                    <div class="task-card">
                        <div class="task-card-header">
                            <div>
                                <button class="favorite-action active">
                                    <i class="fas fa-star star-icon star-active"></i>
                                </button>
                                <span class="task-card-title">城市级关键基础设施级联故障仿真度测评</span>
                            </div>
                            <span class="task-status-badge status-completed">执行成功</span>
                        </div>
                        <div class="task-card-id">EVAL-20250407-002</div>
                        <div class="task-card-desc">评估电力-通信-交通行业间级联故障传播的仿真精确度</div>
                        <div class="task-card-info">
                            <div>
                                <span class="task-card-info-label">评估场景:</span>
                                <span>通信-交通级联故障测评场景</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">评估类型:</span>
                                <span>跨行业级联风险仿真度评估</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">创建时间:</span>
                                <span>2025-04-07 14:30</span>
                            </div>
                        </div>
                        <div class="task-card-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar success" style="width: 100%"></div>
                            </div>
                            <div class="progress-text">
                                <span>已完成：100%</span>
                                <span>耗时：8h 15m</span>
                            </div>
                        </div>
                        <div class="task-card-actions">
                            <button class="task-action results" title="查看结果">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                            <button class="task-action play" title="重新测评">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button class="task-action logs" title="查看日志">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="task-action edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-action delete" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 卡片4 -->
                    <div class="task-card">
                        <div class="task-card-header">
                            <div>
                                <button class="favorite-action">
                                    <i class="fas fa-star star-icon"></i>
                                </button>
                                <span class="task-card-title">城市供水系统安全仿真度测评</span>
                            </div>
                            <span class="task-status-badge status-paused">已暂停</span>
                        </div>
                        <div class="task-card-id">EVAL-20250403-003</div>
                        <div class="task-card-desc">评估供水系统仿真环境安全措施与实际系统对比</div>
                        <div class="task-card-info">
                            <div>
                                <span class="task-card-info-label">评估场景:</span>
                                <span>供水系统测评场景</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">评估类型:</span>
                                <span>水利行业仿真度评估</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">创建时间:</span>
                                <span>2025-03-30 16:20</span>
                            </div>
                        </div>
                        <div class="task-card-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar warning" style="width: 42%"></div>
                            </div>
                            <div class="progress-text">
                                <span>已完成：42%</span>
                                <span>耗时：2h 10m</span>
                            </div>
                        </div>
                        <div class="task-card-actions">
                            <button class="task-action play" title="继续">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="task-action stop" title="终止">
                                <i class="fas fa-stop"></i>
                            </button>
                            <button class="task-action logs" title="查看日志">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="task-action edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-action delete" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 更多卡片... -->
                    
                    <!-- 卡片5 -->
                    <div class="task-card">
                        <div class="task-card-header">
                            <div>
                                <button class="favorite-action">
                                    <i class="fas fa-star star-icon"></i>
                                </button>
                                <span class="task-card-title">城市燃气系统可靠性仿真度测评</span>
                            </div>
                            <span class="task-status-badge status-failed">执行失败</span>
                        </div>
                        <div class="task-card-id">EVAL-20250403-004</div>
                        <div class="task-card-desc">评估燃气仿真系统与实际系统故障模式对比</div>
                        <div class="task-card-info">
                            <div>
                                <span class="task-card-info-label">评估场景:</span>
                                <span>燃气系统测评场景</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">评估类型:</span>
                                <span>燃气行业仿真度评估</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">创建时间:</span>
                                <span>2025-04-02 11:45</span>
                            </div>
                        </div>
                        <div class="task-card-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar danger" style="width: 23%"></div>
                            </div>
                            <div class="progress-text">
                                <span>已完成：23%</span>
                                <span>耗时：1h 05m</span>
                            </div>
                        </div>
                        <div class="task-card-actions">
                            <button class="task-action play" title="重试">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button class="task-action logs" title="查看日志">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="task-action edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-action delete" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 卡片6 -->
                    <div class="task-card">
                        <div class="task-card-header">
                            <div>
                                <button class="favorite-action">
                                    <i class="fas fa-star star-icon"></i>
                                </button>
                                <span class="task-card-title">城市通信网络综合仿真度测评</span>
                            </div>
                            <span class="task-status-badge status-pending">运行中</span>
                        </div>
                        <div class="task-card-id">EVAL-20250403-005</div>
                        <div class="task-card-desc">评估通信网络仿真模型完整性与实际网络对比</div>
                        <div class="task-card-info">
                            <div>
                                <span class="task-card-info-label">评估场景:</span>
                                <span>通信系统测评场景</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">评估类型:</span>
                                <span>通信行业仿真度评估</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">创建时间:</span>
                                <span>2025-04-03 08:15</span>
                            </div>
                        </div>
                        <div class="task-card-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar info" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">
                                <span>等待资源分配</span>
                                <span>预计开始：16:30</span>
                            </div>
                        </div>
                        <div class="task-card-actions">
                            <button class="task-action play" title="立即启动">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="task-action edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-action delete" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 卡片7 -->
                    <div class="task-card">
                        <div class="task-card-header">
                            <div>
                                <button class="favorite-action">
                                    <i class="fas fa-star star-icon"></i>
                                </button>
                                <span class="task-card-title">交通信号控制系统仿真度测评</span>
                            </div>
                            <span class="task-status-badge status-running">运行中</span>
                        </div>
                        <div class="task-card-id">EVAL-20250404-007</div>
                        <div class="task-card-desc">评估交通信号控制仿真模型与实际系统精确度</div>
                        <div class="task-card-info">
                            <div>
                                <span class="task-card-info-label">评估场景:</span>
                                <span>交通系统测评场景</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">评估类型:</span>
                                <span>交通行业仿真度评估</span>
                            </div>
                            <div>
                                <span class="task-card-info-label">创建时间:</span>
                                <span>2025-04-03 07:45</span>
                            </div>
                        </div>
                        <div class="task-card-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar success" style="width: 15%"></div>
                            </div>
                            <div class="progress-text">
                                <span>已完成：15%</span>
                                <span>耗时：1h 20m</span>
                            </div>
                        </div>
                        <div class="task-card-actions">
                            <button class="task-action pause" title="暂停">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button class="task-action stop" title="终止">
                                <i class="fas fa-stop"></i>
                            </button>
                            <button class="task-action logs" title="查看日志">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="task-action edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-action delete" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
	<!-- 日志查看弹窗 -->
    <div class="modal-backdrop mlog-modal" id="mlog-modal">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">评估任务执行日志</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="mlog-header">
                    <div class="mlog-filter">
                        <div class="mlog-level active" data-level="all">全部</div>
                        <div class="mlog-level" data-level="info">信息</div>
                        <div class="mlog-level" data-level="warning">警告</div>
                        <div class="mlog-level" data-level="error">错误</div>
                        <div class="mlog-level" data-level="debug">调试</div>
                    </div>
                    <div class="mlog-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索日志内容..." id="mlog-search-input">
                    </div>
                </div>
                <div class="mlog-content">
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 09:45:26</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">任务启动: 开始执行城市电力系统功能仿真度测评</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 09:45:28</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">正在加载测评模版: 电力系统测评场景 v2.4</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 09:45:30</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">模版加载成功，开始连接仿真系统</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 09:46:15</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">正在执行测试用例集：基础功能测试（1/5）</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 09:52:42</div>
                        <div class="mlog-level-indicator mlog-debug">DEBUG</div>
                        <div class="mlog-message">测试用例 #12 执行完成，测评指标匹配度: 93.5%</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 09:55:33</div>
                        <div class="mlog-level-indicator mlog-warning">WARN</div>
                        <div class="mlog-message">负载均衡响应测试：仿真系统响应时间(142ms)略高于实际系统(125ms)</div>
                    </div>
                    <div class="mlog-line error">
                        <div class="mlog-time">2025-04-03 09:58:19</div>
                        <div class="mlog-level-indicator mlog-error">ERROR</div>
                        <div class="mlog-message">故障切换测试失败：仿真系统切换时间(3.2s)远高于实际系统(1.5s)</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 09:58:25</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">记录故障切换异常，继续执行下一测试用例</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 09:58:40</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">开始执行测试用例集：高级功能测试（2/5）</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 10:01:12</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">已完成 24 个测试用例，测评进度: 32%</div>
                    </div>
                    <div class="mlog-line error">
                        <div class="mlog-time">2025-04-03 10:03:45</div>
                        <div class="mlog-level-indicator mlog-error">ERROR</div>
                        <div class="mlog-message">数据校验错误: 变电站负载模拟值与实际记录值偏差超过15%</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 10:03:48</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">记录偏差数据，继续测试下一指标</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 10:04:55</div>
                        <div class="mlog-level-indicator mlog-debug">DEBUG</div>
                        <div class="mlog-message">当前阶段测评结果汇总: 功能匹配度87.3%, 参数精度82.5%</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="action-btn" id="export-log">
                    <i class="fas fa-download"></i> 导出日志
                </button>
                <button class="action-btn primary" id="close-mlog-modal">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 新建仿真度评估任务弹窗 -->
    <div class="modal" id="newEvalTaskModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新建仿真度评估任务</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 添加任务名称输入框 -->
                <div class="form-group">
                    <label for="taskName">任务名称</label>
                    <input type="text" id="taskName" class="form-select" placeholder="请输入任务名称">
                </div>
                
                <!-- 新增评估对象选择 -->
                <div class="form-group">
                    <label>选择评估场景</label>
                    <select class="form-select" id="assessmentSceneSelect">
                        <option value="">请选评估场景</option>
                        <option value="power">中小企业网场景</option>
                        <option value="transport">轨道交通场景</option>
                        <option value="communication">危险气体缓冲工艺场景</option>
                        <option value="water">通信-电力级联风险场景</option>
                        <option value="gas">通信-交通级联风险场景</option>
                        <option value="power-transport">电力-交通级联风险场景</option>
                    </select>
                </div>
                <!-- 新增：评估场景拓扑下拉+上传 -->
                <div class="form-group" style="display: flex; align-items: center; gap: 10px;">
                    <div style="flex:1;">
                        <label for="topologySelect">评估场景拓扑</label>
                        <select id="topologySelect" class="form-select">
                            <option value="">请选择拓扑文件</option>
                        </select>
                    </div>
                    <div>
                        <input type="file" id="topologyFile" class="form-select" accept=".json,.xml" style="display:none;">
                        <button type="button" id="topologyUploadBtn" class="btn btn-secondary" style="margin-top:24px;">上传</button>
                    </div>
                </div>
                <!-- 新增：评估场景攻击路径下拉+上传 -->
                <div class="form-group" style="display: flex; align-items: center; gap: 10px;">
                    <div style="flex:1;">
                        <label for="attackPathSelect">评估场景攻击路径</label>
                        <select id="attackPathSelect" class="form-select">
                            <option value="">请选择攻击路径文件</option>
                        </select>
                    </div>
                    <div>
                        <input type="file" id="attackPathFile" class="form-select" accept=".json,.xml" style="display:none;">
                        <button type="button" id="attackPathUploadBtn" class="btn btn-secondary" style="margin-top:24px;">上传</button>
                    </div>
                </div>

                <div class="form-group">
                    <label>选择创建方式</label>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="createType" value="template" checked>
                            <span>从模版创建</span>
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="createType" value="empty">
                            <span>创建空的评估任务</span>
                        </label>
                    </div>
                </div>
                <!-- 新增：自动匹配模版按钮 -->
                <div class="form-group" id="autoMatchGroup" style="display: flex; align-items: center; gap: 10px; margin-bottom: 0;">
                    <input type="checkbox" id="autoMatchTemplate" style="width: 18px; height: 18px;">
                    <label for="autoMatchTemplate" style="margin-bottom: 0; cursor: pointer;">自动匹配模版</label>
                    <input type="text" id="recommendedTemplate" class="form-select" style="display:none; flex:1;" readonly placeholder="推荐模版将在此显示">
                </div>
                <div class="form-group template-select" id="templateSelectGroup">
                    <label for="templateType">选择模版</label>
                    <select class="form-select" id="templateType">
                        <option value="">请选择模版</option>
                        <option value="template1">通信-交通级联风险仿真评估模版</option>
                        <option value="template2">电力系统稳态仿真评估模版</option>
                        <option value="template3">交通流量模拟评估模版</option>
                        <option value="template4">供水系统压力仿真评估模版</option>
                        <option value="template5">燃气管网压力监测模版</option>
                    </select>
                </div>
                
                <!-- 新增仿真基本信息表单字段 -->
                <div class="form-group simulation-info" id="simulationTypeGroup" style="display: none;">
                    <label for="simType">评估任务类型</label>
                    <select id="simType" class="form-select">
                        <option value="">请选择评估任务类型</option>
                        <option value="steady">跨行业级联风险仿真度评估</option>
                        <option value="dynamic">电力行业仿真度评估</option>
                        <option value="monte-carlo">通信行业仿真度评估</option>
                        <option value="scenario">交通行业仿真度评估</option>
                        <option value="cascade">燃气行业仿真度评估</option>
                    </select>
                </div>
                
                <div class="form-group simulation-info" id="simulationDescGroup" style="display: none;">
                    <label for="simDescription">描述信息</label>
                    <textarea id="simDescription" class="form-select" rows="3" placeholder="请输入描述信息"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelBtn">取消</button>
                <button class="btn btn-primary" id="confirmBtn">确定</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 主题切换功能初始化
            const themeSwitch = document.querySelector('.theme-switch');
            const htmlRoot = document.documentElement;
            
            // 检查本地存储中的主题设置，默认为暗色主题
            const savedTheme = localStorage.getItem('theme');
            // 只有当明确保存了"light"时才应用亮色主题
            if (savedTheme === 'light') {
                htmlRoot.classList.add('light-theme');
            } else {
                // 如果没有保存或者保存的不是"light"，则确保是暗色主题
                localStorage.setItem('theme', 'dark');
            }
            
            // 切换主题的函数
            function toggleTheme() {
                if (htmlRoot.classList.contains('light-theme')) {
                    htmlRoot.classList.remove('light-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    htmlRoot.classList.add('light-theme');
                    localStorage.setItem('theme', 'light');
                }
                
                // 如果有图表，主题切换后需要重新渲染
                if (typeof echarts !== 'undefined') {
                    // 延迟一点执行，等待CSS变化应用
                    setTimeout(() => {
                        // 找到所有echarts实例并重新调整大小
                        const chartElements = document.querySelectorAll('[id$="-chart"]');
                        chartElements.forEach(element => {
                            const chart = echarts.getInstanceByDom(element);
                            if (chart) {
                                chart.resize();
                            }
                        });
                    }, 200);
                }
            }
            
            // 为主题切换按钮添加点击事件
            themeSwitch.addEventListener('click', toggleTheme);
            
            // 模拟通知弹出
            document.querySelector('.notification-icon').addEventListener('click', function() {
                alert('通知中心功能将在此处展开');
            });

            // 用户资料弹出
            document.querySelector('.user-profile').addEventListener('click', function() {
                alert('用户个人资料功能将在此处展开');
            });
        
            // 高级筛选切换
            const advancedFilterToggle = document.querySelector('.advanced-filter-toggle');
            const advancedFilterRow = document.querySelector('.advanced-filter-row');
            const filterActions = document.querySelector('.filter-actions');
            
            advancedFilterRow.style.display = 'none';
            filterActions.style.display = 'none';
            
            advancedFilterToggle.addEventListener('click', function() {
                if (advancedFilterRow.style.display === 'none') {
                    advancedFilterRow.style.display = 'grid';
                    filterActions.style.display = 'flex';
                    this.innerHTML = '<i class="fas fa-chevron-up"></i> 收起筛选';
                } else {
                    advancedFilterRow.style.display = 'none';
                    filterActions.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-sliders-h"></i> 高级筛选';
                }
            });

            // 视图切换功能
            const tableViewBtn = document.getElementById('table-view-btn');
            const cardViewBtn = document.getElementById('card-view-btn');
            const tableView = document.querySelector('.task-table');
            const tableFooter = document.querySelector('.table-footer');
            const cardView = document.getElementById('card-view');
            
            // 表格视图按钮点击事件
            tableViewBtn.addEventListener('click', function() {
                // 激活表格视图按钮
                tableViewBtn.classList.add('active');
                cardViewBtn.classList.remove('active');
                
                // 显示表格，隐藏卡片
                tableView.style.display = 'table';
                tableFooter.style.display = 'flex';
                cardView.style.display = 'none';
            });
            
            // 卡片视图按钮点击事件
            cardViewBtn.addEventListener('click', function() {
                // 激活卡片视图按钮
                cardViewBtn.classList.add('active');
                tableViewBtn.classList.remove('active');
                
                // 显示卡片，隐藏表格
                cardView.style.display = 'grid';
                tableView.style.display = 'none';
                tableFooter.style.display = 'none';
            });

            // 全选功能
            const selectAllCheckbox = document.querySelector('#select-all');
            const checkboxes = document.querySelectorAll('.task-table tbody .custom-checkbox input');
            
            selectAllCheckbox.addEventListener('change', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
            
            // 收藏功能
            const favoriteButtons = document.querySelectorAll('.favorite-action');
            
            favoriteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const starIcon = this.querySelector('.star-icon');
                    if (starIcon.classList.contains('star-active')) {
                        starIcon.classList.remove('star-active');
                        this.classList.remove('active');
                    } else {
                        starIcon.classList.add('star-active');
                        this.classList.add('active');
                    }
                });
            });
            
            // 表格操作按钮交互
            const actionButtons = document.querySelectorAll('.task-action');
            
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const action = this.classList.contains('play') ? '启动' :
                                this.classList.contains('pause') ? '暂停' :
                                this.classList.contains('stop') ? '终止' :
                                this.classList.contains('logs') ? '查看日志' :
                                this.classList.contains('edit') ? '编辑' : 
                                this.classList.contains('results') ? '查看结果' : '删除';
                                
                    // 获取任务名称 - 根据按钮所在的视图获取
                    let taskName;
                    if (this.closest('.task-card')) {
                        taskName = this.closest('.task-card').querySelector('.task-card-title').textContent;
                    } else {
                        taskName = this.closest('tr').querySelector('.task-name').textContent;
                    }
                    
                    if (action === '删除') {
                        if (confirm(`确定要删除评估任务"${taskName}"吗？此操作不可撤销。`)) {
                            alert(`评估任务"${taskName}"已删除`);
                        }
                    } else if (action === '终止') {
                        if (confirm(`确定要终止评估任务"${taskName}"吗？任务将无法继续执行。`)) {
                            alert(`评估任务"${taskName}"已终止`);
                        }
                    } else if (action === '查看日志') {
                        showLogModal();
                    } else if (action === '查看结果') {
                        alert(`正在加载"${taskName}"的测评结果报告`);
                    } else {
                        alert(`正在${action}评估任务: ${taskName}`);
                    }
                });
            });
            
            // 主要操作按钮交互
            const mainActionButtons = document.querySelectorAll('.task-actions .action-btn');
            
            mainActionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.innerHTML.includes('导入任务')) {
                        alert('打开评估任务导入对话框');
                    } else if (this.innerHTML.includes('导出任务')) {
                        alert('导出选中的评估任务结果');
                    } else if (this.innerHTML.includes('新建仿真度评估任务')) {
                        // 打开新建仿真度评估任务弹窗
                        document.getElementById('newEvalTaskModal').style.display = 'flex';
                    }
                });
            });
            
            // 分页功能
            const pageButtons = document.querySelectorAll('.pagination .page-btn');
            
            pageButtons.forEach(button => {
                if (!button.classList.contains('disabled') && !button.classList.contains('active')) {
                    button.addEventListener('click', function() {
                        pageButtons.forEach(btn => btn.classList.remove('active'));
                        this.classList.add('active');
                        
                        if (this.textContent.trim() !== '...') {
                            alert(`切换到第 ${this.textContent} 页`);
                        }
                    });
                }
            });

            // 批量操作按钮交互
            const bulkActionButtons = document.querySelectorAll('.bulk-action-btn');
            
            bulkActionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const action = this.innerHTML.includes('批量启动') ? '启动' :
                                this.innerHTML.includes('批量暂停') ? '暂停' :
                                this.innerHTML.includes('批量终止') ? '终止' : '删除';
                    
                    let checkedCount = 0;
                    checkboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            checkedCount++;
                        }
                    });
                    
                    if (checkedCount === 0) {
                        alert(`请先选择要${action}的评估任务`);
                    } else {
                        if ((action === '删除' || action === '终止') && 
                            !confirm(`确定要${action}选中的 ${checkedCount} 个评估任务吗？此操作可能无法撤销。`)) {
                            return;
                        }
                        alert(`已${action}选中的 ${checkedCount} 个评估任务`);
                    }
                });
            });
            
            // 显示日志弹窗
            const showLogModal = () => {
                document.getElementById('mlog-modal').style.display = 'flex';
                
                // 日志过滤功能
                const logLevels = document.querySelectorAll('.mlog-level');
                const logLines = document.querySelectorAll('.mlog-line');
                
                logLevels.forEach(level => {
                    level.addEventListener('click', function() {
                        // 移除其他活动状态
                        logLevels.forEach(l => l.classList.remove('active'));
                        this.classList.add('active');
                        
                        const filterLevel = this.getAttribute('data-level');
                        
                        logLines.forEach(line => {
                            if (filterLevel === 'all') {
                                line.style.display = '';
                            } else {
                                const lineLevel = line.querySelector('.mlog-level-indicator').classList[1].split('-')[1];
                                line.style.display = lineLevel === filterLevel ? '' : 'none';
                            }
                        });
                    });
                });
                
                // 日志搜索功能
                const logSearchInput = document.getElementById('mlog-search-input');
                
                logSearchInput.addEventListener('input', function() {
                    const searchText = this.value.toLowerCase();
                    
                    logLines.forEach(line => {
                        const message = line.querySelector('.mlog-message').textContent.toLowerCase();
                        if (searchText === '' || message.includes(searchText)) {
                            line.style.display = '';
                        } else {
                            line.style.display = 'none';
                        }
                    });
                });
            };
            
            // 关闭日志弹窗
            document.querySelectorAll('#close-mlog-modal, .mlog-modal .modal-close').forEach(button => {
                button.addEventListener('click', function() {
                    document.getElementById('mlog-modal').style.display = 'none';
                });
            });

            // 新建评估任务弹窗交互
            const newEvalTaskModal = document.getElementById('newEvalTaskModal');
            const newEvalTaskBtn = document.getElementById('newEvalTaskBtn');
            const createTypeRadios = document.querySelectorAll('input[name="createType"]');
            const templateSelectGroup = document.getElementById('templateSelectGroup');
            const confirmBtn = document.getElementById('confirmBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            const closeBtn = document.querySelector('.close-btn');

            // 打开弹窗
            newEvalTaskBtn.addEventListener('click', function() {
                newEvalTaskModal.style.display = 'flex';
            });

            // 关闭弹窗
            function closeModal() {
                newEvalTaskModal.style.display = 'none';
            }

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            // 点击弹窗外部关闭
            newEvalTaskModal.addEventListener('click', function(e) {
                if (e.target === newEvalTaskModal) {
                    closeModal();
                }
            });

            // 创建方式切换
            createTypeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'empty') {
                        templateSelectGroup.style.display = 'none';
                        document.querySelectorAll('.simulation-info').forEach(el => {
                            el.style.display = 'block';
                        });
                        // 隐藏自动匹配模版
                        document.getElementById('autoMatchGroup').style.display = 'none';
                    } else {
                        templateSelectGroup.style.display = 'block';
                        document.querySelectorAll('.simulation-info').forEach(el => {
                            el.style.display = 'none';
                        });
                        // 显示自动匹配模版
                        document.getElementById('autoMatchGroup').style.display = 'flex';
                    }
                });
            });
            // 初始化时只在从模版创建下显示自动匹配模版
            if(document.querySelector('input[name="createType"]:checked').value === 'template'){
                document.getElementById('autoMatchGroup').style.display = 'flex';
            }else{
                document.getElementById('autoMatchGroup').style.display = 'none';
            }

            // 自动匹配模版逻辑
            const autoMatchCheckbox = document.getElementById('autoMatchTemplate');
            const recommendedTemplateInput = document.getElementById('recommendedTemplate');
            const templateTypeSelect = document.getElementById('templateType');

            autoMatchCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // 禁用下拉框，显示推荐模版
                    templateTypeSelect.disabled = true;
                    recommendedTemplateInput.style.display = 'block';
                    // 简单推荐逻辑：根据评估场景推荐模版
                    const scene = document.getElementById('assessmentSceneSelect').value;
                    let recommend = '';
                    switch(scene) {
                        case 'power': recommend = '电力系统稳态仿真评估模版'; break;
                        case 'transport': recommend = '交通流量模拟评估模版'; break;
                        case 'communication': recommend = '供水系统压力仿真评估模版'; break;
                        case 'water': recommend = '通信-电力级联风险仿真评估模版'; break;
                        case 'gas': recommend = '通信-交通级联风险仿真评估模版'; break;
                        case 'power-transport': recommend = '燃气管网压力监测模版'; break;
                        default: recommend = '未匹配到推荐模版';
                    }
                    recommendedTemplateInput.value = recommend;
                } else {
                    // 启用下拉框，隐藏推荐模版
                    templateTypeSelect.disabled = false;
                    recommendedTemplateInput.style.display = 'none';
                }
            });
            // 当评估场景变更时，若已勾选自动匹配，则自动更新推荐模版
            document.getElementById('assessmentSceneSelect').addEventListener('change', function() {
                if (autoMatchCheckbox.checked) {
                    autoMatchCheckbox.dispatchEvent(new Event('change'));
                }
            });

            // 确认按钮点击事件
            confirmBtn.addEventListener('click', function() {
                const selectedScene = document.getElementById('assessmentSceneSelect').value;
                if (!selectedScene) {
                    alert('请选择评估场景');
                    return;
                }

                const selectedType = document.querySelector('input[name="createType"]:checked').value;
                
                if (selectedType === 'empty') {
                    // 创建空的评估任务
                    const selectedSimType = document.getElementById('simType').value;
                    if (!selectedSimType) {
                        alert('请选择评估任务类型');
                        return;
                    }
                    // 根据模版选择跳转
                    if (selectedSimType === 'steady') {
                        window.location.href = '8.InterIndustryRiskEvaluation.html' ;
                    } else {
                        window.location.href = '7.IndustryEvaluation.html';
                    }
                    closeModal();
                } else {
                    // 从模版创建
                    const selectedTemplate = document.getElementById('templateType').value;
                    if (!selectedTemplate) {
                        alert('请选择模版');
                        return;
                    }
                    
                    // 根据模版选择跳转
                    if (selectedTemplate === 'template1') {
                        window.location.href = '8.InterIndustryRiskEvaluation.html';
                    } else {
                        window.location.href = '7.IndustryEvaluation.html';
                    }
                    closeModal();
                }
            });

            // 新增：评估场景与拓扑/攻击路径联动逻辑
            const sceneTopologyMap = {
                power: ['power_topo1.json', 'power_topo2.xml'],
                transport: ['transport_topo1.json'],
                communication: [],
                water: ['water_topo1.json'],
                gas: [],
                'power-transport': []
            };
            const sceneAttackPathMap = {
                power: ['power_attack1.json'],
                transport: [],
                communication: ['comm_attack1.xml'],
                water: [],
                gas: [],
                'power-transport': []
            };
            const assessmentSceneSelect = document.getElementById('assessmentSceneSelect');
            const topologySelect = document.getElementById('topologySelect');
            const topologyFile = document.getElementById('topologyFile');
            const topologyUploadBtn = document.getElementById('topologyUploadBtn');
            const attackPathSelect = document.getElementById('attackPathSelect');
            const attackPathFile = document.getElementById('attackPathFile');
            const attackPathUploadBtn = document.getElementById('attackPathUploadBtn');

            function updateSelectOptions(select, options) {
                select.innerHTML = '<option value="">请选择</option>';
                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt;
                    option.textContent = opt;
                    select.appendChild(option);
                });
            }

            assessmentSceneSelect.addEventListener('change', function() {
                const scene = this.value;
                // 拓扑
                const topoOptions = sceneTopologyMap[scene] || [];
                updateSelectOptions(topologySelect, topoOptions);
                if (topoOptions.length > 0) {
                    topologySelect.style.display = '';
                    topologyUploadBtn.style.display = 'none';
                    topologyFile.style.display = 'none';
                    topologySelect.selectedIndex = 1; // 自动选第一个
                } else {
                    topologySelect.style.display = 'none';
                    topologyUploadBtn.style.display = '';
                }
                // 攻击路径
                const attackOptions = sceneAttackPathMap[scene] || [];
                updateSelectOptions(attackPathSelect, attackOptions);
                if (attackOptions.length > 0) {
                    attackPathSelect.style.display = '';
                    attackPathUploadBtn.style.display = 'none';
                    attackPathFile.style.display = 'none';
                    attackPathSelect.selectedIndex = 1;
                } else {
                    attackPathSelect.style.display = 'none';
                    attackPathUploadBtn.style.display = '';
                }
            });
            // 上传按钮点击，触发文件选择
            topologyUploadBtn.addEventListener('click', () => topologyFile.click());
            attackPathUploadBtn.addEventListener('click', () => attackPathFile.click());
            // 上传后可在下拉框中显示"已上传：xxx.json"
            topologyFile.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const fileName = this.files[0].name;
                    updateSelectOptions(topologySelect, [
                        '已上传：' + fileName
                    ]);
                    topologySelect.style.display = '';
                    topologySelect.selectedIndex = 1;
                    topologyUploadBtn.style.display = 'none';
                }
            });
            attackPathFile.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const fileName = this.files[0].name;
                    updateSelectOptions(attackPathSelect, [
                        '已上传：' + fileName
                    ]);
                    attackPathSelect.style.display = '';
                    attackPathSelect.selectedIndex = 1;
                    attackPathUploadBtn.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
