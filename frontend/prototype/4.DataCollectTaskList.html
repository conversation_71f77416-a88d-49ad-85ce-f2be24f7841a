<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据采集任务列表 - 城市级关基级联的网络仿真度评估系统</title>
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Echarts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* 页面整体放大120% */
        html {
            zoom: 110%;
        }

        :root {
            --primary-color: #0096FF;
            --secondary-color: #00E0FF;
            --background-dark: #0F1520;
            --background-card: #1A202E;
            --text-color: #E0E6F0;
            --text-secondary: #A0A8B8;
            --border-color: #2A3142;
            --highlight-color: #3E9BFF;
            --success-color: #00C48C;
            --warning-color: #FFB946;
            --danger-color: #F25767;
            --info-color: #0095FF;
        }
        
        /* 亮色主题变量 */
        :root.light-theme {
            --primary-color: #0078D4;
            --secondary-color: #00A2E0;
            --background-dark: #FFFFFF;
            --background-card: #FFFFFF;
            --text-color: #333333;
            --text-secondary: #666666;
            --border-color: #E0E6ED;
            --highlight-color: #106EBE;
            --success-color: #0A9D5A;
            --warning-color: #F29D41;
            --danger-color: #E74C3C;
            --info-color: #0078D4;
        }

        /* Logo样式 */
        .logo {
	zoom: 110%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 10px;
        }

        .logo-icon {
            font-size: 24px;
            color: var(--primary-color);
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 2px rgba(0, 224, 255, 0.4));
        }

        .logo span {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 0.5px;
            background: linear-gradient(90deg, var(--text-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: var(--background-dark);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            min-height: 100vh;
        }

        /* 顶部导航栏样式 */
        .header {
            background-color: rgba(16, 22, 36, 0.95);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        
        /* 亮色主题的导航栏调整 */
        :root.light-theme .header {
            background-color: rgba(245, 247, 250, 0.95);
            border-bottom: 1px solid var(--border-color);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 60px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .nav-item {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .nav-link {
            color: var(--text-color);
            text-decoration: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link.active {
            color: var(--primary-color);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 0 0 6px 6px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        :root.light-theme .dropdown-menu {
            background-color: var(--background-card);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            border-bottom: 1px solid var(--border-color);
        }

        .dropdown-menu {
            list-style-type: none;
            /* 去掉前面的点 */
        }

        .dropdown-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px;
        }

        .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            padding-left: 20px;
        }
        
        :root.light-theme .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }

        .dropdown-icon {
            margin-right: 8px;
        }

        .nav-tools {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        /* 主题切换开关样式 */
        .theme-switch {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 60px;
            height: 30px;
            background: rgba(0, 149, 255, 0.15);
            border-radius: 30px;
            padding: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .theme-switch:hover {
            transform: scale(1.05);
        }
        
        .theme-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-size: 14px;
        }
        
        .theme-switch-slider {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            left: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        :root.light-theme .theme-switch-slider {
            transform: translateX(30px);
        }

        .notification-icon,
        .user-profile {
            position: relative;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .notification-icon:hover,
        .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        :root.light-theme .notification-icon:hover,
        :root.light-theme .user-profile:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-profile img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 24px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
        }

        .page-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .task-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: var(--highlight-color);
        }

        /* 搜索和筛选区域 */
        .filter-section {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
        }

        .search-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .search-input-wrapper {
            position: relative;
            flex: 1;
        }

        .search-input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.08);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .advanced-filter-toggle {
            white-space: nowrap;
            color: var(--primary-color);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 0 10px;
        }

        .advanced-filter-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .filter-select,
        .filter-input {
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-select:focus,
        .filter-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
        }

        /* 统计卡片区域 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 5px 0 0 5px;
        }

        .stat-card.success::before {
            background-color: var(--success-color);
        }

        .stat-card.warning::before {
            background-color: var(--warning-color);
        }

        .stat-card.danger::before {
            background-color: var(--danger-color);
        }

        .stat-card.info::before {
            background-color: var(--info-color);
        }
        
        .stat-card.white::before {
            background-color: #FFFFFF;
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
        }

        .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .stat-card.info .stat-icon {
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--info-color);
        }
        
        .stat-card.white .stat-icon {
            background-color: rgba(255, 255, 255, 0.1);
            color: #FFFFFF;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-description {
            font-size: 13px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-trend-up {
            color: var(--success-color);
        }

        .stat-trend-down {
            color: var(--danger-color);
        }

        /* 任务列表表格 */
        .tasks-section {
            margin-bottom: 24px;
        }

        .table-responsive {
            background-color: var(--background-card);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .table-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .table-title {
            font-size: 16px;
            font-weight: 500;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .bulk-select {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-right: 15px;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
        }

        .bulk-action-btn {
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .bulk-action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .bulk-action-btn.delete {
            color: var(--danger-color);
            border-color: rgba(242, 87, 103, 0.3);
        }

        .bulk-action-btn.delete:hover {
            background-color: rgba(242, 87, 103, 0.1);
            border-color: var(--danger-color);
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
        }

        .task-table th,
        .task-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .task-table th {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 13px;
            background-color: rgba(255, 255, 255, 0.02);
        }

        .task-table tbody tr {
            transition: all 0.2s ease;
        }

        .task-table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .task-table tbody tr:last-child td {
            border-bottom: none;
        }

        .checkbox-cell {
            width: 30px;
        }

        .task-id {
            font-family: monospace;
            color: var(--text-secondary);
        }

        .task-name {
            font-weight: 500;
        }

        .task-desc {
            color: var(--text-secondary);
            font-size: 13px;
            max-width: 250px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .task-status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-running {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .status-paused {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .status-failed {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .status-pending {
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--info-color);
        }

        .status-completed {
            background-color: rgba(160, 168, 184, 0.1);
            color: var(--text-secondary);
        }

        .task-actions-cell {
            text-align: right;
            white-space: nowrap;
        }

        .task-action {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 5px;
        }

        .task-action:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .task-action.play:hover {
            color: var(--success-color);
            border-color: var(--success-color);
            background-color: rgba(0, 196, 140, 0.1);
        }

        .task-action.pause:hover {
            color: var(--warning-color);
            border-color: var(--warning-color);
            background-color: rgba(255, 185, 70, 0.1);
        }

        .task-action.stop:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
            background-color: rgba(242, 87, 103, 0.1);
        }

        .task-action.edit:hover {
            color: var(--info-color);
            border-color: var(--info-color);
            background-color: rgba(0, 149, 255, 0.1);
        }

        .task-action.logs:hover {
            color: var(--text-color);
            border-color: var(--text-color);
            background-color: rgba(224, 230, 240, 0.1);
        }

        .task-action.delete:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
            background-color: rgba(242, 87, 103, 0.1);
        }

        .priority-badge {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .priority-high {
            background-color: var(--danger-color);
        }

        .priority-medium {
            background-color: var(--warning-color);
        }

        .priority-low {
            background-color: var(--success-color);
        }

        .favorite-action {
            color: var(--text-secondary);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .favorite-action:hover,
        .favorite-action.active {
            color: var(--warning-color);
        }

        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
        }

        .page-info {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .pagination {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .page-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .page-btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .page-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 进度条样式 */
        .progress-bar-container {
            width: 100%;
            height: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-bar {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-bar.success {
            background-color: var(--success-color);
        }

        .progress-bar.warning {
            background-color: var(--warning-color);
        }

        .progress-bar.danger {
            background-color: var(--danger-color);
        }

        .progress-bar.info {
            background-color: var(--info-color);
        }

        .progress-text {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            margin-top: 4px;
            gap: 15px; /* 添加间隔 */
        }

        /* 标记/收藏样式 */
        .star-icon {
            transition: all 0.3s ease;
        }

        .star-active {
            color: var(--warning-color);
        }

        /* 自定义复选框 */
        .custom-checkbox {
            position: relative;
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 5px;
            vertical-align: middle;
        }

        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 18px;
            width: 18px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .custom-checkbox input:checked~.checkmark {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 2px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input:checked~.checkmark:after {
            display: block;
        }

        /* 任务执行日志卡片 */
        .task-logs-card {
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .card-header {
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .card-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .card-action:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
        }

        .card-body {
            padding: 20px;
        }

        .log-timeline {
            position: relative;
            padding-left: 24px;
            max-height: 350px;
            overflow-y: auto;
        }

        .log-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 8px;
            height: 100%;
            width: 2px;
            background-color: var(--border-color);
        }

        .log-item {
            position: relative;
            padding-bottom: 20px;
        }

        .log-item:last-child {
            padding-bottom: 0;
        }

        .log-dot {
            position: absolute;
            left: -24px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 3px solid var(--background-card);
            z-index: 1;
        }

        .log-item.success .log-dot {
            background-color: var(--success-color);
        }

        .log-item.warning .log-dot {
            background-color: var(--warning-color);
        }

        .log-item.danger .log-dot {
            background-color: var(--danger-color);
        }

        .log-item.info .log-dot {
            background-color: var(--info-color);
        }

        .log-content {
            padding-bottom: 12px;
            border-bottom: 1px dashed var(--border-color);
        }

        .log-item:last-child .log-content {
            border-bottom: none;
        }

        .log-title {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .log-detail {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
            font-family: monospace;
            background-color: rgba(255, 255, 255, 0.03);
            padding: 6px;
            border-radius: 4px;
            white-space: pre-wrap;
        }

        .log-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 弹窗样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        }

        .modal {
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 600px;
            overflow: hidden;
            animation: modal-in 0.3s ease;
        }

        @keyframes modal-in {
            from {
                transform: translateY(20px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 500;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            color: var(--danger-color);
        }

        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 日志查看弹窗 */
        .mlog-modal .modal-body {
            padding: 0;
        }

        .mlog-header {
            padding: 12px 20px;
            background-color: rgba(0, 0, 0, 0.2);
            display: flex;
            justify-content: space-between;
        }

        .mlog-filter {
            display: flex;
            gap: 10px;
        }

        .mlog-level {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mlog-level.active {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .mlog-content {
            padding: 10px 0;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 13px;
            height: 400px;
            overflow-y: auto;
            background-color: rgba(0, 0, 0, 0.3);
        }

        .mlog-line {
            padding: 4px 20px;
            display: flex;
            gap: 8px;
            border-left: 3px solid transparent;
        }

        .mlog-line:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .mlog-time {
            color: var(--text-secondary);
            min-width: 140px;
        }

        .mlog-level-indicator {
            min-width: 60px;
            text-align: center;
            border-radius: 3px;
            font-weight: 500;
        }

        .mlog-info {
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
        }

        .mlog-warning {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .mlog-error {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .mlog-debug {
            background-color: rgba(151, 96, 255, 0.1);
            color: #9760FF;
        }

        .mlog-message {
            flex: 1;
        }

        .mlog-line.error {
            border-left-color: var(--danger-color);
        }

        .mlog-line.warning {
            border-left-color: var(--warning-color);
        }

        .mlog-search {
            position: relative;
            width: 200px;
        }

        .mlog-search input {
            width: 100%;
            padding: 6px 8px 6px 28px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 12px;
        }

        .mlog-search i {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* 响应式设计 */
        @media screen and (max-width: 1200px) {
            .stats-cards {
                grid-template-columns: repeat(3, 1fr);
            }

            .advanced-filter-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 992px) {
            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 768px) {
            .stats-cards {
                grid-template-columns: 1fr;
            }

            .task-table {
                min-width: 950px;
            }

            .table-responsive {
                overflow-x: auto;
            }

            .advanced-filter-row {
                grid-template-columns: 1fr;
            }

            .bulk-actions {
                display: none;
            }
        }

        @media screen and (max-width: 576px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .task-actions {
                width: 100%;
            }

            .search-row {
                flex-direction: column;
            }
        }

        /* 动画与过渡效果 */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 149, 255, 0.4);
            }

            70% {
                box-shadow: 0 0 0 10px rgba(0, 149, 255, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(0, 149, 255, 0);
            }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 弹窗共用样式扩展 */
        .modal {
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }

        .modal-body {
            flex: 1;
            overflow-y: auto;
        }

        /* 任务编辑弹窗样式 */
        .task-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            overflow-x: auto;
            overflow-y: hidden;
        }

        .form-tab {
            padding: 10px 16px;
            cursor: pointer;
            white-space: nowrap;
            color: var(--text-secondary);
            position: relative;
            transition: all 0.2s ease;
        }

        .form-tab:hover {
            color: var(--text-color);
        }

        .form-tab.active {
            color: var(--primary-color);
        }

        .form-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .form-tab-content {
            display: none;
        }

        .form-tab-content.active {
            display: block;
        }

        .form-section {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-row {
            display: flex;
            gap: 16px;
        }

        .form-group.half {
            flex: 1;
        }

        .form-group.two-thirds {
            flex: 2;
        }

        .form-group.third {
            flex: 1;
        }

        .form-label {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .form-control {
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 149, 255, 0.2);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 80px;
        }

        .form-control.code-editor {
            font-family: monospace;
            white-space: pre;
        }

        .required {
            color: var(--danger-color);
        }

        .form-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .datasource-type-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 16px;
        }

        .datasource-type-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.03);
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
            min-width: 100px;
        }

        .datasource-type-option:hover {
            background-color: rgba(255, 255, 255, 0.06);
        }

        .datasource-type-option.active {
            border-color: var(--primary-color);
            background-color: rgba(0, 149, 255, 0.08);
        }

        .datasource-type-option i {
            font-size: 24px;
            color: var(--text-secondary);
        }

        .datasource-type-option.active i {
            color: var(--primary-color);
        }

        .condition-builder {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .condition-row {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .condition-field,
        .condition-operator {
            flex: 1;
        }

        .condition-value {
            flex: 2;
        }

        .condition-btn {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .condition-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .condition-btn.add:hover {
            color: var(--success-color);
            border-color: var(--success-color);
        }

        .condition-btn.remove:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .mapping-table {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .mapping-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .mapping-table th {
            background-color: rgba(255, 255, 255, 0.03);
            color: var(--text-secondary);
            font-weight: normal;
            font-size: 13px;
            text-align: left;
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .mapping-table td {
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
        }

        .mapping-table tr:last-child td {
            border-bottom: none;
        }

        .action-bar {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }

        .action-btn.small {
            padding: 5px 10px;
            font-size: 12px;
        }

        .action-btn.icon-only {
            width: 30px;
            height: 30px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .full-width {
            width: 100%;
        }

        .toggle-options {
            display: flex;
            gap: 10px;
            margin-bottom: 16px;
        }

        .toggle-option {
            flex: 1;
            padding: 10px;
            text-align: center;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.03);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .toggle-option:hover {
            background-color: rgba(255, 255, 255, 0.06);
        }

        .toggle-option.active {
            border-color: var(--primary-color);
            background-color: rgba(0, 149, 255, 0.08);
            color: var(--primary-color);
        }

        .validation-rules {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-top: 10px;
        }

        .validation-rule {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .validation-rule-header {
            display: flex;
            justify-content: space-between;
            padding: 10px 15px;
            background-color: rgba(255, 255, 255, 0.03);
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
        }

        .rule-actions {
            display: flex;
            gap: 5px;
        }

        .validation-rule-body {
            padding: 15px;
        }

        .custom-switch {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .custom-switch input {
            margin-right: 8px;
        }

        .notification-emails {
            margin-left: 25px;
            margin-bottom: 16px;
        }

        /* 采集进度弹窗样式 */
        .progress-overview {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 20px;
        }

        .progress-summary {
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .progress-stat-item {
            min-width: 100px;
        }

        .stat-label {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 500;
        }

        .progress-percentage {
            font-size: 24px;
            color: var(--primary-color);
        }

        .total-progress-container {
            height: 8px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .total-progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 4px;
        }

        .stage-progress {
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
        }

        .stage-progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stage-title {
            font-weight: 500;
        }

        .stage-refresh {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.1);
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: var(--text-color);
            transition: .4s;
        }

        input:checked+.slider {
            background-color: var(--primary-color);
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }

        .slider.round {
            border-radius: 20px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .stage-progress-items {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .stage-progress-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .stage-progress-item:hover {
            background-color: rgba(255, 255, 255, 0.03);
        }

        .stage-progress-item.active {
            background-color: rgba(0, 149, 255, 0.08);
        }

        .stage-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stage-progress-item.completed .stage-icon {
            background-color: var(--success-color);
            color: white;
        }

        .stage-progress-item.active .stage-icon {
            background-color: var(--primary-color);
            color: white;
        }

        .stage-info {
            flex: 1;
        }

        .stage-name {
            font-weight: 500;
        }

        .stage-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stage-status {
            font-size: 13px;
            font-weight: 500;
        }

        .stage-progress-item.active .stage-status {
            color: var(--primary-color);
        }

        .stage-progress-item.completed .stage-status {
            color: var(--success-color);
        }

        .log-console {
            border: 1px solid var(--border-color);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .log-console-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid var(--border-color);
        }

        .log-title {
            font-weight: 500;
        }

        .log-filter {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .log-level-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 13px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .log-level-btn:hover,
        .log-level-btn.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-color);
        }

        .log-level-btn.all.active {
            color: var(--text-color);
        }

        .log-level-btn.info.active {
            color: var(--primary-color);
        }

        .log-level-btn.warning.active {
            color: var(--warning-color);
        }

        .log-level-btn.error.active {
            color: var(--danger-color);
        }

        .log-level-btn.debug.active {
            color: #9760FF;
        }

        .log-search {
            position: relative;
            width: 180px;
        }

        .log-search input {
            width: 100%;
            padding: 5px 10px 5px 30px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 13px;
        }

        .log-search i {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .log-console-body {
            padding: 10px 0;
            height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 13px;
            background-color: rgba(0, 0, 0, 0.2);
        }

        .log-line {
            padding: 3px 15px;
            display: flex;
            gap: 10px;
            transition: all 0.2s ease;
        }

        .log-line:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .log-time {
            color: var(--text-secondary);
            min-width: 80px;
        }

        .log-level {
            min-width: 60px;
            font-weight: 500;
        }

        .log-line.info .log-level {
            color: var(--primary-color);
        }

        .log-line.warning .log-level {
            color: var(--warning-color);
        }

        .log-line.error .log-level {
            color: var(--danger-color);
        }

        .log-line.debug .log-level {
            color: #9760FF;
        }

        .log-message {
            flex: 1;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .stat-card.mini {
            display: flex;
            flex-direction: column;
            padding: 15px;
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .stat-card.mini .stat-title {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 5px;
        }

        .stat-card.mini .stat-value {
            font-size: 20px;
            font-weight: 600;
        }

        .stat-card.mini .stat-icon {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .success-text {
            color: var(--success-color);
        }

        .warning-text {
            color: var(--warning-color);
        }

        .danger-text {
            color: var(--danger-color);
        }

        .success-bg {
            background-color: rgba(0, 196, 140, 0.2);
            color: var(--success-color);
        }

        .warning-bg {
            background-color: rgba(255, 185, 70, 0.2);
            color: var(--warning-color);
        }

        .danger-bg {
            background-color: rgba(242, 87, 103, 0.2);
            color: var(--danger-color);
        }

        .info-bg {
            background-color: rgba(0, 149, 255, 0.2);
            color: var(--primary-color);
        }

        /* 数据验证结果弹窗样式 */
        .validation-summary {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
        }

        .quality-score-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 150px;
        }

        .quality-score {
            text-align: center;
        }

        .circular-chart {
            width: 120px;
            height: 120px;
        }

        .circle-bg {
            fill: none;
            stroke: rgba(255, 255, 255, 0.1);
            stroke-width: 3;
        }

        .circle {
            fill: none;
            stroke: var(--primary-color);
            stroke-width: 3;
            stroke-linecap: round;
            animation: progress 1s ease-out forwards;
        }

        @keyframes progress {
            0% {
                stroke-dasharray: 0, 100;
            }
        }

        .percentage {
            fill: var(--text-color);
            font-size: 9px;
            text-anchor: middle;
            font-weight: bold;
        }

        .quality-label {
            margin-top: 10px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .validation-summary-stats {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .summary-stat-row {
            display: flex;
            justify-content: space-between;
            gap: 15px;
        }

        .summary-stat {
            text-align: center;
        }

        .summary-stat-value {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .summary-stat-label {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .action-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin: 20px 0 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .validation-rules-results {
            margin-bottom: 20px;
        }

        .validation-rule-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .validation-rule-item.success {
            border-left: 4px solid var(--success-color);
        }

        .validation-rule-item.warning {
            border-left: 4px solid var(--warning-color);
        }

        .validation-rule-item.danger {
            border-left: 4px solid var(--danger-color);
        }

        .rule-status {
            font-size: 20px;
        }

        .validation-rule-item.success .rule-status {
            color: var(--success-color);
        }

        .validation-rule-item.warning .rule-status {
            color: var(--warning-color);
        }

        .validation-rule-item.danger .rule-status {
            color: var(--danger-color);
        }

        .rule-info {
            flex: 1;
        }

        .rule-name {
            font-weight: 500;
            margin-bottom: 3px;
        }

        .rule-description {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .rule-stats {
            display: flex;
            gap: 20px;
        }

        .rule-stat {
            text-align: right;
        }

        .rule-stat-label {
            font-size: 13px;
            color: var(--text-secondary);
            margin-right: 5px;
        }

        .section-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .record-search {
            position: relative;
            width: 200px;
        }

        .record-search input {
            width: 100%;
            padding: 6px 10px 6px 30px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 13px;
        }

        .record-search i {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .record-filter {
            padding: 6px 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 13px;
        }

        .problem-table-container {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .problem-table {
            width: 100%;
            border-collapse: collapse;
        }

        .problem-table th {
            background-color: rgba(255, 255, 255, 0.03);
            color: var(--text-secondary);
            font-weight: normal;
            font-size: 13px;
            text-align: left;
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .problem-table td {
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
        }

        .problem-table tr:last-child td {
            border-bottom: none;
        }

        .problem-table tr:hover {
            background-color: rgba(255, 255, 255, 0.03);
        }

        .severity {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .severity.warning {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .severity.error {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .table-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .pagination-info {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination-btn {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .pagination-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-ellipsis {
            width: 30px;
            text-align: center;
        }

        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .chart-item {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .chart-title {
            padding: 10px 15px;
            background-color: rgba(255, 255, 255, 0.03);
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
            font-size: 14px;
        }

        .chart-placeholder {
            height: 250px;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
        }

        @media screen and (max-width: 768px) {
            .validation-summary {
                flex-direction: column;
            }

            .quality-score-container {
                width: 100%;
            }

            .charts-container {
                grid-template-columns: 1fr;
            }

            .stats-row {
                grid-template-columns: repeat(2, 1fr);
            }

            .summary-stat-row {
                flex-wrap: wrap;
            }

            .summary-stat {
                min-width: 120px;
                margin-bottom: 10px;
            }
        }

        /* 键值对输入样式 */
        .key-value-pairs {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .key-value-pair {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .key-value-pair .form-control {
            flex: 1;
        }

        .action-btn.small {
            padding: 6px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn.add-pair {
            align-self: flex-start;
            margin-top: 6px;
        }

        /* 代码编辑器样式 */
        .code-editor {
            font-family: monospace;
            white-space: pre;
            tab-size: 4;
            -moz-tab-size: 4;
            -o-tab-size: 4;
            resize: vertical;
            min-height: 100px;
        }

        /* 文件上传样式 */
        .file-upload-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .file-input {
            display: none;
        }

        .file-upload-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px dashed var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .file-info {
            font-size: 13px;
            color: var(--text-secondary);
            padding-left: 4px;
        }

        /* 切换开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-label {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            transition: .4s;
            cursor: pointer;
        }

        .toggle-label:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            border-radius: 50%;
            transition: .4s;
        }

        .toggle-input:checked + .toggle-label {
            background-color: var(--primary-color);
        }

        .toggle-input:checked + .toggle-label:before {
            transform: translateX(20px);
        }

        .file-info {
            font-size: 13px;
            color: var(--text-secondary);
            padding-left: 4px;
        }
        
        .template-download {
            margin-top: 12px;
        }
        
        .template-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: rgba(0, 196, 140, 0.1);
            border: 1px solid rgba(0, 196, 140, 0.3);
            color: var(--success-color);
            transition: all 0.3s ease;
        }
        
        .template-btn:hover {
            background-color: rgba(0, 196, 140, 0.2);
            border-color: var(--success-color);
        }

        /* 亮色主题的按钮样式调整 */
        :root.light-theme .action-btn {
            background-color: #F5F7FA;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .action-btn.primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .action-btn.primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        /* 亮色主题的统计卡片 */
        :root.light-theme .stat-card {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .stat-card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .stat-icon {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        :root.light-theme .stat-card.info .stat-icon {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        /* 亮色主题的表格调整 */
        :root.light-theme .table-responsive {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .table-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .bulk-action-btn {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .bulk-action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .task-table th {
            background-color: rgba(0, 0, 0, 0.01);
        }
        
        :root.light-theme .task-table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        :root.light-theme .status-running {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .status-paused {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .status-completed {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .status-failed {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        /* 亮色主题的弹窗样式调整 */
        :root.light-theme .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.4);
        }
        
        :root.light-theme .modal-content {
            background-color: var(--background-card);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .modal-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .modal-footer {
            border-top: 1px solid var(--border-color);
        }
        
        /* 亮色主题的表单元素 */
        :root.light-theme .form-control {
            background-color: #F8FAFD;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .form-control:focus {
            border-color: var(--primary-color);
            background-color: #FFFFFF;
        }
        
        /* 亮色主题的滚动条 */
        :root.light-theme ::-webkit-scrollbar-track {
            background: #F5F7FA;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb {
            background: #D0D6E0;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
    </style>

<body>
    <div class="container">

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题和操作按钮 -->
            <div class="page-header">
                
                <div class="task-actions">
                    <button class="action-btn">
                        <i class="fas fa-file-import"></i> 导入任务
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-file-export"></i> 导出数据
                    </button>
                    <button class="action-btn primary">
                        <i class="fas fa-plus"></i> 新建任务
                    </button>
                </div>
            </div>
        
            <!-- 统计卡片 -->
            <div class="stats-cards">
                <div class="stat-card info">
                    <div class="stat-header">
                        <div class="stat-title">任务总数</div>
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                    </div>
                    <div class="stat-value">146</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长 12 个</span>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-header">
                        <div class="stat-title">运行中</div>
                        <div class="stat-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">37</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较昨日增长 15 个</span>
                    </div>
                </div>
                <div class="stat-card white">
                    <div class="stat-header">
                        <div class="stat-title">已完成</div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">77</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较昨日增长 7 个</span>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-header">
                        <div class="stat-title">已暂停</div>
                        <div class="stat-icon">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">24</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-down stat-trend-down"></i>
                        <span>较昨日减少 8 个</span>
                    </div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-header">
                        <div class="stat-title">执行失败</div>
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">8</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较昨日增加 3 个</span>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="filter-section">
                <div class="search-row">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索任务名称、ID、数据源...">
                    </div>
                    <button class="advanced-filter-toggle">
                        <i class="fas fa-sliders-h"></i> 高级筛选
                    </button>
                </div>
                <div class="advanced-filter-row">
                    <div class="filter-group">
                        <label class="filter-label">任务状态</label>
                        <select class="filter-select">
                            <option value="">全部状态</option>
                            <option value="running">运行中</option>
                            <option value="paused">已暂停</option>
                            <option value="completed">已完成</option>
                            <option value="failed">执行失败</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">数据源类型</label>
                        <select class="filter-select">
                            <option value="">全部类型</option>
                            <option value="database">数据库</option>
                            <option value="api">API接口</option>
                            <option value="file">文件导入</option>
                            <option value="iot">IoT设备</option>
                            <option value="scrape">网页抓取</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">创建时间</label>
                        <select class="filter-select">
                            <option value="">全部时间</option>
                            <option value="today">今天</option>
                            <option value="yesterday">昨天</option>
                            <option value="last7days">最近7天</option>
                            <option value="last30days">最近30天</option>
                            <option value="custom">自定义范围</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">优先级</label>
                        <select class="filter-select">
                            <option value="">全部优先级</option>
                            <option value="high">高</option>
                            <option value="medium">中</option>
                            <option value="low">低</option>
                        </select>
                    </div>
                </div>
                <div class="filter-actions">
                    <button class="action-btn">重置筛选</button>
                    <button class="action-btn primary">应用筛选</button>
                </div>
            </div>

            <!-- 任务列表 -->
            <div class="tasks-section">
                <div class="table-responsive">
                    <div class="table-header">
                        <div class="table-title">全部任务</div>
                        <div class="table-actions">
                            <div class="bulk-select">
                                <label class="custom-checkbox">
                                    <input type="checkbox" id="select-all">
                                    <span class="checkmark"></span>
                                </label>
                                <span>全选</span>
                            </div>
                            <div class="bulk-actions">
                                <button class="bulk-action-btn">
                                    <i class="fas fa-play"></i> 批量启动
                                </button>
                                <button class="bulk-action-btn">
                                    <i class="fas fa-pause"></i> 批量暂停
                                </button>
                                <button class="bulk-action-btn">
                                    <i class="fas fa-stop"></i> 批量停止
                                </button>
                                <button class="bulk-action-btn delete">
                                    <i class="fas fa-trash-alt"></i> 批量删除
                                </button>
                            </div>
                        </div>
                    </div>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </th>
                                <th>任务名称</th>
                                <th>ID</th>
                                <th>数据源</th>
                                <th>状态</th>
                                <th>进度</th>
                                <th>优先级</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="task-name">电力系统运行数据采集</span>
                                    <div class="task-desc">收集城市电力系统各节点实时运行数据</div>
                                </td>
                                <td><span class="task-id">TASK-20250401-001</span></td>
                                <td>IoT设备</td>
                                <td><span class="task-status-badge status-running">运行中</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 75%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：75%</span>
                                        <span>耗时：2h 15m</span>
                                    </div>
                                </td>
                                <td><span class="priority-badge priority-high"></span>高</td>
                                <td>2025-04-01 08:30</td>
                                <td class="task-actions-cell">
                                    <button class="task-action pause" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="task-action stop" title="停止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="task-name">交通流量数据采集</span>
                                    <div class="task-desc">采集城市主要干道交通流量和拥堵情况</div>
                                </td>
                                <td><span class="task-id">TASK-20250402-002</span></td>
                                <td>API接口</td>
                                <td><span class="task-status-badge status-paused">已暂停</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar warning" style="width: 45%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：45%</span>
                                        <span>耗时：1h 30m</span>
                                    </div>
                                </td>
                                <td><span class="priority-badge priority-medium"></span>中</td>
                                <td>2025-04-01 09:15</td>
                                <td class="task-actions-cell">
                                    <button class="task-action play" title="启动">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="task-action stop" title="停止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">供水系统压力监测数据</span>
                                    <div class="task-desc">采集城市供水系统各节点压力和流量数据</div>
                                </td>
                                <td><span class="task-id">TASK-20250403-003</span></td>
                                <td>数据库</td>
                                <td><span class="task-status-badge status-completed">已完成</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 100%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：100%</span>
                                        <span>耗时：3h 45m</span>
                                    </div>
                                </td>
                                <td><span class="priority-badge priority-low"></span>低</td>
                                <td>2025-03-30 14:20</td>
                                <td class="task-actions-cell">
                                    <button class="task-action play" title="重新运行">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                    <button class="task-action view-data" title="查看已采集数据">
                                        <i class="fas fa-table"></i>
                                    </button>
                                </td>

                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">城市燃气管网监测数据</span>
                                    <div class="task-desc">采集燃气管网压力和泄漏监测数据</div>
                                </td>
                                <td><span class="task-id">TASK-20250403-004</span></td>
                                <td>IoT设备</td>
                                <td><span class="task-status-badge status-failed">执行失败</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar danger" style="width: 32%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：32%</span>
                                        <span>耗时：0h 45m</span>
                                    </div>
                                </td>
                                <td><span class="priority-badge priority-high"></span>高</td>
                                <td>2025-04-02 10:45</td>
                                <td class="task-actions-cell">
                                    <button class="task-action play" title="重试">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">通信网络流量数据</span>
                                    <div class="task-desc">采集城市通信网络流量和性能数据</div>
                                </td>
                                <td><span class="task-id">TASK-20250404-005</span></td>
                                <td>数据库</td>
                                <td><span class="task-status-badge status-running">运行中</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 88%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：78%</span>
                                        <span>耗时：3h 20m</span>
                                    </div>
                                </td>
                                <td><span class="priority-badge priority-high"></span>高</td>
                                <td>2025-04-02 23:10</td>
                                <td class="task-actions-cell">
                                    <button class="task-action pause" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="task-action stop" title="停止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>                            
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">电力负荷预测数据采集</span>
                                    <div class="task-desc">采集电力系统负荷预测和历史数据</div>
                                </td>
                                <td><span class="task-id">TASK-20250404-006</span></td>
                                <td>数据库</td>
                                <td><span class="task-status-badge status-running">运行中</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 88%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：88%</span>
                                        <span>耗时：4h 20m</span>
                                    </div>
                                </td>
                                <td><span class="priority-badge priority-high"></span>高</td>
                                <td>2025-04-02 23:10</td>
                                <td class="task-actions-cell">
                                    <button class="task-action pause" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="task-action stop" title="停止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="task-name">网络抓取城市交通拥堵指数</span>
                                    <div class="task-desc">从公开交通信息网站抓取拥堵指数数据</div>
                                </td>
                                <td><span class="task-id">TASK-20250404-007</span></td>
                                <td>网页抓取</td>
                                <td><span class="task-status-badge status-running">运行中</span></td>
                                <td>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar success" style="width: 12%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span>已完成：12%</span>
                                        <span>耗时：0h 45m</span>
                                    </div>
                                </td>
                                <td><span class="priority-badge priority-low"></span>低</td>
                                <td>2025-04-03 06:45</td>
                                <td class="task-actions-cell">
                                    <button class="task-action pause" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="task-action stop" title="停止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="task-action logs" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <button class="task-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="task-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-footer">
                        <div class="page-info">
                            显示 1 到 7 条，共 146 条记录
                        </div>
                        <div class="pagination">
                            <button class="page-btn disabled">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button class="page-btn disabled">
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">21</button>
                            <button class="page-btn">
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button class="page-btn">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务执行日志卡片 -->
            <div class="task-logs-card">
                <div class="card-header">
                    <div class="card-title">任务执行日志</div>
                    <div class="card-actions">
                        <button class="card-action"><i class="fas fa-sync-alt"></i></button>
                        <button class="card-action"><i class="fas fa-ellipsis-v"></i></button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="log-timeline">
                        <div class="log-item danger">
                            <div class="log-dot"></div>
                            <div class="log-content">
                                <div class="log-title">城市燃气管网监测数据采集失败</div>
                                <div class="log-detail">错误：连接超时 - 无法连接到IoT网关设备</div>
                                <div class="log-time">今天 10:47</div>
                            </div>
                        </div>
                        <div class="log-item success">
                            <div class="log-dot"></div>
                            <div class="log-content">
                                <div class="log-title">供水系统压力监测数据采集完成</div>
                                <div class="log-detail">成功采集 24,568 条记录，数据存储完成</div>
                                <div class="log-time">今天 09:32</div>
                            </div>
                        </div>
                        <div class="log-item warning">
                            <div class="log-dot"></div>
                            <div class="log-content">
                                <div class="log-title">交通流量数据采集被手动暂停</div>
                                <div class="log-detail">用户"张工程师"暂停了任务，已保存当前进度</div>
                                <div class="log-time">今天 08:45</div>
                            </div>
                        </div>
                        <div class="log-item info">
                            <div class="log-dot"></div>
                            <div class="log-content">
                                <div class="log-title">电力系统运行数据采集进度更新</div>
                                <div class="log-detail">当前进度：75%，预计完成时间：11:30</div>
                                <div class="log-time">今天 08:30</div>
                            </div>
                        </div>
                        <div class="log-item success">
                            <div class="log-dot"></div>
                            <div class="log-content">
                                <div class="log-title">新建采集任务"电力负荷预测数据采集"</div>
                                <div class="log-detail">任务配置完成并开始执行</div>
                                <div class="log-time">昨天 23:10</div>
                            </div>
                        </div>
                        <div class="log-item warning">
                            <div class="log-dot"></div>
                            <div class="log-content">
                                <div class="log-title">通信网络流量数据采集延迟</div>
                                <div class="log-detail">资源不足，任务已加入队列等待执行</div>
                                <div class="log-time">昨天 21:15</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 日志查看弹窗 -->
    <div class="modal-backdrop mlog-modal" id="mlog-modal">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">任务执行日志</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="mlog-header">
                    <div class="mlog-filter">
                        <div class="mlog-level active" data-level="all">全部</div>
                        <div class="mlog-level" data-level="info">信息</div>
                        <div class="mlog-level" data-level="warning">警告</div>
                        <div class="mlog-level" data-level="error">错误</div>
                        <div class="mlog-level" data-level="debug">调试</div>
                    </div>
                    <div class="mlog-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索日志内容..." id="mlog-search-input">
                    </div>
                </div>
                <div class="mlog-content">
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 07:45:26</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">任务启动: 开始执行数据采集任务 "城市交通流量监测数据采集"</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 07:45:28</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">正在连接API服务器: api.trafficdata.gov.cn</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 07:45:30</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">API连接成功，开始获取数据</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 07:46:15</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">已处理 1,000 条记录，总进度 10%</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 07:52:42</div>
                        <div class="mlog-level-indicator mlog-debug">DEBUG</div>
                        <div class="mlog-message">数据批次 #5 处理完成，内存使用: 128MB</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 07:55:33</div>
                        <div class="mlog-level-indicator mlog-warning">WARN</div>
                        <div class="mlog-message">数据格式不一致: 第6423条记录缺少时间戳字段，使用系统时间替代</div>
                    </div>
                    <div class="mlog-line error">
                        <div class="mlog-time">2025-04-03 07:58:19</div>
                        <div class="mlog-level-indicator mlog-error">ERROR</div>
                        <div class="mlog-message">API请求失败: 接口返回HTTP 503错误，服务暂时不可用</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 07:58:25</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">尝试重新连接API (尝试1/3)</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 07:58:40</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">重新连接成功，继续执行数据采集</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 08:01:12</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">已处理 5,000 条记录，总进度 50%</div>
                    </div>
                    <div class="mlog-line error">
                        <div class="mlog-time">2025-04-03 08:03:45</div>
                        <div class="mlog-level-indicator mlog-error">ERROR</div>
                        <div class="mlog-message">数据解析错误: 第7832条记录包含无效的JSON格式</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 08:03:48</div>
                        <div class="mlog-level-indicator mlog-info">INFO</div>
                        <div class="mlog-message">跳过无效记录，继续处理下一条</div>
                    </div>
                    <div class="mlog-line">
                        <div class="mlog-time">2025-04-03 08:04:55</div>
                        <div class="mlog-level-indicator mlog-debug">DEBUG</div>
                        <div class="mlog-message">数据批次 #12 处理完成，内存使用: 156MB</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="action-btn" id="export-log">
                    <i class="fas fa-download"></i> 导出日志
                </button>
                <button class="action-btn primary" id="close-mlog-modal">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 新建/编辑数据采集任务弹窗 -->
    <div class="modal-backdrop task-edit-modal" id="task-edit-modal">
        <div class="modal" style="max-width: 800px;">
            <div class="modal-header">
                <div class="modal-title">新建数据采集任务</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="task-form">
                    <!-- 表单标签页导航 -->
                    <div class="form-tabs">
                        <div class="form-tab active" data-tab="basic">基本信息</div>
                        <div class="form-tab" data-tab="datasource">数据源配置</div>
                        <div class="form-tab" data-tab="schedule">执行计划</div>
                        <div class="form-tab" data-tab="advanced">高级设置</div>
                    </div>

                    <!-- 基本信息标签页 -->
                    <div class="form-tab-content active" id="basic-tab">
                        <div class="form-section">
                            <div class="form-group">
                                <label class="form-label">任务名称 <span class="required">*</span></label>
                                <input type="text" class="form-control" placeholder="输入任务名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">任务描述</label>
                                <textarea class="form-control" placeholder="描述任务目的和用途" rows="3"></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group half">
                                    <label class="form-label">优先级</label>
                                    <select class="form-control">
                                        <option value="high">高</option>
                                        <option value="medium" selected>中</option>
                                        <option value="low">低</option>
                                    </select>
                                </div>
                                <div class="form-group half">
                                    <label class="form-label">标签</label>
                                    <input type="text" class="form-control" placeholder="添加标签，用逗号分隔">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据源配置标签页 -->
                    <div class="form-tab-content" id="datasource-tab">
                        <div class="form-section">
                            <div class="form-row">
                                <div class="form-group half">
                                    <label class="form-label">数据源类型</label>
                                    <select class="form-control" id="datasource-type">
                                        <option value="database">数据库</option>
                                        <option value="api">API接口</option>
                                        <option value="excel">Excel</option>
                                    </select>
                                </div>
                            </div>
                            <div class="datasource-config" id="database-config">
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">数据库类型</label>
                                        <select class="form-control">
                                            <option value="mysql">MySQL</option>
                                            <option value="postgresql">PostgreSQL</option>
                                            <option value="oracle">Oracle</option>
                                            <option value="sqlserver">SQL Server</option>
                                            <option value="mongodb">MongoDB</option>
                                        </select>
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">连接名称</label>
                                        <select class="form-control">
                                            <option value="">选择已保存的连接</option>
                                            <option value="conn1">生产环境主数据库</option>
                                            <option value="conn2">测试环境数据库</option>
                                            <option value="conn3">备份数据仓库</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">服务器地址</label>
                                        <input type="text" class="form-control" placeholder="输入主机名或IP地址">
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">端口</label>
                                        <input type="text" class="form-control" placeholder="端口号">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">数据库名</label>
                                        <input type="text" class="form-control" placeholder="数据库名称">
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">架构/模式</label>
                                        <input type="text" class="form-control" placeholder="Schema">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">用户名</label>
                                        <input type="text" class="form-control" placeholder="输入用户名">
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">密码</label>
                                        <input type="password" class="form-control" placeholder="输入密码">
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button class="action-btn">测试连接</button>
                                    <button class="action-btn">保存连接</button>
                                </div>
                            </div>
                            
                            <!-- API接口配置 -->
                            <div class="datasource-config" id="api-config" style="display: none;">
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">API类型</label>
                                        <select class="form-control">
                                            <option value="rest">REST API</option>
                                            <option value="graphql">GraphQL</option>
                                            <option value="soap">SOAP</option>
                                            <option value="websocket">WebSocket</option>
                                        </select>
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">接口名称</label>
                                        <select class="form-control">
                                            <option value="">选择已保存的接口</option>
                                            <option value="api1">城市交通数据API</option>
                                            <option value="api2">气象局开放接口</option>
                                            <option value="api3">能源监测接口</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">接口地址 (URL)</label>
                                    <input type="text" class="form-control" placeholder="https://api.example.com/data">
                                </div>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">请求方法</label>
                                        <select class="form-control">
                                            <option value="GET">GET</option>
                                            <option value="POST">POST</option>
                                            <option value="PUT">PUT</option>
                                            <option value="DELETE">DELETE</option>
                                        </select>
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">数据格式</label>
                                        <select class="form-control">
                                            <option value="json">JSON</option>
                                            <option value="xml">XML</option>
                                            <option value="csv">CSV</option>
                                            <option value="binary">二进制</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">请求头 (Headers)</label>
                                    <div class="key-value-pairs">
                                        <div class="key-value-pair">
                                            <input type="text" class="form-control" placeholder="键" value="Content-Type">
                                            <input type="text" class="form-control" placeholder="值" value="application/json">
                                            <button class="action-btn small delete"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                        <div class="key-value-pair">
                                            <input type="text" class="form-control" placeholder="键" value="Authorization">
                                            <input type="text" class="form-control" placeholder="值" value="Bearer {token}">
                                            <button class="action-btn small delete"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                        <button class="action-btn add-pair"><i class="fas fa-plus"></i> 添加参数</button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">请求参数</label>
                                    <div class="key-value-pairs">
                                        <div class="key-value-pair">
                                            <input type="text" class="form-control" placeholder="键" value="limit">
                                            <input type="text" class="form-control" placeholder="值" value="100">
                                            <button class="action-btn small delete"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                        <div class="key-value-pair">
                                            <input type="text" class="form-control" placeholder="键" value="format">
                                            <input type="text" class="form-control" placeholder="值" value="json">
                                            <button class="action-btn small delete"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                        <button class="action-btn add-pair"><i class="fas fa-plus"></i> 添加参数</button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">请求体 (Body)</label>
                                    <textarea class="form-control code-editor" rows="5" placeholder='{
    "query": "select * from data",
    "timestamp": "2025-04-01"
}'></textarea>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">认证方式</label>
                                    <select class="form-control" id="auth-method">
                                        <option value="none">无认证</option>
                                        <option value="basic">Basic Auth</option>
                                        <option value="bearer" selected>Bearer Token</option>
                                        <option value="oauth2">OAuth 2.0</option>
                                        <option value="apikey">API Key</option>
                                    </select>
                                </div>
                                <div class="auth-config" id="token-auth-config">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">访问令牌 (Token)</label>
                                            <input type="text" class="form-control" placeholder="输入访问令牌">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button class="action-btn">测试连接</button>
                                    <button class="action-btn">保存接口配置</button>
                                </div>
                            </div>
                            
                            <!-- Excel配置 -->
                            <div class="datasource-config" id="excel-config" style="display: none;">
                                <div class="form-group">
                                    <label class="form-label">文件来源</label>
                                    <div class="toggle-options">
                                        <div class="toggle-option active" data-mode="upload">
                                            <i class="fas fa-upload"></i> 本地上传
                                        </div>
                                        <div class="toggle-option" data-mode="remote">
                                            <i class="fas fa-cloud-download-alt"></i> 远程地址
                                        </div>
                                        <div class="toggle-option" data-mode="folder">
                                            <i class="fas fa-folder-open"></i> 监控文件夹
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 本地上传 -->
                                <div class="excel-source-config" id="upload-config">
                                    <div class="form-group">
                                        <label class="form-label">上传Excel文件</label>
                                        <div class="file-upload-container">
                                            <input type="file" id="excel-file" class="file-input" accept=".xlsx,.xls,.csv">
                                            <label for="excel-file" class="file-upload-btn">
                                                <i class="fas fa-file-excel"></i>
                                                <span>选择文件</span>
                                            </label>
                                            <div class="file-info">未选择文件</div>
                                        </div>
                                        <div class="template-download">
                                            <button class="action-btn template-btn">
                                                <i class="fas fa-download"></i> Excel模版下载
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 远程地址 -->
                                <div class="excel-source-config" id="remote-config" style="display: none;">
                                    <div class="form-group">
                                        <label class="form-label">文件URL</label>
                                        <input type="text" class="form-control" placeholder="https://example.com/data.xlsx">
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group half">
                                            <label class="form-label">用户名 (如需)</label>
                                            <input type="text" class="form-control" placeholder="输入用户名">
                                        </div>
                                        <div class="form-group half">
                                            <label class="form-label">密码 (如需)</label>
                                            <input type="password" class="form-control" placeholder="输入密码">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 监控文件夹 -->
                                <div class="excel-source-config" id="folder-config" style="display: none;">
                                    <div class="form-group">
                                        <label class="form-label">文件夹路径</label>
                                        <input type="text" class="form-control" placeholder="/data/excel_files/">
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group half">
                                            <label class="form-label">文件名模式</label>
                                            <input type="text" class="form-control" placeholder="data_*.xlsx">
                                        </div>
                                        <div class="form-group half">
                                            <label class="form-label">处理完成后</label>
                                            <select class="form-control">
                                                <option value="archive">归档</option>
                                                <option value="delete">删除</option>
                                                <option value="nothing">保留不变</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">文件设置</label>
                                    <div class="form-row">
                                        <div class="form-group half">
                                            <label class="form-label">工作表</label>
                                            <select class="form-control">
                                                <option value="first">第一个工作表</option>
                                                <option value="all">所有工作表</option>
                                                <option value="named">指定名称</option>
                                            </select>
                                        </div>
                                        <div class="form-group half">
                                            <label class="form-label">工作表名称</label>
                                            <input type="text" class="form-control" placeholder="Sheet1" disabled>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">包含标题行</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="header-row" class="toggle-input" checked>
                                            <label for="header-row" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">编码</label>
                                        <select class="form-control">
                                            <option value="utf8">UTF-8</option>
                                            <option value="gbk">GBK</option>
                                            <option value="iso">ISO-8859-1</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button class="action-btn">测试读取</button>
                                    <button class="action-btn">预览数据</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 执行计划标签页 -->
                    <div class="form-tab-content" id="schedule-tab">
                        <div class="form-section">
                            <div class="form-group">
                                <label class="form-label">执行模式</label>
                                <div class="toggle-options">
                                    <div class="toggle-option active" data-mode="once">
                                        <i class="fas fa-play"></i> 一次性执行
                                    </div>
                                    <div class="toggle-option" data-mode="scheduled">
                                        <i class="fas fa-calendar-alt"></i> 计划执行
                                    </div>
                                    <div class="toggle-option" data-mode="triggered">
                                        <i class="fas fa-bolt"></i> 触发执行
                                    </div>
                                </div>
                            </div>

                            <!-- 一次性执行设置 -->
                            <div class="schedule-config" id="once-config">
                                <div class="form-group">
                                    <label class="form-label">执行时间</label>
                                    <div class="form-row">
                                        <div class="form-group half">
                                            <select class="form-control">
                                                <option value="now">立即执行</option>
                                                <option value="scheduled">指定时间</option>
                                            </select>
                                        </div>
                                        <div class="form-group half">
                                            <input type="datetime-local" class="form-control" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 计划执行设置 -->
                            <div class="schedule-config" id="scheduled-config" style="display: none;">
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">重复频率</label>
                                        <select class="form-control" id="schedule-frequency">
                                            <option value="hourly">每小时</option>
                                            <option value="daily">每天</option>
                                            <option value="weekly">每周</option>
                                            <option value="monthly">每月</option>
                                            <option value="custom">自定义</option>
                                        </select>
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">具体时间</label>
                                        <input type="time" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group custom-schedule" style="display: none;">
                                    <label class="form-label">Cron表达式</label>
                                    <input type="text" class="form-control" placeholder="例如: 0 0 * * * 表示每天0点">
                                </div>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">开始日期</label>
                                        <input type="date" class="form-control">
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">结束日期 (可选)</label>
                                        <input type="date" class="form-control">
                                    </div>
                                </div>
                            </div>

                            <!-- 触发执行设置 -->
                            <div class="schedule-config" id="triggered-config" style="display: none;">
                                <div class="form-group">
                                    <label class="form-label">触发类型</label>
                                    <select class="form-control">
                                        <option value="file">文件到达</option>
                                        <option value="event">事件触发</option>
                                        <option value="api">API调用</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">触发条件</label>
                                    <input type="text" class="form-control" placeholder="触发条件或路径">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">资源配置</label>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">执行节点</label>
                                        <select class="form-control">
                                            <option value="auto">自动分配</option>
                                            <option value="node1">节点1 (主节点)</option>
                                            <option value="node2">节点2 (高性能)</option>
                                            <option value="node3">节点3 (备份)</option>
                                        </select>
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">优先级</label>
                                        <select class="form-control">
                                            <option value="low">低 (背景任务)</option>
                                            <option value="normal" selected>正常</option>
                                            <option value="high">高 (优先执行)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级设置标签页 -->
                    <div class="form-tab-content" id="advanced-tab">
                        <div class="form-section">
                            <div class="form-group">
                                <label class="form-label">错误处理</label>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">重试策略</label>
                                        <select class="form-control">
                                            <option value="none">不重试</option>
                                            <option value="immediate">立即重试</option>
                                            <option value="interval" selected>间隔重试</option>
                                            <option value="exponential">指数退避</option>
                                        </select>
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">最大重试次数</label>
                                        <input type="number" class="form-control" value="3">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">重试间隔(秒)</label>
                                        <input type="number" class="form-control" value="60">
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">失败处理</label>
                                        <select class="form-control">
                                            <option value="abort">终止任务</option>
                                            <option value="continue" selected>继续执行</option>
                                            <option value="skip">跳过错误记录</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">通知设置</label>
                                <div class="custom-switch">
                                    <input type="checkbox" id="email-notify" checked>
                                    <label for="email-notify">任务完成后发送邮件通知</label>
                                </div>
                                <div class="form-row notification-emails">
                                    <div class="form-group">
                                        <label class="form-label">收件人</label>
                                        <input type="text" class="form-control" placeholder="邮箱地址，多个用逗号分隔"
                                            value="<EMAIL>">
                                    </div>
                                </div>
                                <div class="custom-switch">
                                    <input type="checkbox" id="error-notify" checked>
                                    <label for="error-notify">任务失败时发送警报</label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">数据存储</label>
                                <div class="form-row">
                                    <div class="form-group half">
                                        <label class="form-label">存储位置</label>
                                        <select class="form-control">
                                            <option value="default">默认数据库</option>
                                            <option value="warehouse">数据仓库</option>
                                            <option value="file">文件系统</option>
                                        </select>
                                    </div>
                                    <div class="form-group half">
                                        <label class="form-label">存储格式</label>
                                        <select class="form-control">
                                            <option value="raw">原始格式</option>
                                            <option value="json" selected>JSON</option>
                                            <option value="csv">CSV</option>
                                            <option value="parquet">Parquet</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">存储路径/表名</label>
                                    <input type="text" class="form-control" placeholder="存储路径或表名"
                                        value="collected_data/sensor_readings">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="action-btn" id="cancel-task-edit">取消</button>
                <button class="action-btn" id="save-task-draft">保存草稿</button>
                <button class="action-btn primary" id="save-task">保存任务</button>
                <button class="action-btn success" id="save-and-run-task">保存并执行</button>
            </div>
        </div>
    </div>

    <!-- 查看数据采集进度/日志弹窗 -->
    <div class="modal-backdrop progress-modal" id="progress-modal">
        <div class="modal" style="max-width: 800px;">
            <div class="modal-header">
                <div class="modal-title">数据采集进度 - 电力系统运行数据采集</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="progress-overview">
                    <!-- 总体进度 -->
                    <div class="progress-summary">
                        <div class="progress-stats">
                            <div class="progress-stat-item">
                                <div class="stat-label">总体进度</div>
                                <div class="stat-value progress-percentage">75%</div>
                            </div>
                            <div class="progress-stat-item">
                                <div class="stat-label">已处理记录</div>
                                <div class="stat-value">15,783</div>
                            </div>
                            <div class="progress-stat-item">
                                <div class="stat-label">处理速率</div>
                                <div class="stat-value">1,250 条/分钟</div>
                            </div>
                            <div class="progress-stat-item">
                                <div class="stat-label">已用时间</div>
                                <div class="stat-value">2小时 15分钟</div>
                            </div>
                            <div class="progress-stat-item">
                                <div class="stat-label">预计剩余</div>
                                <div class="stat-value">45分钟</div>
                            </div>
                        </div>
                        <div class="total-progress-container">
                            <div class="total-progress-bar" style="width: 75%"></div>
                        </div>
                    </div>

                    <!-- 阶段进度指示器 -->
                    <div class="stage-progress">
                        <div class="stage-progress-header">
                            <div class="stage-title">处理阶段</div>
                            <div class="stage-refresh">
                                <button class="action-btn icon-only small"><i class="fas fa-sync-alt"></i></button>
                                <div class="auto-refresh">
                                    <span>自动刷新</span>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="stage-progress-items">
                            <div class="stage-progress-item completed">
                                <div class="stage-icon"><i class="fas fa-check"></i></div>
                                <div class="stage-info">
                                    <div class="stage-name">连接数据源</div>
                                    <div class="stage-time">07:30:15</div>
                                </div>
                                <div class="stage-status">已完成</div>
                            </div>
                            <div class="stage-progress-item completed">
                                <div class="stage-icon"><i class="fas fa-check"></i></div>
                                <div class="stage-info">
                                    <div class="stage-name">验证数据结构</div>
                                    <div class="stage-time">07:30:45</div>
                                </div>
                                <div class="stage-status">已完成</div>
                            </div>
                            <div class="stage-progress-item completed">
                                <div class="stage-icon"><i class="fas fa-check"></i></div>
                                <div class="stage-info">
                                    <div class="stage-name">数据采集与处理</div>
                                    <div class="stage-time">08:30:45</div>
                                </div>
                                <div class="stage-status">已完成</div>
                            </div>
                            <div class="stage-progress-item completed">
                                <div class="stage-icon"><i class="fas fa-check"></i></div>
                                <!-- <div class="stage-icon"><i class="fas fa-hourglass-half"></i></div> -->
                                <div class="stage-info">
                                    <div class="stage-name">数据验证</div>
                                    <div class="stage-time">09:30:45</div>
                                </div>
                                <div class="stage-status">已完成</div>
                            </div>
                            <div class="stage-progress-item active">
                                <div class="stage-icon"><i class="fas fa-sync fa-spin"></i></div>
                                <!-- <div class="stage-icon"><i class="fas fa-hourglass-half"></i></div> -->
                                <div class="stage-info">
                                    <div class="stage-name">数据存储</div>
                                    <div class="stage-time">进行中</div>
                                </div>
                                <div class="stage-status">5%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志控制台 -->
                <div class="log-console">
                    <div class="log-console-header">
                        <div class="log-title">实时日志</div>
                        <div class="log-filter">
                            <button class="log-level-btn all active">全部</button>
                            <button class="log-level-btn info">信息</button>
                            <button class="log-level-btn warning">警告</button>
                            <button class="log-level-btn error">错误</button>
                            <button class="log-level-btn debug">调试</button>
                            <div class="log-search">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="搜索日志...">
                            </div>
                        </div>
                    </div>
                    <div class="log-console-body">
                        <div class="log-line info">
                            <span class="log-time">[08:45:12]</span>
                            <span class="log-level">INFO</span>
                            <span class="log-message">开始读取电力系统数据批次 #25</span>
                        </div>
                        <div class="log-line info">
                            <span class="log-time">[08:45:20]</span>
                            <span class="log-level">INFO</span>
                            <span class="log-message">成功获取 512 条记录，总计已处理 15,783 条</span>
                        </div>
                        <div class="log-line debug">
                            <span class="log-time">[08:45:25]</span>
                            <span class="log-level">DEBUG</span>
                            <span class="log-message">当前数据批次处理耗时：4.5秒，平均处理速率：113.8条/秒</span>
                        </div>
                        <div class="log-line warning">
                            <span class="log-time">[08:45:30]</span>
                            <span class="log-level">WARNING</span>
                            <span class="log-message">检测到3条记录缺少电压值字段，使用默认值代替</span>
                        </div>
                        <div class="log-line info">
                            <span class="log-time">[08:45:32]</span>
                            <span class="log-level">INFO</span>
                            <span class="log-message">批次 #25 处理完成，开始存储到数据库</span>
                        </div>
                        <div class="log-line error">
                            <span class="log-time">[08:45:40]</span>
                            <span class="log-level">ERROR</span>
                            <span class="log-message">存储操作超时，尝试重新连接数据库</span>
                        </div>
                        <div class="log-line info">
                            <span class="log-time">[08:45:45]</span>
                            <span class="log-level">INFO</span>
                            <span class="log-message">数据库连接已恢复，继续存储操作</span>
                        </div>
                        <div class="log-line info">
                            <span class="log-time">[08:45:50]</span>
                            <span class="log-level">INFO</span>
                            <span class="log-message">批次 #25 数据已成功存储</span>
                        </div>
                        <div class="log-line info">
                            <span class="log-time">[08:45:55]</span>
                            <span class="log-level">INFO</span>
                            <span class="log-message">开始读取电力系统数据批次 #26</span>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="stats-row">
                    <div class="stat-card mini">
                        <div class="stat-title">成功记录</div>
                        <div class="stat-value success-text">15,770</div>
                        <div class="stat-icon success-bg"><i class="fas fa-check"></i></div>
                    </div>
                    <div class="stat-card mini">
                        <div class="stat-title">警告记录</div>
                        <div class="stat-value warning-text">45</div>
                        <div class="stat-icon warning-bg"><i class="fas fa-exclamation-triangle"></i></div>
                    </div>
                    <div class="stat-card mini">
                        <div class="stat-title">错误记录</div>
                        <div class="stat-value danger-text">13</div>
                        <div class="stat-icon danger-bg"><i class="fas fa-times"></i></div>
                    </div>
                    <div class="stat-card mini">
                        <div class="stat-title">系统负载</div>
                        <div class="stat-value">28%</div>
                        <div class="stat-icon info-bg"><i class="fas fa-microchip"></i></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="action-btn danger" id="abort-task">终止任务</button>
                <button class="action-btn warning" id="pause-task">暂停任务</button>
                <button class="action-btn info" id="download-log">
                    <i class="fas fa-download"></i> 下载日志
                </button>
                <button class="action-btn primary" id="close-progress-modal">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 数据验证结果弹窗 -->
    <div class="modal-backdrop validation-modal" id="validation-modal">
        <div class="modal" style="max-width: 800px;">
            <div class="modal-header">
                <div class="modal-title">数据验证结果 - 电力系统运行数据采集</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 验证结果摘要 -->
                <div class="validation-summary">
                    <div class="quality-score-container">
                        <div class="quality-score">
                            <svg viewBox="0 0 36 36" class="circular-chart">
                                <path class="circle-bg" d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831" />
                                <path class="circle" stroke-dasharray="85, 100" d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831" />
                                <text x="18" y="20.35" class="percentage">85%</text>
                            </svg>
                            <div class="quality-label">数据质量评分</div>
                        </div>
                    </div>
                    <div class="validation-summary-stats">
                        <div class="summary-stat-row">
                            <div class="summary-stat">
                                <div class="summary-stat-value">20,432</div>
                                <div class="summary-stat-label">总记录数</div>
                            </div>
                            <div class="summary-stat">
                                <div class="summary-stat-value success-text">19,350</div>
                                <div class="summary-stat-label">通过验证</div>
                            </div>
                            <div class="summary-stat">
                                <div class="summary-stat-value warning-text">872</div>
                                <div class="summary-stat-label">警告记录</div>
                            </div>
                            <div class="summary-stat">
                                <div class="summary-stat-value danger-text">210</div>
                                <div class="summary-stat-label">失败记录</div>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="action-btn small"><i class="fas fa-download"></i> 导出报告</button>
                            <button class="action-btn small"><i class="fas fa-sync"></i> 重新验证</button>
                        </div>
                    </div>
                </div>

                <!-- 验证规则结果 -->
                <div class="validation-rules-results">
                    <div class="section-title">验证规则执行结果</div>
                    <div class="validation-rule-item success">
                        <div class="rule-status"><i class="fas fa-check-circle"></i></div>
                        <div class="rule-info">
                            <div class="rule-name">设备ID格式验证</div>
                            <div class="rule-description">验证设备ID是否符合指定格式</div>
                        </div>
                        <div class="rule-stats">
                            <div class="rule-stat">
                                <span class="rule-stat-label">通过率:</span>
                                <span class="rule-stat-value">100%</span>
                            </div>
                            <div class="rule-stat">
                                <span class="rule-stat-label">错误数:</span>
                                <span class="rule-stat-value">0</span>
                            </div>
                        </div>
                    </div>
                    <div class="validation-rule-item warning">
                        <div class="rule-status"><i class="fas fa-exclamation-circle"></i></div>
                        <div class="rule-info">
                            <div class="rule-name">时间戳连续性检查</div>
                            <div class="rule-description">检查时间戳是否存在异常间隔</div>
                        </div>
                        <div class="rule-stats">
                            <div class="rule-stat">
                                <span class="rule-stat-label">通过率:</span>
                                <span class="rule-stat-value">93%</span>
                            </div>
                            <div class="rule-stat">
                                <span class="rule-stat-label">警告数:</span>
                                <span class="rule-stat-value">872</span>
                            </div>
                        </div>
                    </div>
                    <div class="validation-rule-item danger">
                        <div class="rule-status"><i class="fas fa-times-circle"></i></div>
                        <div class="rule-info">
                            <div class="rule-name">电压值范围检查</div>
                            <div class="rule-description">验证电压值是否在有效范围内</div>
                        </div>
                        <div class="rule-stats">
                            <div class="rule-stat">
                                <span class="rule-stat-label">通过率:</span>
                                <span class="rule-stat-value">98%</span>
                            </div>
                            <div class="rule-stat">
                                <span class="rule-stat-label">错误数:</span>
                                <span class="rule-stat-value">210</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 问题记录表格 -->
                <div class="problem-records">
                    <div class="section-title">
                        <span>问题记录</span>
                        <div class="section-actions">
                            <div class="record-search">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="搜索记录...">
                            </div>
                            <select class="record-filter">
                                <option value="all">所有问题</option>
                                <option value="warning">仅警告</option>
                                <option value="error">仅错误</option>
                            </select>
                        </div>
                    </div>
                    <div class="problem-table-container">
                        <table class="problem-table">
                            <thead>
                                <tr>
                                    <th>记录ID</th>
                                    <th>时间戳</th>
                                    <th>设备ID</th>
                                    <th>问题字段</th>
                                    <th>问题描述</th>
                                    <th>严重程度</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>15089</td>
                                    <td>2025-04-03 07:42:15</td>
                                    <td>DEV-PS-1023</td>
                                    <td>voltage</td>
                                    <td>电压值 (320.5) 超出有效范围 [0-300]</td>
                                    <td><span class="severity error">错误</span></td>
                                    <td>
                                        <button class="action-btn icon-only small"><i class="fas fa-edit"></i></button>
                                        <button class="action-btn icon-only small"><i class="fas fa-check"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>15092</td>
                                    <td>2025-04-03 07:44:05</td>
                                    <td>DEV-PS-1045</td>
                                    <td>timestamp</td>
                                    <td>时间间隔异常 (相差 120 秒，预期 60 秒)</td>
                                    <td><span class="severity warning">警告</span></td>
                                    <td>
                                        <button class="action-btn icon-only small"><i class="fas fa-edit"></i></button>
                                        <button class="action-btn icon-only small"><i class="fas fa-check"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>15103</td>
                                    <td>2025-04-03 07:45:30</td>
                                    <td>DEV-PS-1032</td>
                                    <td>voltage</td>
                                    <td>电压值 (305.2) 超出有效范围 [0-300]</td>
                                    <td><span class="severity error">错误</span></td>
                                    <td>
                                        <button class="action-btn icon-only small"><i class="fas fa-edit"></i></button>
                                        <button class="action-btn icon-only small"><i class="fas fa-check"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>15118</td>
                                    <td>2025-04-03 07:48:20</td>
                                    <td>DEV-PS-1018</td>
                                    <td>timestamp</td>
                                    <td>时间间隔异常 (相差 90 秒，预期 60 秒)</td>
                                    <td><span class="severity warning">警告</span></td>
                                    <td>
                                        <button class="action-btn icon-only small"><i class="fas fa-edit"></i></button>
                                        <button class="action-btn icon-only small"><i class="fas fa-check"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="table-pagination">
                        <span class="pagination-info">显示 1-4 条，共 1,082 条记录</span>
                        <div class="pagination-controls">
                            <button class="pagination-btn" disabled><i class="fas fa-angle-double-left"></i></button>
                            <button class="pagination-btn" disabled><i class="fas fa-angle-left"></i></button>
                            <button class="pagination-btn active">1</button>
                            <button class="pagination-btn">2</button>
                            <button class="pagination-btn">3</button>
                            <span class="pagination-ellipsis">...</span>
                            <button class="pagination-btn">271</button>
                            <button class="pagination-btn"><i class="fas fa-angle-right"></i></button>
                            <button class="pagination-btn"><i class="fas fa-angle-double-right"></i></button>
                        </div>
                    </div>
                </div>

                <!-- 数据分布图表 -->
                <div class="data-distribution">
                    <div class="section-title">数据分布分析</div>
                    <div class="charts-container">
                        <div class="chart-item">
                            <div class="chart-title">电压值分布</div>
                            <div class="chart-placeholder" id="voltage-chart">
                                <!-- 图表将通过JS渲染 -->
                            </div>
                        </div>
                        <div class="chart-item">
                            <div class="chart-title">采样时间间隔分布</div>
                            <div class="chart-placeholder" id="time-interval-chart">
                                <!-- 图表将通过JS渲染 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="action-btn" id="edit-rules">编辑验证规则</button>
                <button class="action-btn warning" id="ignore-warnings">忽略所有警告</button>
                <button class="action-btn info" id="export-validation-report">
                    <i class="fas fa-download"></i> 导出报告
                </button>
                <button class="action-btn primary" id="accept-data">
                    <i class="fas fa-check"></i> 接受数据
                </button>
            </div>
        </div>
    </div>

    <!-- 查看已采集数据弹窗 -->
    <div class="modal-backdrop view-data-modal" id="view-data-modal">
        <div class="modal" style="max-width: 900px;">
            <div class="modal-header">
                <div class="modal-title">已采集数据 - 供水系统压力监测数据</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 数据统计摘要 -->
                <div class="stats-row" style="margin-bottom: 20px;">
                    <div class="stat-card mini">
                        <div class="stat-title">采集时间</div>
                        <div class="stat-value">2025-03-30</div>
                        <div class="stat-icon info-bg"><i class="fas fa-calendar"></i></div>
                    </div>
                    <div class="stat-card mini">
                        <div class="stat-title">总记录数</div>
                        <div class="stat-value">24,568</div>
                        <div class="stat-icon info-bg"><i class="fas fa-database"></i></div>
                    </div>
                    <!-- <div class="stat-card mini">
                        <div class="stat-title">节点数量</div>
                        <div class="stat-value">128</div>
                        <div class="stat-icon info-bg"><i class="fas fa-sitemap"></i></div>
                    </div> -->
                    <div class="stat-card mini">
                        <div class="stat-title">数据大小</div>
                        <div class="stat-value">4.2 MB</div>
                        <div class="stat-icon info-bg"><i class="fas fa-hdd"></i></div>
                    </div>
                </div>

                <!-- 数据筛选工具栏 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 15px; align-items: center;">
                    <div class="section-title" style="margin: 0;">数据记录</div>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <div class="record-search">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索数据...">
                        </div>
                        <select class="record-filter">
                            <option value="all">全部数据</option>
                            <option value="day1">最近24小时</option>
                            <option value="week1">最近7天</option>
                            <option value="month1">最近30天</option>
                        </select>
                        <button class="action-btn small">
                            <i class="fas fa-filter"></i> 高级筛选
                        </button>
                        <button class="action-btn small">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="problem-table-container">
                    <table class="problem-table">
                        <thead>
                            <tr>
                                <th style="width: 60px;">#</th>
                                <th>时间戳</th>
                                <th>节点ID</th>
                                <th>压力值(MPa)</th>
                                <th>流量(m³/h)</th>
                                <th>温度(°C)</th>
                                <th>状态</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0042</td>
                                <td>0.42</td>
                                <td>128.5</td>
                                <td>18.2</td>
                                <td><span class="task-status-badge status-running">正常</span></td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0043</td>
                                <td>0.39</td>
                                <td>112.8</td>
                                <td>18.5</td>
                                <td><span class="task-status-badge status-running">正常</span></td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0044</td>
                                <td>0.28</td>
                                <td>95.2</td>
                                <td>19.0</td>
                                <td><span class="task-status-badge status-warning">偏低</span></td>
                                <td>压力波动</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0045</td>
                                <td>0.45</td>
                                <td>135.6</td>
                                <td>17.8</td>
                                <td><span class="task-status-badge status-running">正常</span></td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0046</td>
                                <td>0.41</td>
                                <td>122.3</td>
                                <td>18.1</td>
                                <td><span class="task-status-badge status-running">正常</span></td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0047</td>
                                <td>0.18</td>
                                <td>58.7</td>
                                <td>19.2</td>
                                <td><span class="task-status-badge status-failed">异常</span></td>
                                <td>管道故障</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0048</td>
                                <td>0.40</td>
                                <td>118.9</td>
                                <td>18.3</td>
                                <td><span class="task-status-badge status-running">正常</span></td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0049</td>
                                <td>0.43</td>
                                <td>130.1</td>
                                <td>17.9</td>
                                <td><span class="task-status-badge status-running">正常</span></td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0050</td>
                                <td>0.37</td>
                                <td>105.6</td>
                                <td>18.7</td>
                                <td><span class="task-status-badge status-running">正常</span></td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>2025-03-30 14:00:00</td>
                                <td>NODE-WS-0051</td>
                                <td>0.44</td>
                                <td>132.8</td>
                                <td>18.0</td>
                                <td><span class="task-status-badge status-running">正常</span></td>
                                <td>-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="table-pagination">
                    <span class="pagination-info">显示 1-10 条，共 24,568 条记录</span>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled><i class="fas fa-angle-double-left"></i></button>
                        <button class="pagination-btn" disabled><i class="fas fa-angle-left"></i></button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <span class="pagination-ellipsis">...</span>
                        <button class="pagination-btn">2457</button>
                        <button class="pagination-btn"><i class="fas fa-angle-right"></i></button>
                        <button class="pagination-btn"><i class="fas fa-angle-double-right"></i></button>
                    </div>
                </div>

                <!-- 数据可视化区域 -->
                <div style="margin-top: 25px;display:none;">
                    <div class="section-title">数据可视化</div>
                    <div class="charts-container">
                        <div class="chart-item">
                            <div class="chart-title">压力变化趋势</div>
                            <div class="chart-placeholder" id="pressure-chart">
                                <!-- 图表将通过JS渲染 -->
                                <div
                                    style="display: flex; justify-content: center; align-items: center; height: 250px; color: var(--text-secondary);">
                                    正在加载图表...
                                </div>
                            </div>
                        </div>
                        <div class="chart-item">
                            <div class="chart-title">流量分布</div>
                            <div class="chart-placeholder" id="flow-chart">
                                <!-- 图表将通过JS渲染 -->
                                <div
                                    style="display: flex; justify-content: center; align-items: center; height: 250px; color: var(--text-secondary);">
                                    正在加载图表...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <!-- <button class="action-btn" id="print-data">
                    <i class="fas fa-print"></i> 打印数据
                </button> -->
                <button class="action-btn info" id="download-data">
                    <i class="fas fa-download"></i> 下载数据
                </button>
                <button class="action-btn primary" id="close-view-data-modal">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>

    <script>
        // 高级筛选切换
        const advancedFilterToggle = document.querySelector('.advanced-filter-toggle');
        const advancedFilterRow = document.querySelector('.advanced-filter-row');
        const filterActions = document.querySelector('.filter-actions');

        advancedFilterRow.style.display = 'none';
        filterActions.style.display = 'none';

        advancedFilterToggle.addEventListener('click', function () {
            if (advancedFilterRow.style.display === 'none') {
                advancedFilterRow.style.display = 'grid';
                filterActions.style.display = 'flex';
                this.innerHTML = '<i class="fas fa-chevron-up"></i> 收起筛选';
            } else {
                advancedFilterRow.style.display = 'none';
                filterActions.style.display = 'none';
                this.innerHTML = '<i class="fas fa-sliders-h"></i> 高级筛选';
            }
        });

        // 全选功能
        const selectAllCheckbox = document.querySelector('#select-all');
        const checkboxes = document.querySelectorAll('.task-table tbody .custom-checkbox input');

        selectAllCheckbox.addEventListener('change', function () {
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 收藏功能
        const favoriteButtons = document.querySelectorAll('.favorite-action');

        favoriteButtons.forEach(button => {
            button.addEventListener('click', function () {
                const starIcon = this.querySelector('.star-icon');
                if (starIcon.classList.contains('star-active')) {
                    starIcon.classList.remove('star-active');
                    this.classList.remove('active');
                } else {
                    starIcon.classList.add('star-active');
                    this.classList.add('active');
                }
            });
        });

        // 表格操作按钮交互
        const actionButtons = document.querySelectorAll('.task-action');

        actionButtons.forEach(button => {
            button.addEventListener('click', function () {
                const action = this.classList.contains('play') ? '启动' :
                    this.classList.contains('pause') ? '暂停' :
                        this.classList.contains('stop') ? '停止' :
                            this.classList.contains('logs') ? '查看日志' :
                                this.classList.contains('edit') ? '编辑' :
                                    this.classList.contains('delete') ? '删除' : '查看结果';

                const taskName = this.closest('tr').querySelector('.task-name').textContent;

                if (action === '删除') {
                    if (confirm(`确定要删除任务"${taskName}"吗？此操作不可撤销。`)) {
                        alert(`任务"${taskName}"已删除`);
                    }
                } else if (action === '停止') {
                    if (confirm(`确定要停止任务"${taskName}"吗？任务将无法继续执行。`)) {
                        alert(`任务"${taskName}"已停止`);
                    }
                } else if (action === '查看日志') {
                    showLogModal();
                }
                //else {
                //    alert(`正在${action}任务: ${taskName}`);
                //}
            });
        });

        // 主要操作按钮交互
        const mainActionButtons = document.querySelectorAll('.task-actions .action-btn');

        //mainActionButtons.forEach(button => {
        //    button.addEventListener('click', function() {
        //        if (this.innerHTML.includes('导入任务')) {
        //            alert('打开任务导入对话框');
        //        } else if (this.innerHTML.includes('导出数据')) {
        //            alert('导出选中的任务数据');
        //        } else if (this.innerHTML.includes('新建任务')) {
        //            alert('打开新建任务表单');
        //        }
        //    });
        //});

        // 分页功能
        const pageButtons = document.querySelectorAll('.pagination .page-btn');

        pageButtons.forEach(button => {
            if (!button.classList.contains('disabled') && !button.classList.contains('active')) {
                button.addEventListener('click', function () {
                    pageButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    if (this.textContent.trim() !== '...') {
                        alert(`切换到第 ${this.textContent} 页`);
                    }
                });
            }
        });

        // 批量操作按钮交互
        const bulkActionButtons = document.querySelectorAll('.bulk-action-btn');

        bulkActionButtons.forEach(button => {
            button.addEventListener('click', function () {
                const action = this.innerHTML.includes('批量启动') ? '启动' :
                    this.innerHTML.includes('批量暂停') ? '暂停' :
                        this.innerHTML.includes('批量停止') ? '停止' : '删除';

                let checkedCount = 0;
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        checkedCount++;
                    }
                });

                if (checkedCount === 0) {
                    alert(`请先选择要${action}的任务`);
                } else {
                    if ((action === '删除' || action === '停止') &&
                        !confirm(`确定要${action}选中的 ${checkedCount} 个任务吗？此操作可能无法撤销。`)) {
                        return;
                    }
                    alert(`已${action}选中的 ${checkedCount} 个任务`);
                }
            });
        });

        // 显示日志弹窗
        const showLogModal = () => {
            document.getElementById('mlog-modal').style.display = 'flex';

            // 日志过滤功能
            const logLevels = document.querySelectorAll('.mlog-level');
            const logLines = document.querySelectorAll('.mlog-line');

            logLevels.forEach(level => {
                level.addEventListener('click', function () {
                    // 移除其他活动状态
                    logLevels.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');

                    const filterLevel = this.getAttribute('data-level');

                    logLines.forEach(line => {
                        if (filterLevel === 'all') {
                            line.style.display = '';
                        } else {
                            const lineLevel = line.querySelector('.mlog-level-indicator').classList[1].split('-')[1];
                            line.style.display = lineLevel === filterLevel ? '' : 'none';
                        }
                    });
                });
            });

            // 日志搜索功能
            const logSearchInput = document.getElementById('mlog-search-input');

            logSearchInput.addEventListener('input', function () {
                const searchText = this.value.toLowerCase();

                logLines.forEach(line => {
                    const message = line.querySelector('.mlog-message').textContent.toLowerCase();
                    if (searchText === '' || message.includes(searchText)) {
                        line.style.display = '';
                    } else {
                        line.style.display = 'none';
                    }
                });
            });
        }

        // 关闭日志弹窗
        document.querySelectorAll('#close-mlog-modal, .mlog-modal .modal-close').forEach(button => {
            button.addEventListener('click', function () {
                document.getElementById('mlog-modal').style.display = 'none';
            });
        });

        // 显示编辑任务弹窗
        const showTaskEditModal = () => {
            document.getElementById('task-edit-modal').style.display = 'flex';
        };

        // 显示进度弹窗
        const showProgressModal = () => {
            document.getElementById('progress-modal').style.display = 'flex';
        };

        // 显示验证结果弹窗
        const showValidationModal = () => {
            document.getElementById('validation-modal').style.display = 'flex';

            // 初始化图表
            if (typeof echarts !== 'undefined') {
                // 电压值分布图
                const voltageChart = echarts.init(document.getElementById('voltage-chart'));
                voltageChart.setOption({
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        top: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['0-50', '50-100', '100-150', '150-200', '200-250', '250-300', '>300'],
                        axisLine: { lineStyle: { color: '#2A3142' } },
                        axisLabel: { color: '#A0A8B8' }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#2A3142' } },
                        axisLabel: { color: '#A0A8B8' },
                        splitLine: { lineStyle: { color: '#2A3142', opacity: 0.3 } }
                    },
                    series: [{
                        data: [1200, 2350, 4580, 6890, 4250, 890, 210],
                        type: 'bar',
                        barWidth: '60%',
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: '#3E9BFF' },
                                { offset: 1, color: '#0055A9' }
                            ])
                        }
                    }]
                });

                // 时间间隔分布图
                const timeIntervalChart = echarts.init(document.getElementById('time-interval-chart'));
                timeIntervalChart.setOption({
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        top: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['<30s', '30-45s', '45-60s', '60-75s', '75-90s', '90-120s', '>120s'],
                        axisLine: { lineStyle: { color: '#2A3142' } },
                        axisLabel: { color: '#A0A8B8' }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#2A3142' } },
                        axisLabel: { color: '#A0A8B8' },
                        splitLine: { lineStyle: { color: '#2A3142', opacity: 0.3 } }
                    },
                    series: [{
                        data: [320, 480, 18560, 420, 350, 280, 190],
                        type: 'bar',
                        barWidth: '60%',
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: '#00C48C' },
                                { offset: 1, color: '#007451' }
                            ])
                        }
                    }]
                });

                // 窗口大小改变时重新调整图表大小
                window.addEventListener('resize', function () {
                    voltageChart.resize();
                    timeIntervalChart.resize();
                });
            }
        };

        // 关闭所有弹窗
        const closeAllModals = () => {
            document.querySelectorAll('.modal-backdrop').forEach(modal => {
                modal.style.display = 'none';
            });
        };

        // 表单标签页切换
        document.querySelectorAll('.form-tab').forEach(tab => {
            tab.addEventListener('click', function () {
                // 移除其他标签页的active类
                document.querySelectorAll('.form-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.form-tab-content').forEach(c => c.classList.remove('active'));

                // 添加当前标签页的active类
                this.classList.add('active');
                const tabId = this.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        // 执行模式切换
        document.querySelectorAll('.toggle-option').forEach(option => {
            option.addEventListener('click', function () {
                // 移除其他选项的active类
                document.querySelectorAll('.toggle-option').forEach(o => o.classList.remove('active'));
                // 隐藏所有配置
                document.querySelectorAll('.schedule-config').forEach(c => c.style.display = 'none');

                // 添加当前选项的active类
                this.classList.add('active');
                const mode = this.getAttribute('data-mode');
                document.getElementById(`${mode}-config`).style.display = 'block';
            });
        });

        // 数据源类型选择
        document.querySelectorAll('.datasource-type-option').forEach(option => {
            option.addEventListener('click', function () {
                document.querySelectorAll('.datasource-type-option').forEach(o => o.classList.remove('active'));
                this.classList.add('active');
                // 这里可以添加根据选择的数据源类型显示不同配置面板的逻辑
            });
        });

        // 关闭弹窗
        document.querySelectorAll('.modal-close, #cancel-task-edit, #close-progress-modal').forEach(button => {
            button.addEventListener('click', function () {
                closeAllModals();
            });
        });

        // 绑定编辑按钮事件
        document.querySelectorAll('.task-action.edit').forEach(button => {
            button.addEventListener('click', function (e) {
                e.stopPropagation(); // 防止事件冒泡
                showTaskEditModal();
            });
        });

        // 绑定日志查看按钮事件
        document.querySelectorAll('.task-action.logs').forEach(button => {
            button.addEventListener('click', function (e) {
                e.stopPropagation(); // 防止事件冒泡
                showProgressModal();
            });
        });

        // 新建任务按钮事件
        document.querySelector('.task-actions .action-btn.primary').addEventListener('click', function () {
            showTaskEditModal();
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 页面加载完成后的初始化逻辑
        });

        // 显示查看数据弹窗
        const showViewDataModal = () => {
            document.getElementById('view-data-modal').style.display = 'flex';

            // 初始化数据图表
            if (typeof echarts !== 'undefined') {
                // 压力变化趋势图
                const pressureChart = echarts.init(document.getElementById('pressure-chart'));
                pressureChart.setOption({
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        top: '8%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],
                        axisLine: { lineStyle: { color: '#2A3142' } },
                        axisLabel: { color: '#A0A8B8' }
                    },
                    yAxis: {
                        type: 'value',
                        name: '压力(MPa)',
                        nameTextStyle: { color: '#A0A8B8' },
                        min: 0,
                        max: 0.6,
                        axisLine: { lineStyle: { color: '#2A3142' } },
                        axisLabel: { color: '#A0A8B8' },
                        splitLine: { lineStyle: { color: '#2A3142', opacity: 0.3 } }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(26, 32, 46, 0.9)',
                        borderColor: '#3E9BFF',
                        textStyle: { color: '#E0E6F0' }
                    },
                    series: [{
                        name: '平均压力',
                        data: [0.41, 0.40, 0.39, 0.38, 0.42, 0.45, 0.44, 0.42, 0.41, 0.43, 0.42, 0.40],
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            color: '#3E9BFF'
                        },
                        lineStyle: {
                            width: 3,
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 1,
                                y2: 0,
                                colorStops: [{
                                    offset: 0, color: '#0095FF'
                                }, {
                                    offset: 1, color: '#00E0FF'
                                }]
                            }
                        },
                        areaStyle: {
                            opacity: 0.2,
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: '#0095FF'
                                }, {
                                    offset: 1, color: 'rgba(0, 149, 255, 0.1)'
                                }]
                            }
                        }
                    }]
                });

                // 流量分布图
                const flowChart = echarts.init(document.getElementById('flow-chart'));
                flowChart.setOption({
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        top: '8%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['0-25', '25-50', '50-75', '75-100', '100-125', '125-150', '150+'],
                        name: '流量范围(m³/h)',
                        nameTextStyle: { color: '#A0A8B8' },
                        axisLine: { lineStyle: { color: '#2A3142' } },
                        axisLabel: { color: '#A0A8B8' }
                    },
                    yAxis: {
                        type: 'value',
                        name: '节点数量',
                        nameTextStyle: { color: '#A0A8B8' },
                        axisLine: { lineStyle: { color: '#2A3142' } },
                        axisLabel: { color: '#A0A8B8' },
                        splitLine: { lineStyle: { color: '#2A3142', opacity: 0.3 } }
                    },
                    tooltip: {
                        trigger: 'item',
                        backgroundColor: 'rgba(26, 32, 46, 0.9)',
                        borderColor: '#00C48C',
                        textStyle: { color: '#E0E6F0' }
                    },
                    series: [{
                        data: [5, 12, 18, 32, 43, 15, 3],
                        type: 'bar',
                        barWidth: '60%',
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: '#00C48C' },
                                { offset: 1, color: '#007451' }
                            ])
                        }
                    }]
                });

                // 窗口大小改变时重新调整图表大小
                window.addEventListener('resize', function () {
                    pressureChart.resize();
                    flowChart.resize();
                });
            }
        };

        // 关闭查看数据弹窗
        document.querySelectorAll('#close-view-data-modal, .view-data-modal .modal-close').forEach(button => {
            button.addEventListener('click', function () {
                document.getElementById('view-data-modal').style.display = 'none';
            });
        });

        // 绑定查看数据按钮事件
        document.querySelectorAll('.task-action.view-data').forEach(button => {
            button.addEventListener('click', function (e) {
                e.stopPropagation(); // 防止事件冒泡
                showViewDataModal();
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // ... existing code ...

            // 数据源类型切换
            const datasourceTypeSelect = document.getElementById('datasource-type');
            if (datasourceTypeSelect) {
                datasourceTypeSelect.addEventListener('change', function() {
                    const selectedValue = this.value;
                    // 隐藏所有配置界面
                    document.querySelectorAll('.datasource-config').forEach(config => {
                        config.style.display = 'none';
                    });
                    
                    // 显示选中的配置界面
                    const selectedConfig = document.getElementById(selectedValue + '-config');
                    if (selectedConfig) {
                        selectedConfig.style.display = 'block';
                    }
                });
            }
            
            // Excel文件来源切换
            const excelSourceToggleOptions = document.querySelectorAll('#excel-config .toggle-option');
            if (excelSourceToggleOptions.length > 0) {
                excelSourceToggleOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        // 移除所有active类
                        excelSourceToggleOptions.forEach(opt => opt.classList.remove('active'));
                        // 添加当前选中项的active类
                        this.classList.add('active');
                        
                        // 隐藏所有配置界面
                        document.querySelectorAll('.excel-source-config').forEach(config => {
                            config.style.display = 'none';
                        });
                        
                        // 显示选中的配置界面
                        const selectedMode = this.getAttribute('data-mode');
                        const selectedConfig = document.getElementById(selectedMode + '-config');
                        if (selectedConfig) {
                            selectedConfig.style.display = 'block';
                        }
                    });
                });
            }
            
            // 文件上传交互
            const excelFileInput = document.getElementById('excel-file');
            const fileInfo = document.querySelector('.file-info');
            if (excelFileInput && fileInfo) {
                excelFileInput.addEventListener('change', function() {
                    if (this.files.length > 0) {
                        const fileName = this.files[0].name;
                        fileInfo.textContent = fileName;
                    } else {
                        fileInfo.textContent = '未选择文件';
                    }
                });
            }
            
            // 工作表选择交互
            const worksheetSelect = document.querySelector('#excel-config .form-row:nth-of-type(4) .form-group:first-child select');
            const worksheetNameInput = document.querySelector('#excel-config .form-row:nth-of-type(4) .form-group:last-child input');
            if (worksheetSelect && worksheetNameInput) {
                worksheetSelect.addEventListener('change', function() {
                    worksheetNameInput.disabled = (this.value !== 'named');
                });
            }

        });

        // 主题切换功能初始化
        document.addEventListener('DOMContentLoaded', function() {
            const themeSwitch = document.querySelector('.theme-switch');
            const htmlRoot = document.documentElement;
            
            // 检查本地存储中的主题设置，默认为暗色主题
            const savedTheme = localStorage.getItem('theme');
            // 只有当明确保存了"light"时才应用亮色主题
            if (savedTheme === 'light') {
                htmlRoot.classList.add('light-theme');
            } else {
                // 如果没有保存或者保存的不是"light"，则确保是暗色主题
                localStorage.setItem('theme', 'dark');
            }
            
            // 切换主题的函数
            function toggleTheme() {
                if (htmlRoot.classList.contains('light-theme')) {
                    htmlRoot.classList.remove('light-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    htmlRoot.classList.add('light-theme');
                    localStorage.setItem('theme', 'light');
                }
                
                // 如果有图表，主题切换后需要重新渲染
                if (typeof echarts !== 'undefined') {
                    // 延迟一点执行，等待CSS变化应用
                    setTimeout(() => {
                        // 找到所有echarts实例并重新调整大小
                        const chartElements = document.querySelectorAll('[id$="-chart"]');
                        chartElements.forEach(element => {
                            const chart = echarts.getInstanceByDom(element);
                            if (chart) {
                                chart.resize();
                            }
                        });
                    }, 200);
                }
            }
            
            // 为主题切换按钮添加点击事件
            themeSwitch.addEventListener('click', toggleTheme);
        });

        // 模拟通知弹出
        document.querySelector('.notification-icon').addEventListener('click', function() {
            alert('通知中心功能将在此处展开');
        });

        // 用户资料弹出
        document.querySelector('.user-profile').addEventListener('click', function() {
            alert('用户个人资料功能将在此处展开');
        });
    </script>
</body>

</html>