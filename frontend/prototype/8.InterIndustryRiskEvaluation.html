<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>行业仿真度评估详情 - 城市级关基级联的网络仿真度评估系统</title>
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- 引入Echarts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入AntV G6图形库 -->
    <script src="https://gw.alipayobjects.com/os/lib/antv/g6/4.8.14/dist/g6.min.js"></script>
    <style>
        :root {
        --primary-color: #0096FF;
        --secondary-color: #00E0FF;
        --background-dark: #0F1520;
        --background-card: #1A202E;
            --card-bg: #1A202E;
        --text-color: #E0E6F0;
        --text-secondary: #A0A8B8;
        --border-color: #2A3142;
        --highlight-color: #3E9BFF;
        --success-color: #00C48C;
        --warning-color: #FFB946;
        --danger-color: #F25767;
        --info-color: #0095FF;
            --add-color: #27ae60;
            --remove-color: #e74c3c;
            --modify-color: #f39c12;
            --progress-bg: rgba(255, 255, 255, 0.05);
        }

        /* 亮色主题变量 */
        :root.light-theme {
            --primary-color: #0078D4;
            --secondary-color: #00A2E0;
            --background-dark: #FFFFFF;
            --background-card: #FFFFFF;
            --card-bg: #F8FAFC;
            --text-color: #333333;
            --text-secondary: #666666;
            --border-color: #E0E6ED;
            --highlight-color: #106EBE;
            --success-color: #0A9D5A;
            --warning-color: #F29D41;
            --danger-color: #E74C3C;
            --info-color: #0078D4;
            --add-color: #27ae60;
            --remove-color: #e74c3c;
            --modify-color: #f39c12;
            --progress-bg: rgba(0, 0, 0, 0.05);
        }

        /* 主题切换开关样式 */
        .theme-switch {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 60px;
            height: 30px;
            background: rgba(0, 149, 255, 0.15);
            border-radius: 30px;
            padding: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .theme-switch:hover {
            transform: scale(1.05);
        }
        
        .theme-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-size: 14px;
        }
        
        .theme-switch-slider {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            left: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        :root.light-theme .theme-switch-slider {
            transform: translateX(30px);
        }

        /* Logo样式 */
        .logo {
	zoom: 110%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 10px;
        }

        .logo-icon {
            font-size: 24px;
            color: var(--primary-color);
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 2px rgba(0, 224, 255, 0.4));
        }

        .logo span {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 0.5px;
            background: linear-gradient(90deg, var(--text-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
        }

        body {
            background-color: var(--background-dark);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            min-height: 100vh;
        }

        /* 顶部导航栏样式 */
        .header {
            background-color: rgba(16, 22, 36, 0.95);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 60px;
            zoom: 110%;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .nav-item {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .nav-link {
            color: var(--text-color);
            text-decoration: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link.active {
            color: var(--primary-color);
        }

        .nav-link.active::after {
        content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 0 0 6px 6px;
            width: max-content;
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            list-style-type: none;
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
        }

        .dropdown-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px;
        }

        .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            padding-left: 20px;
        }

        .dropdown-icon {
            margin-right: 8px;
        }

        .nav-tools {
            display: flex;
            align-items: center;
            gap: 20px;
        }

      .notification-icon,
      .user-profile {
            position: relative;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

      .notification-icon:hover,
      .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-profile img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 24px;
        }

        /* 面包屑导航 */
        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 14px;
            zoom: 110%;
        }

        .breadcrumb a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .breadcrumb a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-secondary);
        }

        .breadcrumb-current {
            color: var(--text-color);
            font-weight: 500;
        }

        /* 任务详情页头部 */
        .task-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .task-detail-title-section {
            flex: 1;
        }

        .task-detail-title {
            font-size: 28px;
            font-weight: 500;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .task-status {
            font-size: 13px;
            padding: 4px 10px;
            border-radius: 4px;
            font-weight: 500;
        }

        .status-pending {
            background-color: rgba(160, 168, 184, 0.15);
            color: var(--text-secondary);
        }

        .status-running {
            background-color: rgba(62, 155, 255, 0.15);
            color: var(--primary-color);
        }

        .status-completed {
            background-color: rgba(0, 196, 140, 0.15);
            color: var(--success-color);
        }

        .status-failed {
            background-color: rgba(242, 87, 103, 0.15);
            color: var(--danger-color);
        }

        .status-paused {
            background-color: rgba(255, 185, 70, 0.15);
            color: var(--warning-color);
        }

        .task-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 10px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .task-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: var(--highlight-color);
        }

        .action-btn.danger {
            color: var(--danger-color);
            border-color: rgba(242, 87, 103, 0.3);
        }

        .action-btn.danger:hover {
            background-color: rgba(242, 87, 103, 0.1);
            border-color: var(--danger-color);
        }

        .action-btn.success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .action-btn.success:hover {
            background-color: rgba(0, 196, 140, 0.8);
        }

        .action-btn.warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
            color: white;
        }

        .action-btn.warning:hover {
            background-color: rgba(255, 185, 70, 0.8);
        }

        /* 标签样式 */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 16px;
        }

        .tag {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            background-color: rgba(62, 155, 255, 0.1);
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid rgba(62, 155, 255, 0.2);
        }

        /* 任务详情主内容 */
        .task-detail-content {
            display: flex;
            gap: 24px;
        }

        .task-detail-main {
            flex: 1;
        }

        .task-detail-sidebar {
            width: 320px;
        }

        /* 卡片和面板通用样式 */
        .card {
            background-color: var(--background-card);
            border-radius: 10px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .card-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .card-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .card-action:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
        }

        .card-body {
            padding: 20px;
        }

        /* 导航选项卡 */
        .detail-nav-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            overflow-x: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
            zoom: 110%;
        }

        .detail-nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .detail-nav-tab {
            padding: 12px 20px;
            font-size: 14px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-nav-tab:hover {
            color: var(--text-color);
        }

        .detail-nav-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        /* 详情内容区域 */
        .detail-content-section {
            margin-bottom: 24px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-actions {
            display: flex;
            gap: 8px;
        }

        /* 描述区域 */
        .description-section {
            padding: 16px;
            background-color: rgba(255, 255, 255, 0.02);
            border-radius: 8px;
            border-left: 3px solid var(--primary-color);
            margin-bottom: 24px;
        }

        .description-text {
            color: var(--text-color);
            font-size: 14px;
            line-height: 1.6;
        }

        /* 进度指示器样式 */
        .progress-container {
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: var(--progress-bg);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        /* 树形进度监控样式 */
        .tree-monitor {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
            zoom: 110%;
        }

        .tree-toolbar {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.02);
            border-bottom: 1px solid var(--border-color);
        }

        .tree-container {
            padding: 15px;
            overflow-y: auto;
        }

        .tree-node {
            margin-bottom: 8px;
        }

        .node-content {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 10px;
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            position: relative;
        }

        .node-content:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .node-collapse {
            cursor: pointer;
            width: 20px;
            display: flex;
            justify-content: center;
        }

        .node-icon {
            color: var(--primary-color);
            font-size: 14px;
        }

        .node-title {
            flex: 1;
        }

        .node-weight {
            margin-right: 10px;
            font-size: 12px;
            color: var(--text-secondary);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 10px;
        }

        .node-progress {
            width: 100px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .node-progress-bar {
            width: 100%;
            height: 4px;
            background-color: var(--progress-bg);
            border-radius: 2px;
            overflow: hidden;
        }

        .node-progress-fill {
            height: 100%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .node-progress-data {
            background-color: var(--info-color);
        }

        .node-progress-calc {
            background-color: var(--success-color);
        }

        .node-progress-label {
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            color: var(--text-secondary);
        }

        .node-result {
            margin-left: 10px;
            font-size: 14px;
            font-weight: 500;
            color: var(--primary-color);
            background-color: rgba(62, 155, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            min-width: 45px;
            text-align: center;
        }

        .node-children {
            margin-left: 25px;
            padding-left: 15px;
            border-left: 1px dashed var(--border-color);
            margin-top: 8px;
        }

        /* 实时日志区域 */
        .log-container {
            background-color: rgba(16, 22, 36, 0.6);
            border-radius: 6px;
            border: 1px solid var(--border-color);
            height: 300px;
            overflow-y: auto;
            padding: 12px;
        font-family: "Courier New", monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .log-line {
            margin-bottom: 4px;
        }

        .log-timestamp {
            color: var(--text-secondary);
            margin-right: 8px;
        }

        .log-level-info {
            color: var(--info-color);
            margin-right: 8px;
        }

        .log-level-warning {
            color: var(--warning-color);
            margin-right: 8px;
        }

        .log-level-error {
            color: var(--danger-color);
            margin-right: 8px;
        }

        .log-level-success {
            color: var(--success-color);
            margin-right: 8px;
        }

        /* 数据集项样式 */
        .dataset-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .dataset-item:last-child {
            border-bottom: none;
        }

        .dataset-item:hover {
            background-color: rgba(255, 255, 255, 0.02);
        }

        .dataset-icon {
            width: 36px;
            height: 36px;
            border-radius: 6px;
            background-color: rgba(62, 155, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            margin-right: 12px;
        }

        .dataset-info {
            flex: 1;
        }

        .dataset-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 2px;
        }

        .dataset-meta {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            gap: 12px;
        }

        .dataset-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: auto;
        }

        /* 模态窗口通用样式 */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 2000;
            animation: fadeIn 0.3s ease;
        }

        .modal {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid var(--border-color);
            overflow: hidden;
            max-width: 90%;
            width: 600px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 500;
        }

        .modal-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 22px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            color: var(--text-color);
        }

        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: var(--background-card);
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--highlight-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: rgba(0, 196, 140, 0.8);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: rgba(242, 87, 103, 0.8);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background-color: rgba(255, 185, 70, 0.8);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: var(--border-color);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }

        /* 测评报告结果卡片 */
        .result-summary-card {
            background-color: rgba(62, 155, 255, 0.05);
            border: 1px solid rgba(62, 155, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .result-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .result-score {
            font-size: 32px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .result-grade {
            font-size: 14px;
            padding: 4px 12px;
            border-radius: 20px;
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
            font-weight: 500;
        }

        .result-metrics {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 15px;
        }

        .result-metric {
            text-align: center;
            flex: 1;
            min-width: 100px;
        }

        .metric-value {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .form-sublabel {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

      .form-input,
      .form-select,
      .form-textarea {
            width: 100%;
            padding: 10px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

      .form-input:focus,
      .form-select:focus,
      .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.08);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-col {
            flex: 1;
        }

        /* 动画效果 */
        @keyframes fadeIn {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
        }

        @keyframes slideIn {
        from {
          transform: translate(-50%, -60%);
          opacity: 0;
        }

        to {
          transform: translate(-50%, -50%);
          opacity: 1;
        }
        }

        /* 响应式设计 */
        @media screen and (max-width: 1200px) {
            .task-detail-content {
                flex-direction: column;
            }
            
            .task-detail-sidebar {
                width: 100%;
            }
        }

        @media screen and (max-width: 768px) {
            .task-detail-header {
                flex-direction: column;
                gap: 16px;
            }
            
            .task-actions {
                width: 100%;
                overflow-x: auto;
                padding-bottom: 8px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 20px;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        .custom-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: var(--card-bg);
            border-radius: 8px;
            overflow: hidden;
        }

        .custom-table thead {
            background: var(--table-header-bg, rgba(255, 255, 255, 0.05));
        }

        .custom-table th {
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        }

        .custom-table td {
            padding: 12px 15px;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        }

        .custom-table tbody tr:hover {
            background: var(--table-hover-bg, rgba(255, 255, 255, 0.03));
        }

        .custom-table tbody tr:last-child td {
            border-bottom: none;
        }

        .custom-table i {
            margin-right: 8px;
        color: var(--primary-color, #0096ff);
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-success {
            background: rgba(0, 196, 140, 0.2);
        color: #00c48c;
        }

        .badge-warning {
            background: rgba(255, 169, 64, 0.2);
        color: #ffa940;
        }

        .badge-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.6);
        }

        .report-versions {
            margin-top: 20px;
        }

        .version-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
            padding-bottom: 10px;
        }

        .version-tabs .tab {
            padding: 8px 16px;
            cursor: pointer;
            border-radius: 4px;
            background: var(--card-bg);
            transition: all 0.3s ease;
        }

        .version-tabs .tab:hover {
            background: var(--table-hover-bg, rgba(255, 255, 255, 0.03));
        }

        .version-tabs .tab.active {
        background: var(--primary-color, #0096ff);
            color: white;
        }

        .version-number {
            font-weight: 600;
            margin-right: 8px;
        }

        .version-date {
            font-size: 12px;
            opacity: 0.8;
        }

        .version-content {
            position: relative;
        }

        .version-info {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .version-info.active {
            display: block;
        }

        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .version-actions {
            display: flex;
            gap: 10px;
        }

        .version-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .detail-item {
            display: flex;
            align-items: center;
        }

        .detail-label {
            color: var(--text-color);
            opacity: 0.7;
            margin-right: 8px;
        }

        .version-changes {
            background: var(--card-bg);
            padding: 15px;
            border-radius: 8px;
        }

        .version-changes h5 {
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .version-changes ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .version-changes li {
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        }

        .version-changes li:last-child {
            border-bottom: none;
        }

        @keyframes fadeIn {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
        }

        .simulation-score {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .score-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .score-header h5 {
            margin: 0;
            color: var(--text-color);
        }

        .score-value {
            font-size: 24px;
            font-weight: 600;
        color: var(--primary-color, #0096ff);
        }

        .score-progress {
            margin-bottom: 20px;
        }

        .progress-bar {
            height: 8px;
            background: var(--table-header-bg, rgba(255, 255, 255, 0.05));
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
        background: var(--primary-color, #0096ff);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .score-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .score-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        }

        .score-item:last-child {
            border-bottom: none;
        }

        .score-label {
            color: var(--text-color);
            opacity: 0.8;
        }

        .score-number {
            font-weight: 500;
            color: var(--text-color);
        }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
      }

      /* 思维导图相关样式 */
      #mind-map-container {
        width: 100%;
        height: 800px;
        background-color: var(--background-card);
        border-radius: 18px;
        position: relative;
        overflow: hidden;
        border: 1.5px solid var(--border-color);
        box-shadow: 0 8px 32px rgba(0,0,0,0.10), 0 1.5px 6px rgba(0,150,255,0.06);
      }

      .mind-map-toolbar {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
        display: flex;
        gap: 5px;
        background-color: rgba(26, 32, 46, 0.8);
        padding: 5px;
            border-radius: 8px;
        border: 1px solid var(--border-color);
      }

      .mind-map-btn {
        padding: 5px 10px;
        border-radius: 4px;
        border: 1px solid var(--border-color);
        background-color: var(--background-card);
        color: var(--text-color);
        cursor: pointer;
        font-size: 13px;
        display: flex;
        align-items: center;
        gap: 5px;
        transition: all 0.2s ease;
      }

      .mind-map-btn:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      .mind-map-btn.primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
      }

      .mind-map-btn.primary:hover {
        background-color: var(--highlight-color);
      }

      .mind-map-btn.danger {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
        color: white;
      }

      .mind-map-btn.danger:hover {
        background-color: rgba(242, 87, 103, 0.8);
      }

      .node-tooltip {
        position: absolute;
        padding: 10px;
        background-color: rgba(16, 22, 36, 0.95);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        z-index: 999;
        font-size: 13px;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s;
        max-width: 300px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
      }

      .node-form {
        padding: 15px;
        background-color: var(--background-card);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        position: absolute;
        z-index: 100;
        width: 300px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      }

      .node-form label {
        display: block;
        margin-bottom: 5px;
        font-size: 14px;
      }

      .node-form input,
      .node-form select {
        width: 100%;
        padding: 8px;
        margin-bottom: 10px;
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 4px;
        color: var(--text-color);
      }

      .node-form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        margin-top: 10px;
      }

      .node-indicator {
        position: absolute;
        top: 0;
        right: 0;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        }

        /* 关联任务卡片样式 */
        .related-tasks-container {
            margin-top: 30px;
            border-top: 1px solid var(--border-color);
            padding-top: 20px;
            zoom: 110%;
        }

        .related-tasks-container .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .related-tasks-container .section-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .related-tasks-container .section-subtitle {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .task-card {
            background-color: var(--background-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .task-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        }

        .task-card-header {
            padding: 12px 16px;
            background-color: rgba(16, 22, 36, 0.5);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .task-card-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .task-card-status .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-running {
            background-color: rgba(0, 196, 140, 0.15);
            color: var(--success-color);
        }

        .status-waiting {
            background-color: rgba(255, 185, 70, 0.15);
            color: var(--warning-color);
        }

        .status-completed {
            background-color: rgba(62, 155, 255, 0.15);
            color: var(--highlight-color);
        }

        .status-error {
            background-color: rgba(242, 87, 103, 0.15);
            color: var(--danger-color);
        }

        .task-card-content {
            padding: 16px;
        }

        .task-info-item {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .task-info-label {
            width: 120px;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .task-info-value {
            flex: 1;
            font-size: 14px;
        }

        .task-progress {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .task-progress-bar {
            flex: 1;
            height: 6px;
            background-color: var(--progress-bg);
            border-radius: 3px;
            overflow: hidden;
        }

        .task-progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .task-progress-value {
            font-size: 14px;
            min-width: 40px;
            text-align: right;
        }

        .task-card-footer {
            padding: 12px 16px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 新增布局样式 */
        .mindmap-container-wrapper {
            display: flex;
            gap: 20px;
        }
        
        .mindmap-main-area {
            flex: 1;
            min-width: 0;
            /* 防止弹性项目溢出 */
        }
        
        .related-tasks-container {
            width: 350px;
            margin-top: 0;
            padding-top: 0;
            border-top: none;
            border-left: 1px solid var(--border-color);
            padding-left: 20px;
        }

        /* 新增的内部子选项卡导航样式 */
        .results-tab-navigation {
            display: flex;
            background-color: rgba(16, 22, 36, 0.5);
            border-radius: 8px 8px 0 0;
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .results-tab {
            padding: 12px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .results-tab:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .results-tab.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .results-tab-content {
            display: none;
        }
        
        .results-tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        /* 报告大纲样式 */
        .report-outline {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .outline-item {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .outline-header {
            padding: 12px 15px;
            background-color: rgba(16, 22, 36, 0.6);
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .outline-content {
            padding: 15px;
            background-color: rgba(16, 22, 36, 0.3);
            color: var(--text-secondary);
            font-size: 14px;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }
        
        /* 版本切换样式 */
        .report-versions {
            margin-top: 20px;
        }

        .version-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
            padding-bottom: 10px;
        }

        .version-tabs .tab {
            padding: 8px 16px;
            cursor: pointer;
            border-radius: 4px;
            background: var(--card-bg);
            transition: all 0.3s ease;
        }

        .version-tabs .tab:hover {
            background: var(--table-hover-bg, rgba(255, 255, 255, 0.03));
        }

        .version-tabs .tab.active {
        background: var(--primary-color, #0096ff);
            color: white;
        }

        .version-number {
            font-weight: 600;
            margin-right: 8px;
        }

        .version-date {
            font-size: 12px;
            opacity: 0.8;
        }

        .version-content {
            position: relative;
        }

        .version-info {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .version-info.active {
            display: block;
        }

        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .version-actions {
            display: flex;
            gap: 10px;
        }

        .version-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .detail-item {
            display: flex;
            align-items: center;
        }

        .detail-label {
            color: var(--text-color);
            opacity: 0.7;
            margin-right: 8px;
        }

        /* 亮色主题的导航栏调整 */
        :root.light-theme .header {
            background-color: rgba(245, 247, 250, 0.95);
            border-bottom: 1px solid var(--border-color);
        }

        :root.light-theme .nav-link:hover {
            color: var(--primary-color);
        }

        :root.light-theme .dropdown-menu {
            background-color: var(--background-card);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        :root.light-theme .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }

        /* 亮色主题的卡片调整 */
        :root.light-theme .dashboard-card,
        :root.light-theme .evaluation-card,
        :root.light-theme .detail-card {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
        }

        :root.light-theme .card-header {
            border-bottom: 1px solid var(--border-color);
        }

        :root.light-theme .card-action:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }

        /* 亮色主题的表单元素 */
        :root.light-theme .form-control,
        :root.light-theme .form-select {
            background-color: #F8FAFD;
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }

        :root.light-theme .form-control:focus,
        :root.light-theme .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.25);
        }

        /* 亮色主题的按钮 */
        :root.light-theme .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        :root.light-theme .btn-primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }

        :root.light-theme .btn-secondary {
            background-color: #F0F2F5;
            color: var(--text-color);
        }

        :root.light-theme .btn-secondary:hover {
            background-color: #E8EBF0;
        }

        :root.light-theme .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* 亮色主题的表格 */
        :root.light-theme table {
            border-color: var(--border-color);
        }

        :root.light-theme th {
            background-color: #F5F7FA;
        }

        :root.light-theme tr:hover {
            background-color: rgba(0, 120, 212, 0.05);
        }

        /* 亮色主题的选项卡 */
        :root.light-theme .tabs-nav {
            border-bottom-color: var(--border-color);
        }

        :root.light-theme .tab-item {
            color: var(--text-secondary);
        }

        :root.light-theme .tab-item.active {
            color: var(--primary-color);
        }

        :root.light-theme .tab-item.active::after {
            background-color: var(--primary-color);
        }

        /* 亮色主题的滚动条 */
        :root.light-theme ::-webkit-scrollbar-track {
            background: #F5F7FA;
        }

        :root.light-theme ::-webkit-scrollbar-thumb {
            background: #D0D6E0;
        }

        :root.light-theme ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 亮色主题的图表调整 */
        :root.light-theme .echarts-container canvas {
            background-color: var(--background-card);
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        .custom-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: var(--card-bg);
            border-radius: 8px;
            overflow: hidden;
        }

        .custom-table thead {
            background: var(--table-header-bg, rgba(255, 255, 255, 0.05));
        }

        .custom-table th {
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        }

        .custom-table td {
            padding: 12px 15px;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        }

        .custom-table tbody tr:hover {
            background: var(--table-hover-bg, rgba(255, 255, 255, 0.03));
        }

        .custom-table tbody tr:last-child td {
            border-bottom: none;
        }

        .custom-table i {
            margin-right: 8px;
        color: var(--primary-color, #0096ff);
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-success {
            background: rgba(0, 196, 140, 0.2);
        color: #00c48c;
        }

        .badge-warning {
            background: rgba(255, 169, 64, 0.2);
        color: #ffa940;
        }

        .badge-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.6);
        }

        .report-versions {
            margin-top: 20px;
        }

        .version-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
            padding-bottom: 10px;
        }

        .version-tabs .tab {
            padding: 8px 16px;
            cursor: pointer;
            border-radius: 4px;
            background: var(--card-bg);
            transition: all 0.3s ease;
        }

        .version-tabs .tab:hover {
            background: var(--table-hover-bg, rgba(255, 255, 255, 0.03));
        }

        .version-tabs .tab.active {
        background: var(--primary-color, #0096ff);
            color: white;
        }

        .version-number {
            font-weight: 600;
            margin-right: 8px;
        }

        .version-date {
            font-size: 12px;
            opacity: 0.8;
        }

        .version-content {
            position: relative;
        }

        .version-info {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .version-info.active {
            display: block;
        }

        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .version-actions {
            display: flex;
            gap: 10px;
        }

        .version-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .detail-item {
            display: flex;
            align-items: center;
        }

        .detail-label {
            color: var(--text-color);
            opacity: 0.7;
            margin-right: 8px;
        }

        /* 高亮节点样式 */
        /* .node-content.highlighted {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.15), rgba(255, 140, 66, 0.08));
            border: 2px solid #FF6B35;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
            position: relative;
            animation: highlight-pulse 2s ease-in-out infinite;
        }

        .node-content.highlighted::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #FF6B35, #FF8C42, #FFB366, #FF6B35);
            border-radius: 8px;
            z-index: -1;
            animation: border-rotate 3s linear infinite;
        }

        .node-content.highlighted .node-title {
            color: #FFE5D1;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        }

        .node-content.highlighted .node-weight {
            background-color: rgba(255, 107, 53, 0.2);
            color: #FFE5D1;
            border: 1px solid #FF6B35;
        }

        .node-content.highlighted .node-result {
            background-color: rgba(255, 107, 53, 0.2);
            color: #FF6B35;
            border: 1px solid #FF6B35;
        }

        .node-content.highlighted .node-progress-data {
            background-color: #FF8C42;
        }

        .node-content.highlighted .node-progress-calc {
            background-color: #FF6B35;
        }

        .node-content.highlighted .node-progress-label {
            color: #FFE5D1;
        }

        .node-content.highlighted .node-icon {
            color: #FF6B35;
            text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        } */

        @keyframes highlight-pulse {

            0%,
            100% {
                box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
            }

            50% {
                box-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
            }
        }

        @keyframes border-rotate {
            0% {
                background: linear-gradient(45deg, #FF6B35, #FF8C42, #FFB366, #FF6B35);
            }

            25% {
                background: linear-gradient(45deg, #FF8C42, #FFB366, #FF6B35, #FF8C42);
            }

            50% {
                background: linear-gradient(45deg, #FFB366, #FF6B35, #FF8C42, #FFB366);
            }

            75% {
                background: linear-gradient(45deg, #FF6B35, #FF8C42, #FFB366, #FF6B35);
            }

            100% {
                background: linear-gradient(45deg, #FF6B35, #FF8C42, #FFB366, #FF6B35);
            }
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.8);
            display: none;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .modal-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal-content {
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            width: calc(100vw - 40px);
            height: calc(100vh - 40px);
            max-width: calc(100vw - 40px);
            max-height: calc(100vh - 40px);
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--background-card);
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 16px;
        }

        .modal-close:hover {
            background-color: var(--border-color);
            color: var(--text-color);
            transform: scale(1.1);
        }

        .modal-body {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .modal-iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }

        /* 动画效果 */
        .modal-overlay.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        .modal-overlay.show .modal-content {
            animation: modalSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-overlay.hide {
            animation: fadeOut 0.3s ease;
        }

        .modal-overlay.hide .modal-content {
            animation: modalSlideOut 0.3s ease;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }

        @keyframes modalSlideOut {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.8) translateY(-20px);
            }
        }
    </style>
    
<body>
    <div class="container">

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="dashboard.html"><i class="fas fa-home"></i> 首页</a>
                <span class="breadcrumb-separator">/</span>
                <a href="eval-task-list.html">评估任务列表</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">通信-交通行业级联风险仿真度评估
            </div>

            <!-- 评估任务详情页头部 -->
            <div class="task-detail-header">
                <div class="task-detail-title-section">
                    <div class="task-detail-title">
                        <span>通信-交通行业级联风险仿真度评估</span>
                        <span class="scenario-badge"
                            style="background-color: #FF7700; color: white; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: 500; margin-left: 10px;">仿真场景：通信-交通级联风险场景</span>
                        <span class="task-status status-running">进行中</span>
                    </div>
                    <div class="task-meta">
                        <div class="meta-item">
                            <i class="fas fa-layer-group"></i>
                            <span>测评类型: 跨行业综合测评</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-cube"></i>
                            <span>使用模版: 城市级联关基行业综合仿真度评估模版</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>创建日期: 2025-04-05</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>开始时间: 2025-04-09 09:15:32</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-user"></i>
                            <span>负责人: 王五</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-spinner"></i>
                            <span>进度: 68%</span>
                        </div>
                    </div>
                    <div class="tags-container">
              <div class="tag">城市X</div>
              <div class="tag">跨行业仿真</div>
              <div class="tag">电力-通信-交通</div>
              <div class="tag">关键基础设施</div>
              <div class="tag">级联故障分析</div>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="action-btn warning" id="pauseTaskBtn">
                        <i class="fas fa-pause"></i> 暂停任务
                    </button>
                    <button class="action-btn danger" id="stopTaskBtn">
                        <i class="fas fa-stop"></i> 停止任务
                    </button>
                    <button class="action-btn" id="saveAsTemplateBtn">
                        <i class="fas fa-save"></i> 保存为模版
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-file-export"></i> 导出报告
                    </button>
                </div>
            </div>

            <!-- 总体进度指示器 -->
            <div class="progress-container">
                <div class="progress-bar">
            <div class="progress-fill" style="width: 68%"></div>
                </div>
                <div class="progress-label">
                    <span>总体进度: 68%</span>
                    <span>预计完成时间: 2025-04-09 14:30:00</span>
                </div>
            </div>

            <!-- 评估任务详情主内容 -->
            <div class="task-detail-content">
                <!-- 左侧主要内容区域 -->
                <div class="task-detail-main">
                    <!-- 导航选项卡 -->
                    <div class="detail-nav-tabs">
              <div class="detail-nav-tab active" data-tab="mindmap">
                        <i class="fas fa-sitemap"></i> 指标体系
              </div>
              <div class="detail-nav-tab" data-tab="progress">
                            <i class="fas fa-tasks"></i> 数据采集进度
                        </div>
                        <div class="detail-nav-tab" data-tab="calculate">
                            <i class="fas fa-tasks"></i> 指标计算进度
                        </div>
                        <div class="detail-nav-tab" data-tab="results">
                        <i class="fas fa-chart-bar"></i> 评估结果与报告
                        </div>
                        <div class="detail-nav-tab" data-tab="logs">
                            <i class="fas fa-list-alt"></i> 执行日志
                        </div>
                        <div class="detail-nav-tab" data-tab="settings">
                            <i class="fas fa-cog"></i> 任务配置
                        </div>
                    </div>

            <!-- 思维导图内容 -->
            <div class="tab-content active" id="mindmap-content">
              <div class="detail-content-section">
                <div class="mindmap-container-wrapper">
                  <div class="mindmap-main-area">
                <div class="section-header">
                  <h3 class="section-title"></h3>
                  <div class="section-actions">
                    <button class="btn" id="backToMindMapBtn" style="display:none;">
                      <i class="fas fa-arrow-left"></i> 返回
                    </button>
                    <button class="btn" id="zoomInBtn">
                      <i class="fas fa-search-plus"></i> 放大
                    </button>
                    <button class="btn" id="zoomOutBtn">
                      <i class="fas fa-search-minus"></i> 缩小
                    </button>
                    <button class="btn" id="fitViewBtn">
                      <i class="fas fa-expand"></i> 适应画布
                    </button>
                    <button class="btn" id="saveLayoutBtn">
                      <i class="fas fa-save"></i> 保存布局
                    </button>
                    <button class="btn btn-primary" id="addNodeBtn">
                      <i class="fas fa-plus"></i> 添加指标
                    </button>
                  </div>
                </div>
                <div id="mind-map-container">
                  <!-- 思维导图画布将在此处渲染 -->
                </div>
                <!-- 节点提示框 -->
                                    <div class="node-tooltip" id="nodeTooltip" style="display: none"></div>
                <!-- 节点编辑表单 -->
                <div class="node-form" id="nodeForm" style="display: none">
                  <input type="hidden" id="nodeId" />
                  <div class="form-group">
                    <label>指标名称</label>
                    <input type="text" id="nodeName" />
                  </div>
                  <div class="form-group">
                    <label>权重 (%)</label>
                    <input type="number" id="nodeWeight" min="0" max="100" />
                  </div>
                  <div class="form-group">
                    <label>节点类型</label>
                    <select id="nodeType" class="form-select">
                      <option value="composite">复合节点</option>
                      <option value="normal">普通节点</option>
                    </select>
                  </div>
                  <div id="leafNodeFields" style="display: none">
                  </div>
                  <div class="form-group">
                    <label>指标详细描述</label>
                    <textarea id="nodeDescription" class="form-textarea" placeholder="请输入该指标的详细描述"></textarea>
                  </div>
                  <div class="node-form-actions">
                    <button class="mind-map-btn" id="cancelNodeEdit">
                      <i class="fas fa-times"></i> 取消
                    </button>
                    <button class="mind-map-btn primary" id="saveNodeEdit">
                      <i class="fas fa-check"></i> 保存
                    </button>
                      </div>
                    </div>
                  </div>

                  <!-- 关联任务卡片区域 - 右侧显示 -->
                  <div class="related-tasks-container" id="relatedTasksContainer">
                    <div class="section-header">
                      <h3 class="section-title">
                        <i class="fas fa-link"></i> 关联任务
                      </h3>
                      <div class="section-subtitle" id="currentMetricName">
                        当前指标：未选择
                      </div>
                    </div>
                    
                    <!-- 关联的采集任务卡片 -->
                    <div class="task-card" id="collectionTaskCard">
                      <div class="task-card-header">
                        <div class="task-card-title">
                          <i class="fas fa-database"></i> 关联的采集任务
                        </div>
                        <div class="task-card-status">
                          <span class="status-badge status-running">运行中</span>
                        </div>
                      </div>
                      <div class="task-card-content">
                        <div class="task-info-item">
                          <span class="task-info-label">任务名称：</span>
                          <span class="task-info-value" id="collectionTaskName">配电网设备参数采集任务</span>
                        </div>
                        <div class="task-info-item">
                          <span class="task-info-label">采集类型：</span>
                          <span class="task-info-value" id="collectionTaskType">实时数据采集</span>
                        </div>
                        <div class="task-info-item">
                          <span class="task-info-label">采集进度：</span>
                          <div class="task-progress">
                            <div class="task-progress-bar">
                                                        <div class="task-progress-fill" id="collectionTaskProgress"
                                                            style="width: 75%"></div>
                            </div>
                                                    <span class="task-progress-value"
                                                        id="collectionTaskProgressValue">75%</span>
                          </div>
                        </div>
                        <div class="task-info-item">
                          <span class="task-info-label">开始时间：</span>
                                                <span class="task-info-value" id="collectionTaskStartTime">2024-04-01
                                                    08:30:00</span>
                        </div>
                        <div class="task-info-item">
                          <span class="task-info-label">预计完成时间：</span>
                                                <span class="task-info-value" id="collectionTaskEndTime">2024-04-09
                                                    18:00:00</span>
                        </div>
                      </div>
                      <div class="task-card-footer">
                        <button class="btn btn-sm">
                          <i class="fas fa-eye"></i> 查看详情
                        </button>
                        <button class="btn btn-sm">
                          <i class="fas fa-pause"></i> 暂停任务
                        </button>
                      </div>
                    </div>
                    
                    <!-- 关联的计算任务卡片 -->
                    <div class="task-card" id="computationTaskCard">
                      <div class="task-card-header">
                        <div class="task-card-title">
                          <i class="fas fa-calculator"></i> 关联的计算任务
                        </div>
                        <div class="task-card-status">
                          <span class="status-badge status-waiting">等待中</span>
                        </div>
                      </div>
                      <div class="task-card-content">
                        <div class="task-info-item">
                          <span class="task-info-label">任务名称：</span>
                          <span class="task-info-value" id="computationTaskName">拓扑结构一致性计算</span>
                        </div>
                        <div class="task-info-item">
                          <span class="task-info-label">计算类型：</span>
                          <span class="task-info-value" id="computationTaskType">模型比对</span>
                        </div>
                        <div class="task-info-item">
                          <span class="task-info-label">计算进度：</span>
                          <div class="task-progress">
                            <div class="task-progress-bar">
                                                        <div class="task-progress-fill" id="computationTaskProgress"
                                                            style="width: 40%"></div>
                            </div>
                                                    <span class="task-progress-value"
                                                        id="computationTaskProgressValue">40%</span>
                          </div>
                        </div>
                        <div class="task-info-item">
                          <span class="task-info-label">开始时间：</span>
                                                <span class="task-info-value" id="computationTaskStartTime">2024-04-02
                                                    10:15:00</span>
                        </div>
                        <div class="task-info-item">
                          <span class="task-info-label">预计完成时间：</span>
                                                <span class="task-info-value" id="computationTaskEndTime">2024-04-08
                                                    16:30:00</span>
                        </div>
                      </div>
                      <div class="task-card-footer">
                        <button class="btn btn-sm" id="viewComputationTaskDetail">
                          <i class="fas fa-eye"></i> 查看详情
                        </button>
                        <button class="btn btn-sm btn-primary">
                          <i class="fas fa-play"></i> 启动任务
                        </button>
                      </div>
                    </div>
                  </div>
                  <!-- 算法库选择配置区域，初始隐藏 -->
                  <div class="algorithm-config-container" id="algorithmConfigContainer" style="display:none;">
                    <div class="section-header">
                      <h3 class="section-title"><i class="fas fa-cogs"></i> 算法库选择配置</h3>
                    </div>
                    <div class="card">
                      <div class="card-body">
                        <div class="form-group">
                          <label class="form-label">选择算法</label>
                          <select class="form-select" id="algorithmSelect">
                            <option value="alg1">算法A</option>
                            <option value="alg2">算法B</option>
                            <option value="alg3">算法C</option>
                          </select>
                        </div>
                        <div class="form-group">
                          <label class="form-label">参数配置</label>
                          <input type="text" class="form-input" id="algorithmParams" placeholder="请输入参数" />
                        </div>
                        <button class="btn btn-primary" id="saveAlgorithmConfigBtn"><i class="fas fa-save"></i> 保存配置</button>
                      </div>
                    </div>
                  </div>
                </div>
                        </div>
                    </div>

                    <!-- 任务进度内容 -->
                    <div class="tab-content" id="progress-content" style="display: none">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-sitemap"></i>
                                    <span>指标评估进度</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-expand-alt"></i> 展开全部
                                    </button>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-sync-alt"></i> 刷新
                                    </button>
                                </div>
                            </div>
                            <div class="tree-monitor">
                                <div class="tree-container">
                                    <!-- 根指标：电力系统仿真度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse">
                                                <i class="fas fa-caret-down"></i>
                                            </div>
                                            <div class="node-icon"><i class="fas fa-bolt"></i></div>
                                            <div class="node-title">电力系统仿真度</div>
                                            <div class="node-result">-</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-project-diagram"></i>
                                                    </div>
                                                    <div class="node-title">配电网拓扑结构精度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-title">负荷预测精度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                    </div>
                                                    <div class="node-title">电网故障响应特性</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 根指标：通信网络仿真度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse">
                                                <i class="fas fa-caret-down"></i>
                                            </div>
                                            <div class="node-icon">
                                                <i class="fas fa-broadcast-tower"></i>
                                            </div>
                                            <div class="node-title">通信网络仿真度</div>
                                            <div class="node-result">-</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-title">通信网络容量仿真度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-hourglass-half"></i>
                                                    </div>
                                                    <div class="node-title">通信时延仿真度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-wifi"></i>
                                                    </div>
                                                    <div class="node-title">通信网络覆盖精度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 根指标：交通系统仿真度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse">
                                                <i class="fas fa-caret-down"></i>
                                            </div>
                                            <div class="node-icon"><i class="fas fa-road"></i></div>
                                            <div class="node-title">交通系统仿真度</div>
                                            <div class="node-result">-</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-title">交通流量仿真度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-traffic-light"></i>
                                                    </div>
                                                    <div class="node-title">交通信号控制精度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-map-marked-alt"></i>
                                                    </div>
                                                    <div class="node-title">道路拥堵状态精度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 根指标：跨行业交互影响仿真度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse">
                                                <i class="fas fa-caret-down"></i>
                                            </div>
                                            <div class="node-icon">
                                                <i class="fas fa-exchange-alt"></i>
                                            </div>
                                            <div class="node-title">跨行业交互影响仿真度</div>
                                            <div class="node-progress">
                                                <div class="node-progress-bar">
                                                    <div class="node-progress-fill node-progress-data"
                                                        style="width: 78%"></div>
                                                </div>
                                                <div class="node-progress-label">
                                                    <span>数据采集: 78%</span>
                                                </div>
                                            </div>
                                            <div class="node-result">-</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-plug"></i>
                                                    </div>
                                                    <div class="node-title">电力-通信依赖关系</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-network-wired"></i>
                                                    </div>
                                                    <div class="node-title">通信-交通依赖关系</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-right"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-charging-station"></i>
                                                    </div>
                                                    <div class="node-title">交通-电力依赖关系</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-data"
                                                                style="width: 35%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>数据采集: 35%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content highlighted">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-down"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-route"></i>
                                                    </div>
                                                    <div class="node-title">风险传播路径相似度</div>
                                                    <div class="node-result">-</div>
                                                </div>
                                                <div class="node-children">
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="node-collapse">
                                                                <i class="fas fa-caret-right"></i>
                                                            </div>
                                                            <div class="node-icon">
                                                                <i class="fas fa-layer-group"></i>
                                                            </div>
                                                            <div class="node-title">风险级联层数匹配度</div>
                                                            <div class="node-progress">
                                                                <div class="node-progress-bar">
                                                                    <div class="node-progress-fill node-progress-data"
                                                                        style="width: 80%"></div>
                                                                </div>
                                                                <div class="node-progress-label">
                                                                    <span>数据采集: 80%</span>
                                                                </div>
                                                            </div>
                                                            <div class="node-result">-</div>
                                                        </div>
                                                    </div>
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="node-collapse">
                                                                <i class="fas fa-caret-right"></i>
                                                            </div>
                                                            <div class="node-icon">
                                                                <i class="fas fa-cubes"></i>
                                                            </div>
                                                            <div class="node-title">风险关联资产匹配度</div>
                                                            <div class="node-progress">
                                                                <div class="node-progress-bar">
                                                                    <div class="node-progress-fill node-progress-data"
                                                                        style="width: 70%"></div>
                                                                </div>
                                                                <div class="node-progress-label">
                                                                    <span>数据采集: 70%</span>
                                                                </div>
                                                            </div>
                                                            <div class="node-result">-</div>
                                                        </div>
                                                    </div>
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="node-collapse">
                                                                <i class="fas fa-caret-right"></i>
                                                            </div>
                                                            <div class="node-icon">
                                                                <i class="fas fa-clock"></i>
                                                            </div>
                                                            <div class="node-title">风险传播时序一致性</div>
                                                            <div class="node-progress">
                                                                <div class="node-progress-bar">
                                                                    <div class="node-progress-fill node-progress-data"
                                                                        style="width: 45%"></div>
                                                                </div>
                                                                <div class="node-progress-label">
                                                                    <span>数据采集: 45%</span>
                                                                </div>
                                                            </div>
                                                            <div class="node-result">-</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 计算进度内容 -->
                    <div class="tab-content" id="calculate-content" style="display: none">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-sitemap"></i>
                                    <span>指标评估进度</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-expand-alt"></i> 展开全部
                                    </button>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-sync-alt"></i> 刷新
                                    </button>
                                </div>
                            </div>
                            <div class="tree-monitor">
                                <div class="tree-container">
                                    <!-- 根指标：电力系统仿真度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                        <div class="node-collapse">
                          <i class="fas fa-caret-down"></i>
                        </div>
                                            <div class="node-icon"><i class="fas fa-bolt"></i></div>
                                            <div class="node-title">电力系统仿真度</div>
                                            <div class="node-weight">权重: 35%</div>
                                            <div class="node-result">82.6</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-project-diagram"></i>
                            </div>
                                                    <div class="node-title">配电网拓扑结构精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">85.3</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-chart-line"></i>
                            </div>
                                                    <div class="node-title">负荷预测精度</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">78.9</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-exclamation-triangle"></i>
                            </div>
                                                    <div class="node-title">电网故障响应特性</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">84.2</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：通信网络仿真度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                        <div class="node-collapse">
                          <i class="fas fa-caret-down"></i>
                        </div>
                        <div class="node-icon">
                          <i class="fas fa-broadcast-tower"></i>
                        </div>
                                            <div class="node-title">通信网络仿真度</div>
                                            <div class="node-weight">权重: 30%</div>
                                            <div class="node-result">79.4</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-tachometer-alt"></i>
                            </div>
                                                    <div class="node-title">通信网络容量仿真度</div>
                                                    <div class="node-weight">权重: 40%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">76.8</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-hourglass-half"></i>
                            </div>
                                                    <div class="node-title">通信时延仿真度</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">80.5</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-wifi"></i>
                            </div>
                                                    <div class="node-title">通信网络覆盖精度</div>
                                                    <div class="node-weight">权重: 25%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">82.1</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：交通系统仿真度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                        <div class="node-collapse">
                          <i class="fas fa-caret-down"></i>
                        </div>
                                            <div class="node-icon"><i class="fas fa-road"></i></div>
                                            <div class="node-title">交通系统仿真度</div>
                                            <div class="node-weight">权重: 25%</div>
                                            <div class="node-result">76.2</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-car"></i>
                            </div>
                                                    <div class="node-title">交通流量仿真度</div>
                                                    <div class="node-weight">权重: 40%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">74.5</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-traffic-light"></i>
                            </div>
                                                    <div class="node-title">交通信号控制精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">78.3</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-map-marked-alt"></i>
                            </div>
                                                    <div class="node-title">道路拥堵状态精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 100%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 100%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">76.8</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：跨行业交互影响仿真度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                        <div class="node-collapse">
                          <i class="fas fa-caret-down"></i>
                        </div>
                        <div class="node-icon">
                          <i class="fas fa-exchange-alt"></i>
                        </div>
                                            <div class="node-title">跨行业交互影响仿真度</div>
                                            <div class="node-weight">权重: 10%</div>
                                            <div class="node-progress">
                                                <div class="node-progress-bar">
                                                    <div class="node-progress-fill node-progress-calc"
                                                        style="width: 0%"></div>
                                                </div>
                                                <div class="node-progress-label">
                                                    <span>计算: 0%</span>
                                                </div>
                                            </div>
                                            <div class="node-result">-</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-plug"></i>
                            </div>
                                                    <div class="node-title">电力-通信依赖关系</div>
                                                    <div class="node-weight">权重: 25%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 0%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 0%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-network-wired"></i>
                            </div>
                                                    <div class="node-title">通信-交通依赖关系</div>
                                                    <div class="node-weight">权重: 25%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 0%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 0%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                            <div class="node-collapse">
                              <i class="fas fa-caret-right"></i>
                            </div>
                            <div class="node-icon">
                              <i class="fas fa-charging-station"></i>
                            </div>
                                                    <div class="node-title">交通-电力依赖关系</div>
                                                    <div class="node-weight">权重: 20%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 0%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 0%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content highlighted">
                                                    <div class="node-collapse">
                                                        <i class="fas fa-caret-down"></i>
                                                    </div>
                                                    <div class="node-icon">
                                                        <i class="fas fa-route"></i>
                                                    </div>
                                                    <div class="node-title">风险传播路径相似度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                    <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                            <div class="node-progress-fill node-progress-calc"
                                                                style="width: 0%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 0%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                                <div class="node-children">
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="node-collapse">
                                                                <i class="fas fa-caret-right"></i>
                                                            </div>
                                                            <div class="node-icon">
                                                                <i class="fas fa-layer-group"></i>
                                                            </div>
                                                            <div class="node-title">风险级联层数匹配度</div>
                                                            <div class="node-weight">权重: 35%</div>
                                                            <div class="node-progress">
                                                        <div class="node-progress-bar">
                                                                    <div class="node-progress-fill node-progress-calc"
                                                                        style="width: 0%"></div>
                                                        </div>
                                                        <div class="node-progress-label">
                                                            <span>计算: 0%</span>
                                                        </div>
                                                    </div>
                                                    <div class="node-result">-</div>
                                                </div>
                                            </div>
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="node-collapse">
                                                                <i class="fas fa-caret-right"></i>
                                        </div>
                                                            <div class="node-icon">
                                                                <i class="fas fa-cubes"></i>
                                    </div>
                                                            <div class="node-title">风险关联资产匹配度</div>
                                                            <div class="node-weight">权重: 35%</div>
                                                            <div class="node-progress">
                                                                <div class="node-progress-bar">
                                                                    <div class="node-progress-fill node-progress-calc"
                                                                        style="width: 0%"></div>
                                </div>
                                                                <div class="node-progress-label">
                                                                    <span>计算: 0%</span>
                                                                </div>
                                                            </div>
                                                            <div class="node-result">-</div>
                                                        </div>
                                                    </div>
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="node-collapse">
                                                                <i class="fas fa-caret-right"></i>
                                                            </div>
                                                            <div class="node-icon">
                                                                <i class="fas fa-clock"></i>
                                                            </div>
                                                            <div class="node-title">风险传播时序一致性</div>
                                                            <div class="node-weight">权重: 30%</div>
                                                            <div class="node-progress">
                                                                <div class="node-progress-bar">
                                                                    <div class="node-progress-fill node-progress-calc"
                                                                        style="width: 0%"></div>
                                                                </div>
                                                                <div class="node-progress-label">
                                                                    <span>计算: 0%</span>
                                                                </div>
                                                            </div>
                                                            <div class="node-result">-</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 测评结果与报告内容 -->
            <div class="tab-content" id="results-content" style="display: none">
                <!-- 内部子选项卡导航 -->
                <div class="results-tab-navigation">
                    <div class="results-tab active" data-results-tab="summary">
                        <i class="fas fa-list"></i> 评估总览
                    </div>
                    <div class="results-tab" data-results-tab="detail">
                        <i class="fas fa-chart-pie"></i> 详细指标
                    </div>
                    <div class="results-tab" data-results-tab="report">
                        <i class="fas fa-file-alt"></i> 评估报告
                    </div>
                </div>
                
                <!-- 评估总览 -->
                <div class="results-tab-content active" id="summary-content">
                        <div class="result-summary-card">
                            <div class="result-header">
                                <div>
                                    <span class="result-score">79.8</span>
                                    <span> / 100</span>
                                </div>
                                <div class="result-grade">优良</div>
                            </div>
                            <div class="result-metrics">
                                <div class="result-metric">
                                    <div class="metric-value">82.6</div>
                                    <div class="metric-label">电力系统</div>
                                </div>
                                <div class="result-metric">
                                    <div class="metric-value">79.4</div>
                                    <div class="metric-label">通信网络</div>
                                </div>
                                <div class="result-metric">
                                    <div class="metric-value">76.2</div>
                                    <div class="metric-label">交通系统</div>
                                </div>
                                <div class="result-metric">
                                    <div class="metric-value">-</div>
                                    <div class="metric-label">跨行业交互</div>
                                </div>
                            </div>
                        </div>

                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-chart-pie"></i>
                                    <span>各指标权重分布与得分</span>
                                </h3>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="chart-container" id="weight-score-chart"></div>
                                    <div class="weight-score-table">
                                        <table class="custom-table">
                                            <thead>
                                                <tr>
                                                    <th>指标名称</th>
                                                    <th>权重</th>
                                                    <th>得分</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><i class="fas fa-bolt"></i> 电力系统仿真度</td>
                                                    <td>35%</td>
                                                    <td>82.6</td>
                                                <td><span class="badge badge-success">良好</span></td>
                                                </tr>
                                                <tr>
                                                <td><i class="fas fa-broadcast-tower"></i> 通信网络仿真度</td>
                                                    <td>30%</td>
                                                    <td>79.4</td>
                                                <td><span class="badge badge-success">良好</span></td>
                                                </tr>
                                                <tr>
                                                    <td><i class="fas fa-road"></i> 交通系统仿真度</td>
                                                    <td>25%</td>
                                                    <td>76.2</td>
                                                <td><span class="badge badge-warning">一般</span></td>
                                                </tr>
                                                <tr>
                                                <td><i class="fas fa-exchange-alt"></i> 跨行业交互影响仿真度</td>
                                                    <td>10%</td>
                                                    <td>78.5</td>
                                                <td><span class="badge badge-success">良好</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <!-- 报告生成状态 -->
                    <div class="detail-content-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-file-alt"></i>
                                <span>报告生成状态</span>
                            </h3>
                            <div class="section-actions">
                                <button class="btn btn-secondary">
                                    <i class="fas fa-sync-alt"></i> 刷新状态
                                </button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <div class="progress-container">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 60%"></div>
                                    </div>
                                    <div class="progress-label">
                                        <span>报告生成进度: 60%</span>
                                        <span>预计完成: 任务执行完毕后 15 分钟</span>
                                    </div>
                                </div>
                                
                                <div style="margin-top: 20px; display: flex; justify-content: space-between;">
                                    <div>
                                        <h4>报告生成状态:</h4>
                                        <ul style="margin-top: 10px; margin-left: 20px; list-style-type: disc;">
                                                    <li style="margin-bottom: 8px; color: var(--success-color);">已完成:
                                                        电力系统评估章节</li>
                                                    <li style="margin-bottom: 8px; color: var(--success-color);">已完成:
                                                        通信网络评估章节</li>
                                                    <li style="margin-bottom: 8px; color: var(--success-color);">已完成:
                                                        交通系统评估章节</li>
                                                    <li style="margin-bottom: 8px; color: var(--text-secondary);">等待中:
                                                        跨行业交互评估章节</li>
                                                    <li style="margin-bottom: 8px; color: var(--text-secondary);">等待中:
                                                        综合评估结论</li>
                                                    <li style="margin-bottom: 8px; color: var(--text-secondary);">等待中:
                                                        改进建议</li>
                                        </ul>
                                    </div>
                                    <div class="report-preview">
                                        <div style="text-align: center; padding: 20px">
                                                    <i class="fas fa-file-pdf"
                                                        style="font-size: 48px; color: var(--primary-color); margin-bottom: 15px;"></i>
                                            <h3 style="margin-bottom: 10px">最新报告预览</h3>
                                            <button class="btn btn-primary">
                                                <i class="fas fa-eye"></i> 查看初步报告
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细指标 -->
                <div class="results-tab-content" id="detail-content" style="display: none">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>叶子指标得分详情</span>
                                </h3>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="chart-container" id="leaf-score-chart"></div>
                                    <div class="leaf-score-table">
                                        <table class="custom-table">
                                            <thead>
                                                <tr>
                                                    <th>指标名称</th>
                                                    <th>所属系统</th>
                                                    <th>权重</th>
                                                    <th>得分</th>
                                                    <th>状态</th>
                                                    <th>评估算法</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                <td><i class="fas fa-project-diagram"></i> 配电网拓扑结构精度</td>
                                                    <td>电力系统</td>
                                                    <td>30%</td>
                                                    <td>85.3</td>
                                                <td><span class="badge badge-success">优秀</span></td>
                                                    <td>电网拓扑一致性评估算法</td>
                                                </tr>
                                                <tr>
                                                <td><i class="fas fa-chart-line"></i> 负荷预测精度</td>
                                                    <td>电力系统</td>
                                                    <td>35%</td>
                                                    <td>78.2</td>
                                                <td><span class="badge badge-success">良好</span></td>
                                                    <td>负荷预测评估算法</td>
                                                </tr>
                                                <tr>
                                                <td><i class="fas fa-exclamation-triangle"></i> 电网故障响应特性</td>
                                                    <td>电力系统</td>
                                                    <td>35%</td>
                                                    <td>84.2</td>
                                                <td><span class="badge badge-success">优秀</span></td>
                                                    <td>电网暂态响应评估算法</td>
                                                </tr>
                                                <tr>
                                                <td><i class="fas fa-tachometer-alt"></i> 通信网络容量仿真度</td>
                                                    <td>通信网络</td>
                                                    <td>40%</td>
                                                    <td>75.8</td>
                                                <td><span class="badge badge-warning">一般</span></td>
                                                    <td>网络流量模型评估算法</td>
                                                </tr>
                                                <tr>
                                                <td><i class="fas fa-hourglass-half"></i> 通信时延仿真度</td>
                                                    <td>通信网络</td>
                                                    <td>35%</td>
                                                    <td>80.3</td>
                                                <td><span class="badge badge-success">良好</span></td>
                                                    <td>网络时延评估算法</td>
                                                </tr>
                                                <tr>
                                                <td><i class="fas fa-wifi"></i> 通信网络覆盖精度</td>
                                                    <td>通信网络</td>
                                                    <td>25%</td>
                                                    <td>82.1</td>
                                                <td><span class="badge badge-success">良好</span></td>
                                                    <td>信号覆盖评估算法</td>
                                                </tr>
                                                <tr>
                                                    <td><i class="fas fa-car"></i> 交通流量仿真度</td>
                                                    <td>交通系统</td>
                                                    <td>40%</td>
                                                    <td>75.6</td>
                                                <td><span class="badge badge-warning">一般</span></td>
                                                <td>交通流量评估算法</td>
                                                </tr>
                                                    <tr>
                                                        <td><i class="fas fa-layer-group"></i> 风险级联层数匹配度</td>
                                                        <td>跨行业交互</td>
                                                        <td>35%</td>
                                                        <td>78.4</td>
                                                        <td><span class="badge badge-success">良好</span></td>
                                                        <td>风险级联分析算法</td>
                                                    </tr>
                                                    <tr>
                                                        <td><i class="fas fa-cubes"></i> 风险关联资产匹配度</td>
                                                        <td>跨行业交互</td>
                                                        <td>35%</td>
                                                        <td>75.2</td>
                                                        <td><span class="badge badge-warning">一般</span></td>
                                                        <td>资产关联评估算法</td>
                                                    </tr>
                                                    <tr>
                                                        <td><i class="fas fa-clock"></i> 风险传播时序一致性</td>
                                                        <td>跨行业交互</td>
                                                        <td>30%</td>
                                                        <td>73.8</td>
                                                        <td><span class="badge badge-warning">一般</span></td>
                                                        <td>时序一致性评估算法</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-content-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-chart-line"></i>
                                <span>指标完成度趋势</span>
                            </h3>
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <div class="chart-container" id="indicator-completion-chart"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 评估报告 -->
                <div class="results-tab-content" id="report-content" style="display: none">
                    <div class="detail-content-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-file-alt"></i>
                                <span>测评报告版本</span>
                            </h3>
                            <div class="section-actions">
                                <button class="btn btn-secondary">
                                    <i class="fas fa-download"></i> 下载当前报告
                                </button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <div class="report-versions">
                                    <div class="version-tabs">
                                        <div class="tab active" data-version="v1.0.0">
                                            <span class="version-number">v1.0.0</span>
                                            <span class="version-date">2025-04-09 10:30</span>
                                        </div>
                                        <div class="tab" data-version="v0.9.0">
                                            <span class="version-number">v0.9.0</span>
                                            <span class="version-date">2025-04-09 09:45</span>
                                        </div>
                                        <div class="tab" data-version="v0.8.0">
                                            <span class="version-number">v0.8.0</span>
                                            <span class="version-date">2025-04-09 09:15</span>
                                        </div>
                                    </div>
                                    <div class="version-content">
                                        <div class="version-info active" data-version="v1.0.0">
                                            <div class="version-header">
                                                <h4>测评报告 v1.0.0</h4>
                                                <div class="version-actions">
                                                            <button class="btn btn-primary"
                                                                onclick="downloadReport('v1.0.0')">
                                                        <i class="fas fa-download"></i> 下载报告
                                                    </button>
                                                            <button class="btn btn-secondary"
                                                                onclick="viewReport('v1.0.0')">
                                                        <i class="fas fa-eye"></i> 预览报告
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="version-details">
                                                <div class="detail-item">
                                                    <span class="detail-label">生成时间：</span>
                                                    <span class="detail-value">2025-04-09 10:30</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">报告状态：</span>
                                                    <span class="badge badge-success">已完成</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">报告大小：</span>
                                                    <span class="detail-value">2.5 MB</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">报告格式：</span>
                                                    <span class="detail-value">PDF</span>
                                                </div>
                                            </div>
                                            <div class="simulation-score">
                                                <div class="score-header">
                                                    <h5>仿真度评分</h5>
                                                    <span class="score-value">78.5%</span>
                                                </div>
                                                <div class="score-progress">
                                                    <div class="progress-bar">
                                                        <div class="progress-fill" style="width: 78.5%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-content-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-chart-pie"></i>
                                <span>报告内容大纲</span>
                            </h3>
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <div class="report-outline">
                                    <div class="outline-item">
                                        <div class="outline-header">
                                            <i class="fas fa-file-alt"></i>
                                            <span>1. 测评概述</span>
                                        </div>
                                        <div class="outline-content">
                                            <p>包含测评目的、测评范围、测评方法和测评标准等基本信息，以及整体测评结果的概括性描述。</p>
                                        </div>
                                    </div>
                                    <div class="outline-item">
                                        <div class="outline-header">
                                            <i class="fas fa-bolt"></i>
                                            <span>2. 电力系统仿真度评估</span>
                                        </div>
                                        <div class="outline-content">
                                            <p>详细分析电力系统仿真度评估的各项指标、评估方法和结果，包括配电网拓扑结构精度、负荷预测精度和电网故障响应特性等。</p>
                                        </div>
                                    </div>
                                    <div class="outline-item">
                                        <div class="outline-header">
                                            <i class="fas fa-broadcast-tower"></i>
                                            <span>3. 通信网络仿真度评估</span>
                                        </div>
                                        <div class="outline-content">
                                            <p>详细分析通信网络仿真度评估的各项指标、评估方法和结果，包括通信网络容量仿真度、通信时延仿真度和通信网络覆盖精度等。</p>
                                        </div>
                                    </div>
                                    <div class="outline-item">
                                        <div class="outline-header">
                                            <i class="fas fa-road"></i>
                                            <span>4. 交通系统仿真度评估</span>
                                        </div>
                                        <div class="outline-content">
                                            <p>详细分析交通系统仿真度评估的各项指标、评估方法和结果，包括交通流量仿真度等。</p>
                                        </div>
                                    </div>
                                    <div class="outline-item">
                                        <div class="outline-header">
                                            <i class="fas fa-exchange-alt"></i>
                                            <span>5. 跨行业交互仿真度评估</span>
                                            <span class="badge badge-warning">待完成</span>
                                        </div>
                                                <div class="outline-content">
                                                    <p>详细分析跨行业交互影响仿真度评估的各项指标、评估方法和结果，包括电力-通信依赖关系、通信-交通依赖关系、交通-电力依赖关系，以及重点评估风险传播路径相似度，涵盖风险级联层数匹配度、风险关联资产匹配度和风险传播时序一致性等关键指标。
                                                    </p>
                                        </div>
                                    </div>
                                    <div class="outline-item">
                                        <div class="outline-header">
                                            <i class="fas fa-check-circle"></i>
                                            <span>6. 综合评估结论</span>
                                            <span class="badge badge-warning">待完成</span>
                                        </div>
                                    </div>
                                    <div class="outline-item">
                                        <div class="outline-header">
                                            <i class="fas fa-lightbulb"></i>
                                            <span>7. 改进建议</span>
                                            <span class="badge badge-warning">待完成</span>
                                        </div>
                                    </div>
                                </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 执行日志内容 -->
            <div class="tab-content" id="logs-content" style="display: none">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-list-alt"></i>
                                    <span>任务执行日志</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-filter"></i> 筛选
                                    </button>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-file-export"></i> 导出日志
                                    </button>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-sync-alt"></i> 刷新
                                    </button>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="log-container">
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:52:18</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>正在采集"交通-电力依赖关系"指标数据，进度 35%</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:50:42</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>正在采集"风险传播时序一致性"指标数据，进度 45%</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:48:33</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>正在采集"风险关联资产匹配度"指标数据，进度 70%</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:46:15</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>正在采集"风险级联层数匹配度"指标数据，进度 80%</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:45:07</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>开始采集"交通-电力依赖关系"指标数据</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:40:22</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"通信-交通依赖关系"指标数据采集完成</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:38:15</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>正在采集"通信-交通依赖关系"指标数据，进度 80%</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:30:10</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"电力-通信依赖关系"指标数据采集完成</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:25:47</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>正在采集"电力-通信依赖关系"指标数据，进度 90%</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:20:33</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"道路拥堵状态精度"指标评估完成，得分 76.8</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:18:21</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"交通信号控制精度"指标评估完成，得分 78.3</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:15:42</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"交通流量仿真度"指标评估完成，得分 74.5</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:14:10</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>根据权重计算"交通系统仿真度"得分为 76.2</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:10:35</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"通信网络覆盖精度"指标评估完成，得分 82.1</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:05:59</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"通信时延仿真度"指标评估完成，得分 80.5</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:02:17</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"通信网络容量仿真度"指标评估完成，得分 76.8</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 10:00:48</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>根据权重计算"通信网络仿真度"得分为 79.4</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 09:55:22</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"电网故障响应特性"指标评估完成，得分 84.2</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 09:45:10</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"负荷预测精度"指标评估完成，得分 78.9</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 09:35:42</span>
                                            <span class="log-level-success">[SUCCESS]</span>
                                            <span>"配电网拓扑结构精度"指标评估完成，得分 85.3</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 09:32:18</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>根据权重计算"电力系统仿真度"得分为 82.6</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-09 09:15:32</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>评估任务"通信-交通行业级联风险仿真度评估"开始执行</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 任务配置内容 -->
                    <div class="tab-content" id="settings-content" style="display: none">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-cog"></i>
                                    <span>任务基本配置</span>
                                </h3>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label class="form-label">任务名称</label>
                                        <input type="text" class="form-input" value="通信-交通行业级联风险仿真度评估" disabled />
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">任务描述</label>
                      <textarea class="form-textarea" disabled>
对城市X关键基础设施跨行业协同仿真模型进行综合评估，重点关注电力-通信-交通三大行业的互联互通和级联故障仿真度。该评估旨在验证仿真模型对城市X实际基础设施网络拓扑和运行特性的还原程度，特别是风险传播路径相似度的评估，包括风险级联层数匹配度、风险关联资产匹配度和风险传播时序一致性等关键指标。</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 保存为模版弹窗 -->
    <div class="modal-overlay" id="saveAsTemplateModal">
        <div class="modal">
            <div class="modal-header">
                <div>
                    <h2 class="modal-title">保存为模版</h2>
                    <p class="modal-subtitle">将当前评估任务的配置保存为可复用的模版</p>
                </div>
                <button class="modal-close" onclick="closeModal('saveAsTemplateModal')">
            &times;
          </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">模版名称 <span style="color: var(--danger-color)">*</span></label>
                    <input type="text" class="form-input" value="城市X电力-通信-交通仿真度评估模版" />
                </div>
                
                <div class="form-group">
                    <label class="form-label">模版描述</label>
            <textarea class="form-textarea">
基于城市X实际评估经验创建的电力-通信-交通协同仿真度评估模版，适用于类似规模和结构的城市级关键基础设施评估。</textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">模版分类</label>
                            <select class="form-select">
                                <option value="multi" selected>跨行业综合</option>
                                <option value="power">电力行业</option>
                                <option value="comm">通信行业</option>
                                <option value="traffic">交通行业</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">权限设置</label>
                            <select class="form-select">
                                <option value="private">仅创建者可见</option>
                                <option value="team" selected>团队内可见</option>
                                <option value="public">公开</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">标签</label>
                    <input type="text" class="form-input" value="城市X, 跨行业仿真, 电力-通信-交通, 关键基础设施, 级联故障分析" />
                    <div class="form-sublabel">多个标签请用逗号分隔</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">保存内容选择</label>
            <div style="margin-top: 10px">
                        <label style="display: flex; align-items: center; margin-bottom: 10px">
                <input type="checkbox" checked style="margin-right: 10px" />
                            <span>指标体系与权重配置</span>
                        </label>
                        <label style="display: flex; align-items: center; margin-bottom: 10px">
                <input type="checkbox" checked style="margin-right: 10px" />
                            <span>算法与参数配置</span>
                        </label>
                        <label style="display: flex; align-items: center; margin-bottom: 10px">
                <input type="checkbox" style="margin-right: 10px" />
                            <span>数据源配置</span>
                        </label>
              <label style="display: flex; align-items: center">
                <input type="checkbox" checked style="margin-right: 10px" />
                            <span>任务描述与标签</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('saveAsTemplateModal')">
            取消
          </button>
          <button class="btn btn-primary" onclick="saveAsTemplate()">
            保存为模版
          </button>
            </div>
        </div>
    </div>

    <!-- 确认停止任务弹窗 -->
    <div class="modal-overlay" id="stopTaskConfirmModal">
        <div class="modal">
            <div class="modal-header">
                <div>
                    <h2 class="modal-title">确认停止任务</h2>
                </div>
                <button class="modal-close" onclick="closeModal('stopTaskConfirmModal')">
            &times;
          </button>
            </div>
            <div class="modal-body">
          <div style="text-align: center; padding: 20px">
                    <i class="fas fa-exclamation-triangle" style="
                font-size: 48px;
                color: var(--danger-color);
                margin-bottom: 20px;
              "></i>
            <h3 style="margin-bottom: 15px">确定要停止当前评估任务吗？</h3>
            <p style="color: var(--text-secondary); margin-bottom: 5px">
                        停止操作将立即终止所有正在执行的评估过程，已评估完成的指标数据将保留。
                    </p>
            <p style="color: var(--danger-color); margin-bottom: 0">
                        此操作不可恢复，请确认您的决定。
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('stopTaskConfirmModal')">
            取消
          </button>
                <button class="btn btn-danger" onclick="stopTask()">确认停止</button>
            </div>
        </div>
    </div>

    <!-- 确认暂停任务弹窗 -->
    <div class="modal-overlay" id="pauseTaskConfirmModal">
        <div class="modal">
            <div class="modal-header">
                <div>
                    <h2 class="modal-title">确认暂停任务</h2>
                </div>
                <button class="modal-close" onclick="closeModal('pauseTaskConfirmModal')">
            &times;
          </button>
            </div>
            <div class="modal-body">
          <div style="text-align: center; padding: 20px">
                    <i class="fas fa-pause-circle" style="
                font-size: 48px;
                color: var(--warning-color);
                margin-bottom: 20px;
              "></i>
            <h3 style="margin-bottom: 15px">确定要暂停当前评估任务吗？</h3>
            <p style="color: var(--text-secondary); margin-bottom: 5px">
                        暂停操作将临时中断测评过程，您可以稍后继续执行。当前进度和已计算的结果将被保留。
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('pauseTaskConfirmModal')">
            取消
          </button>
          <button class="btn btn-warning" onclick="pauseTask()">
            确认暂停
          </button>
            </div>
        </div>
    </div>

    <!-- 任务配置内容 -->
    <div class="tab-content" id="config-content" style="display: none;">
        <div class="config-layout">
            <!-- 左侧知识图谱 -->
            <div class="config-main">
                <div class="detail-content-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-sitemap"></i>
                            <span>指标详情</span>
                        </h3>
                        <div class="section-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-expand-alt"></i> 展开全部
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div id="knowledge-graph" style="width: 100%; height: 800px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧配置页专用侧边栏 -->
            <div class="config-sidebar">
                <!-- 关联的数据采集任务卡片 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">关联的数据采集任务</div>
                    </div>
                    <div class="card-body">
                        <div class="stats-grid">
                            <div class="stats-card" data-indicator="一级指标1">
                                <div class="stats-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-label">电力系统数据采集</div>
                                    <div class="stats-value">运行中</div>
                                    <div class="stats-meta">
                                        <span><i class="fas fa-clock"></i> 每4小时</span>
                                        <span><i class="fas fa-user"></i> 张三</span>
                                    </div>
                                </div>
                                <div class="stats-chart">
                                    <div class="mini-chart" id="collection-chart-1"></div>
                                </div>
                            </div>

                            <div class="stats-card" data-indicator="一级指标2">
                                <div class="stats-icon">
                                    <i class="fas fa-broadcast-tower"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-label">通信网络数据采集</div>
                                    <div class="stats-value">已完成</div>
                                    <div class="stats-meta">
                                        <span><i class="fas fa-clock"></i> 每6小时</span>
                                        <span><i class="fas fa-user"></i> 李四</span>
                                    </div>
                                </div>
                                <div class="stats-chart">
                                    <div class="mini-chart" id="collection-chart-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关联的计算任务卡片 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">关联的计算任务</div>
                    </div>
                    <div class="card-body">
                        <div class="stats-grid">
                            <div class="stats-card" data-indicator="一级指标1">
                                <div class="stats-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-label">电力系统仿真度计算</div>
                                    <div class="stats-value">运行中</div>
                                    <div class="stats-meta">
                                        <span><i class="fas fa-clock"></i> 每8小时</span>
                                        <span><i class="fas fa-user"></i> 王五</span>
                                    </div>
                                </div>
                                <div class="stats-chart">
                                    <div class="mini-chart" id="calculation-chart-1"></div>
                                </div>
                            </div>

                            <div class="stats-card" data-indicator="一级指标2">
                                <div class="stats-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-label">跨行业协同计算</div>
                                    <div class="stats-value">等待中</div>
                                    <div class="stats-meta">
                                        <span><i class="fas fa-clock"></i> 每12小时</span>
                                        <span><i class="fas fa-user"></i> 赵六</span>
                                    </div>
                                </div>
                                <div class="stats-chart">
                                    <div class="mini-chart" id="calculation-chart-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 弹窗结构 -->
    <div class="modal-overlay" id="topoModal">
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">
                        <i class="fas fa-sitemap"></i>
                        拓扑结构详情
                    </div>
                    <button class="modal-close" onclick="closeTopoModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <iframe class="modal-iframe" id="topoIframe" src=""></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化页面和图表
      document.addEventListener("DOMContentLoaded", function () {
            initTabSystem();
            initTreeCollapse();
            initTaskStatusChart();
            initProgressChart();
            initIndicatorCompletionChart();
            initWeightScoreChart();
            initLeafScoreChart();
            initResultsTabs(); // 添加此行，初始化结果页面的子标签页
            
            // 绑定暂停和停止任务按钮事件
        const pauseTaskBtn = document.getElementById("pauseTaskBtn");
        if (pauseTaskBtn) {
            pauseTaskBtn.addEventListener("click", function () {
            openModal("pauseTaskConfirmModal");
          });
        }

        const pauseTaskBtn2 = document.getElementById("pauseTaskBtn2");
        if (pauseTaskBtn2) {
            pauseTaskBtn2.addEventListener("click", function () {
            openModal("pauseTaskConfirmModal");
          });
        }

        const stopTaskBtn = document.getElementById("stopTaskBtn");
        if (stopTaskBtn) {
            stopTaskBtn.addEventListener("click", function () {
            openModal("stopTaskConfirmModal");
          });
        }

        const stopTaskBtn2 = document.getElementById("stopTaskBtn2");
        if (stopTaskBtn2) {
            stopTaskBtn2.addEventListener("click", function () {
            openModal("stopTaskConfirmModal");
            });
        }
            
            // 绑定保存为模版按钮事件
        const saveAsTemplateBtn = document.getElementById("saveAsTemplateBtn");
        if (saveAsTemplateBtn) {
            saveAsTemplateBtn.addEventListener("click", function () {
            openModal("saveAsTemplateModal");
            });
        }
            
            // 启动模拟的进度更新
            startProgressSimulation();
        });

        // 选项卡系统初始化
        function initTabSystem() {
            const tabButtons = document.querySelectorAll('.detail-nav-tab');
            const tabContents = document.querySelectorAll('.tab-content');
        const mainSidebar = document.querySelector('.task-detail-sidebar');
        const configSidebar = document.querySelector('.config-sidebar');
            
            // 解析URL参数获取默认tab
            function getUrlParameter(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }
            
            // 激活指定的tab
            function activateTab(tabId) {
                    // 移除所有标签页的激活状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.style.display = 'none');
                    
                // 查找对应的按钮和内容
                const targetButton = document.querySelector(`[data-tab="${tabId}"]`);
                const targetContent = document.getElementById(`${tabId}-content`);
                
                if (targetButton && targetContent) {
                    // 激活当前选中的标签页
                    targetButton.classList.add('active');
                    targetContent.style.display = 'block';

            // 管理右侧栏的显示
                    if (tabId === 'progress' || tabId === 'calculate') {
                        // 任务进度和计算进度页面显示主侧边栏
              if (mainSidebar) mainSidebar.style.display = 'block';
              if (configSidebar) configSidebar.style.display = 'none';
                    } else if (tabId === 'settings') {
              // 任务配置页面显示其专用侧边栏
              if (mainSidebar) mainSidebar.style.display = 'none';
              if (configSidebar) configSidebar.style.display = 'block';
            } else {
              // 其他页面不显示侧边栏
              if (mainSidebar) mainSidebar.style.display = 'none';
              if (configSidebar) configSidebar.style.display = 'none';
            }
                    
                    // 刷新图表，解决图表在隐藏标签页中不能正确显示的问题
                    window.dispatchEvent(new Event('resize'));
                }
            }
            
            // 绑定tab按钮点击事件
            tabButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const tabId = this.getAttribute('data-tab');
                    activateTab(tabId);
                });
            });
            
            // 根据URL参数决定初始显示的tab
            const tabParam = getUrlParameter('tab');
            let initialTab = 'mindmap'; // 默认显示第一个tab（指标体系）
            
            // 根据tab参数值映射到对应的tab
            const tabMap = {
                '0': 'mindmap',     // 指标体系
                '1': 'progress',    // 数据采集进度
                '2': 'calculate',   // 指标计算进度
                '3': 'results',     // 评估结果与报告
                '4': 'logs',        // 执行日志
                '5': 'settings'     // 任务配置
            };
            
            if (tabParam && tabMap[tabParam]) {
                initialTab = tabMap[tabParam];
            }
            
            // 激活初始tab
            activateTab(initialTab);
        }

        // 初始化结果页面的子标签页
        function initResultsTabs() {
            const resultTabButtons = document.querySelectorAll('.results-tab');
            
            resultTabButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const tabId = this.getAttribute('data-results-tab');
                    
                    // 移除所有子标签页的激活状态
                    resultTabButtons.forEach(btn => btn.classList.remove('active'));
                    document.querySelectorAll('.results-tab-content').forEach(content => {
                        content.classList.remove('active');
                        content.style.display = 'none';
                    });
                    
                    // 激活当前选中的子标签页
                    this.classList.add('active');
                    const targetContent = document.getElementById(`${tabId}-content`);
                    if (targetContent) {
                        targetContent.classList.add('active');
                        targetContent.style.display = 'block';
            }
                    
                    // 刷新图表，解决图表在隐藏标签页中不能正确显示的问题
                    window.dispatchEvent(new Event('resize'));
                });
            });
        }

        // 树形结构折叠/展开功能
        function initTreeCollapse() {
        const nodeCollapseIcons = document.querySelectorAll(".node-collapse");

        nodeCollapseIcons.forEach((icon) => {
          icon.addEventListener("click", function () {
            const nodeContent = this.closest(".node-content");
                    const nodeChildren = nodeContent.nextElementSibling;
                    
            if (
              nodeChildren &&
              nodeChildren.classList.contains("node-children")
            ) {
              if (nodeChildren.style.display === "none") {
                nodeChildren.style.display = "block";
                            this.innerHTML = '<i class="fas fa-caret-down"></i>';
                        } else {
                nodeChildren.style.display = "none";
                            this.innerHTML = '<i class="fas fa-caret-right"></i>';
                        }
                    }
                });
            });
        }

        // 模态窗口操作
        function openModal(modalId) {
        document.getElementById(modalId).style.display = "block";
        document.body.style.overflow = "hidden";
        }
        
        function closeModal(modalId) {
        document.getElementById(modalId).style.display = "none";
        document.body.style.overflow = "auto";
        }

        // 任务操作函数
        function pauseTask() {
        closeModal("pauseTaskConfirmModal");
            // 更新UI状态
        const statusElements = document.querySelectorAll(".status-running");
        statusElements.forEach((el) => {
          el.classList.remove("status-running");
          el.classList.add("status-paused");
          el.textContent = "已暂停";
        });

        const pauseBtn = document.getElementById("pauseTaskBtn");
        if (pauseBtn) {
            pauseBtn.innerHTML = '<i class="fas fa-play"></i> 继续任务';
            pauseBtn.classList.remove("warning");
            pauseBtn.classList.add("success");
        }

        const pauseBtn2 = document.getElementById("pauseTaskBtn2");
        if (pauseBtn2) {
            pauseBtn2.innerHTML = '<i class="fas fa-play"></i> 继续任务';
            pauseBtn2.classList.remove("warning");
            pauseBtn2.classList.add("success");
        }
            
            // 停止进度模拟
            stopProgressSimulation();
            
            // 显示提示消息
        showNotification("任务已成功暂停", "info");
        }
        
        function stopTask() {
        closeModal("stopTaskConfirmModal");
            // 更新UI状态
        const statusElements = document.querySelectorAll(
          ".status-running, .status-paused"
        );
        statusElements.forEach((el) => {
          el.classList.remove("status-running", "status-paused");
          el.classList.add("status-failed");
          el.textContent = "已终止";
            });
            
            // 禁用控制按钮
        const pauseBtn = document.getElementById("pauseTaskBtn");
        if (pauseBtn) {
            pauseBtn.disabled = true;
            pauseBtn.style.opacity = "0.5";
        }
        
        const stopBtn = document.getElementById("stopTaskBtn");
        if (stopBtn) {
            stopBtn.disabled = true;
            stopBtn.style.opacity = "0.5";
        }

        const pauseBtn2 = document.getElementById("pauseTaskBtn2");
        if (pauseBtn2) {
            pauseBtn2.disabled = true;
            pauseBtn2.style.opacity = "0.5";
        }
        
        const stopBtn2 = document.getElementById("stopTaskBtn2");
        if (stopBtn2) {
            stopBtn2.disabled = true;
            stopBtn2.style.opacity = "0.5";
        }
            
            // 停止进度模拟
            stopProgressSimulation();
            
            // 显示提示消息
        showNotification("任务已终止", "error");
        }
        
        function saveAsTemplate() {
        closeModal("saveAsTemplateModal");
            // 显示成功消息
        showNotification("评估任务已成功保存为模版", "success");
        }

        // 初始化任务状态图表
        function initTaskStatusChart() {
        const chartDom = document.getElementById("task-status-chart");
            if (!chartDom) return; // 如果找不到DOM元素，则直接返回
            
            const myChart = echarts.init(chartDom);
            const option = {
          series: [
            {
              type: "pie",
              radius: ["80%", "100%"],
                    avoidLabelOverlap: false,
                    silent: true,
                    itemStyle: {
                color: function (params) {
                            // 进度为0-100
                            const progress = 68;
                            return params.dataIndex === 0 
                                ? { 
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                                    colorStops: [
                          { offset: 0, color: "#0096FF" },
                          { offset: 1, color: "#00E0FF" },
                        ],
                      }
                    : "rgba(255, 255, 255, 0.05)";
                },
                    },
                    label: {
                show: false,
                    },
                    data: [
                { value: 68, name: "进度" },
                { value: 32, name: "剩余" },
                    ],
              animation: false,
            },
          ],
            };
            myChart.setOption(option);
        }

        // 初始化进度趋势图表
        function initProgressChart() {
        const chartDom = document.getElementById("progress-chart");
            if (!chartDom) return;
            
            const myChart = echarts.init(chartDom);
            const option = {
                title: {
            text: "评估任务进度趋势",
            left: "center",
                    textStyle: {
              color: "#E0E6F0",
            },
                },
                tooltip: {
            trigger: "axis",
            formatter: function (params) {
                        const time = params[0].axisValue;
                        let result = `${time}<br/>`;
                        
              params.forEach((param) => {
                            result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`;
                        });
                        
                        return result;
            },
                },
                grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
                },
                xAxis: {
            type: "category",
                    boundaryGap: false,
            data: [
              "09:15",
              "09:30",
              "09:45",
              "10:00",
              "10:15",
              "10:30",
              "10:45",
              "11:00",
            ],
                    axisLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.2)",
              },
                    },
                    axisLabel: {
              color: "rgba(255, 255, 255, 0.6)",
            },
                },
                yAxis: {
            type: "value",
                    min: 0,
                    max: 100,
                    axisLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.2)",
              },
                    },
                    splitLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.1)",
              },
                    },
                    axisLabel: {
              color: "rgba(255, 255, 255, 0.6)",
              formatter: "{value}%",
            },
                },
                series: [
                    {
              name: "总体进度",
              type: "line",
                        data: [0, 15, 28, 42, 56, 62, 66, 68],
                        itemStyle: {
                color: "#0096FF",
                        },
                        lineStyle: {
                            width: 3,
                            color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                                colorStops: [
                    { offset: 0, color: "#0096FF" },
                    { offset: 1, color: "#00E0FF" },
                  ],
                },
                        },
                        areaStyle: {
                            color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                                colorStops: [
                    { offset: 0, color: "rgba(0, 150, 255, 0.3)" },
                    { offset: 1, color: "rgba(0, 150, 255, 0.05)" },
                  ],
                },
              },
              smooth: true,
            },
            {
              name: "数据采集",
              type: "line",
                        data: [0, 25, 45, 68, 82, 90, 95, 97],
                        itemStyle: {
                color: "#36CFC9",
                        },
                        lineStyle: {
                width: 2,
                        },
              smooth: true,
                    },
                    {
              name: "指标计算",
              type: "line",
                        data: [0, 10, 20, 35, 48, 56, 62, 65],
                        itemStyle: {
                color: "#FFA940",
                        },
                        lineStyle: {
                width: 2,
              },
              smooth: true,
            },
          ],
            };
            myChart.setOption(option);
            
            // 窗口调整时重绘图表
        window.addEventListener("resize", function () {
                myChart.resize();
            });
        }

        // 初始化指标完成情况图表
        function initIndicatorCompletionChart() {
        const chartDom = document.getElementById("indicator-completion-chart");
            if (!chartDom) return;
            
            const myChart = echarts.init(chartDom);
            const option = {
                tooltip: {
            trigger: "item",
                },
                legend: {
            orient: "vertical",
            left: "left",
                    textStyle: {
              color: "rgba(255, 255, 255, 0.7)",
            },
                },
                series: [
                    {
              name: "指标完成情况",
              type: "pie",
              radius: "70%",
              center: ["55%", "50%"],
                        data: [
                { value: 9, name: "已完成", itemStyle: { color: "#0096FF" } },
                            { value: 7, name: "进行中", itemStyle: { color: "#FFA940" } },
                {
                  value: 0,
                  name: "未开始",
                  itemStyle: { color: "rgba(255, 255, 255, 0.2)" },
                },
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
                        },
                        label: {
                color: "rgba(255, 255, 255, 0.7)",
              },
            },
          ],
            };
            myChart.setOption(option);
            
            // 窗口调整时重绘图表
        window.addEventListener("resize", function () {
                myChart.resize();
            });
        }

        // 初始化权重与得分图表
        function initWeightScoreChart() {
        const chartDom = document.getElementById("weight-score-chart");
            if (!chartDom) return;
            
            const myChart = echarts.init(chartDom);
            const option = {
                tooltip: {
            trigger: "axis",
                    axisPointer: {
              type: "shadow",
                    },
            formatter: function (params) {
                        return `${params[0].name}<br/>
                                ${params[0].marker} 权重: ${params[0].value}%<br/>
                                ${params[1].marker} 得分: ${params[1].value}`;
            },
                },
                legend: {
            data: ["权重", "得分"],
                    textStyle: {
              color: "rgba(255, 255, 255, 0.7)",
            },
                },
                grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
                },
                xAxis: {
            type: "value",
                    axisLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.2)",
              },
                    },
                    axisLabel: {
              color: "rgba(255, 255, 255, 0.6)",
                    },
                    splitLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.1)",
              },
            },
                },
                yAxis: {
            type: "category",
            data: ["电力系统", "通信网络", "交通系统", "跨行业交互"],
                    axisLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.2)",
              },
                    },
                    axisLabel: {
              color: "rgba(255, 255, 255, 0.7)",
            },
                },
                series: [
                    {
              name: "权重",
              type: "bar",
                        data: [35, 30, 25, 10],
                        label: {
                            show: true,
                position: "right",
                formatter: "{c}%",
                        },
                        itemStyle: {
                            color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                                colorStops: [
                    { offset: 0, color: "rgba(0, 150, 255, 0.6)" },
                    { offset: 1, color: "rgba(0, 224, 255, 0.6)" },
                  ],
                },
              },
            },
            {
              name: "得分",
              type: "bar",
                        data: [82.6, 79.4, 76.2, 0],
                        label: {
                            show: true,
                position: "right",
                        },
                        itemStyle: {
                color: function (params) {
                                const score = params.value;
                  if (score === 0) return "rgba(255, 255, 255, 0.2)";
                  if (score >= 85) return "#00C48C";
                  if (score >= 75) return "#0096FF";
                  if (score >= 60) return "#FFA940";
                  return "#F25767";
                },
              },
            },
          ],
            };
            myChart.setOption(option);
            
            // 窗口调整时重绘图表
        window.addEventListener("resize", function () {
                myChart.resize();
            });
        }

        // 初始化叶子指标得分图表
        function initLeafScoreChart() {
        const chartDom = document.getElementById("leaf-score-chart");
            if (!chartDom) return;
            
            const myChart = echarts.init(chartDom);
            const option = {
                tooltip: {
            trigger: "axis",
                    axisPointer: {
              type: "shadow",
            },
                },
                grid: {
            left: "3%",
            right: "4%",
            bottom: "15%",
            containLabel: true,
                },
                xAxis: {
            type: "category",
                    data: [
              "配电网拓扑结构精度",
              "负荷预测精度",
              "电网故障响应特性",
              "通信网络容量精度",
              "通信时延精度",
              "通信覆盖精度",
              "交通流量精度",
              "交通信号控制精度",
              "道路拥堵状态精度",
                        "风险级联层数匹配度",
                        "风险关联资产匹配度",
                        "风险传播时序一致性",
                    ],
                    axisLabel: {
                        interval: 0,
                        rotate: 45,
              color: "rgba(255, 255, 255, 0.6)",
              fontSize: 10,
                    },
                    axisLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.2)",
              },
            },
                },
                yAxis: {
            type: "value",
                    min: 0,
                    max: 100,
                    axisLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.2)",
              },
                    },
                    axisLabel: {
              color: "rgba(255, 255, 255, 0.6)",
                    },
                    splitLine: {
                        lineStyle: {
                color: "rgba(255, 255, 255, 0.1)",
              },
            },
                },
                series: [
                    {
              type: "bar",
                        data: [85.3, 78.9, 84.2, 76.8, 80.5, 82.1, 74.5, 78.3, 76.8, 78.4, 75.2, 73.8],
                        itemStyle: {
                color: function (params) {
                                const score = params.value;
                  if (score >= 85) return "#00C48C";
                  if (score >= 75) return "#0096FF";
                  if (score >= 60) return "#FFA940";
                  return "#F25767";
                },
                        },
                        label: {
                            show: true,
                position: "top",
                color: "rgba(255, 255, 255, 0.7)",
              },
            },
          ],
            };
            myChart.setOption(option);
            
            // 窗口调整时重绘图表
        window.addEventListener("resize", function () {
                myChart.resize();
            });
        }

        // 显示通知消息
      function showNotification(message, type = "info") {
            // 创建通知元素
        const notification = document.createElement("div");
        notification.style.position = "fixed";
        notification.style.top = "20px";
        notification.style.right = "20px";
        notification.style.padding = "12px 20px";
        notification.style.borderRadius = "8px";
        notification.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
        notification.style.zIndex = "9999";
        notification.style.display = "flex";
        notification.style.alignItems = "center";
        notification.style.gap = "10px";
        notification.style.transition = "all 0.3s ease";
        notification.style.transform = "translateX(120%)";
            
            // 根据类型设置样式
            let icon, bgColor, textColor;
            switch (type) {
          case "success":
            icon = "fas fa-check-circle";
            bgColor = "rgba(0, 196, 140, 0.95)";
            textColor = "white";
                    break;
          case "error":
            icon = "fas fa-times-circle";
            bgColor = "rgba(242, 87, 103, 0.95)";
            textColor = "white";
                    break;
          case "warning":
            icon = "fas fa-exclamation-triangle";
            bgColor = "rgba(255, 185, 70, 0.95)";
            textColor = "white";
                    break;
                default: // info
            icon = "fas fa-info-circle";
            bgColor = "rgba(0, 150, 255, 0.95)";
            textColor = "white";
            }
            
            notification.style.backgroundColor = bgColor;
            notification.style.color = textColor;
            
            // 添加图标和消息
            notification.innerHTML = `<i class="${icon}"></i> ${message}`;
            
            // 添加到文档
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
          notification.style.transform = "translateX(0)";
            }, 10);
            
            // 自动关闭
            setTimeout(() => {
          notification.style.transform = "translateX(120%)";
                
                // 移除元素
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 4000);
        }

        // 模拟进度更新
        let progressInterval;
        let lastProgressUpdate = new Date();
        
        function startProgressSimulation() {
            progressInterval = setInterval(updateProgress, 5000);
        }
        
        function stopProgressSimulation() {
            clearInterval(progressInterval);
        }
        
        function updateProgress() {
            // 这里可以根据需要模拟进度更新
            // 仅用于演示，实际应用中应从后端获取真实进度
            
            // 更新上次进度更新时间
            const now = new Date();
            const timeDiff = Math.round((now - lastProgressUpdate) / 1000);
            lastProgressUpdate = now;
            
            // 模拟添加新的日志
        const logContainer = document.querySelector(".log-container");
            if (logContainer) {
                const timestamp = now.toLocaleTimeString();
                
          const newLog = document.createElement("div");
          newLog.className = "log-line";
                newLog.innerHTML = `
                    <span class="log-timestamp">${now
                      .toISOString()
                      .replace("T", " ")
                      .substr(0, 19)}</span>
                    <span class="log-level-info">[INFO]</span>
                    <span>正在采集"交通-电力依赖关系"指标数据，进度更新至 42%</span>
                `;
                
                logContainer.insertBefore(newLog, logContainer.firstChild);
            }
            
            // 更新交通-电力依赖关系的进度
        const progressBarElements = document.querySelectorAll(
          "#progress-content .tree-node:last-child .node-children .tree-node:last-child .node-progress-bar:first-child .node-progress-fill"
        );
        if (progressBarElements.length > 0) {
        progressBarElements.forEach((bar) => {
                if (!bar) return;
                const currentWidth = parseInt(bar.style.width) || 35;
                const newWidth = Math.min(currentWidth + 7, 100);
                bar.style.width = `${newWidth}%`;
                
                // 更新对应的标签
          const label = bar.closest(".node-progress")?.querySelector(".node-progress-label span:first-child");
                if (label) {
                    label.textContent = `数据: ${newWidth}%`;
                }
            });
        }
            
            // 更新总进度
        const mainProgressElements = document.querySelectorAll(".progress-fill");
        if (mainProgressElements.length > 0) {
        mainProgressElements.forEach((bar) => {
                if (!bar) return;
          if (bar.closest(".node-progress-bar")) return;
          if (bar.closest(".progress-bar")) {
                    const currentWidth = parseInt(bar.style.width) || 68;
                    const newWidth = Math.min(currentWidth + 2, 100);
                    bar.style.width = `${newWidth}%`;
                    
                    // 更新进度文本
            const container = bar.closest(".progress-container");
                    if (container) {
              const progressLabel = container.querySelector(".progress-value");
                        if (progressLabel) {
                            progressLabel.textContent = `${newWidth}%`;
                        }
                    }
                }
            });
        }
        }

      // 思维导图初始化和操作
      let graph = null;
      let nodeMenu = null;
      let currentNode = null;
      let focusedNode = null;
      let isFocused = false;
      let originalMindMapData = null;

      // 思维导图数据
      const mindMapData = {
        id: "root",
        label: "城市级联关基行业仿真度测评",
        dataProgress: 68,
        calcProgress: 62,
        style: {
          fill: "#0F1520",
          stroke: "#3E9BFF",
          lineWidth: 4,
          radius: 8,
          fontWeight: "bold",
        },
        children: [
          {
            id: "power",
            label: "电力系统仿真度",
            weight: 35,
            result: 82.6,
            style: {
              fill: "#0F1520",
              stroke: "#0096FF",
              lineWidth: 4,
              radius: 8,
            },
            children: [
              {
                id: "power-topo",
                label: "配电网拓扑结构精度",
                weight: 30,
                result: 85.3,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#00A3FF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
              {
                id: "power-load",
                label: "负荷预测精度",
                weight: 35,
                result: 78.9,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#00A3FF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
              {
                id: "power-fault",
                label: "电网故障响应特性",
                weight: 35,
                result: 84.2,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#00A3FF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
            ],
          },
          {
            id: "comm",
            label: "通信网络仿真度",
            weight: 30,
            result: 79.4,
            style: {
              fill: "#0F1520",
              stroke: "#00BFFF",
              lineWidth: 4,
              radius: 8,
            },
            children: [
              {
                id: "comm-capacity",
                label: "通信网络容量仿真度",
                weight: 40,
                result: 76.8,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#00CCFF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
              {
                id: "comm-delay",
                label: "通信时延仿真度",
                weight: 35,
                result: 80.5,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#00CCFF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
              {
                id: "comm-coverage",
                label: "通信网络覆盖精度",
                weight: 25,
                result: 82.1,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#00CCFF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
            ],
          },
          {
            id: "traffic",
            label: "交通系统仿真度",
            weight: 25,
            result: 76.2,
            style: {
              fill: "#0F1520",
              stroke: "#00E0FF",
              lineWidth: 4,
              radius: 8,
            },
            children: [
              {
                id: "traffic-flow",
                label: "交通流量仿真度",
                weight: 40,
                result: 74.5,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#40E0FF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
              {
                id: "traffic-signal",
                label: "交通信号控制精度",
                weight: 30,
                result: 78.3,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#40E0FF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
              {
                id: "traffic-congestion",
                label: "道路拥堵状态精度",
                weight: 30,
                result: 76.8,
                dataProgress: 100,
                calcProgress: 100,
                style: {
                  fill: "#0F1520",
                  stroke: "#40E0FF",
                  lineWidth: 4,
                  radius: 8,
                },
              },
            ],
          },
          {
            id: "cross",
            label: "跨行业交互影响仿真度",
            weight: 10,
            style: {
              fill: "#0F1520",
              stroke: "#00C48C",
              lineWidth: 4,
              radius: 8,
            },
            children: [
              {
                id: "cross-power-comm",
                label: "电力-通信依赖关系",
                            weight: 25,
                dataProgress: 100,
                calcProgress: 0,
                style: {
                  fill: "#0F1520",
                  stroke: "#20D49C",
                  lineWidth: 4,
                  radius: 8,
                },
              },
              {
                id: "cross-comm-traffic",
                label: "通信-交通依赖关系",
                            weight: 25,
                dataProgress: 100,
                calcProgress: 0,
                style: {
                  fill: "#0F1520",
                  stroke: "#20D49C",
                  lineWidth: 4,
                  radius: 8,
                },
              },
              {
                id: "cross-traffic-power",
                label: "交通-电力依赖关系",
                            weight: 20,
                            dataProgress: 35,
                            calcProgress: 0,
                            style: {
                                fill: "#0F1520",
                                stroke: "#20D49C",
                                lineWidth: 4,
                                radius: 8,
                            },
                        },
                        {
                            id: "cross-risk-path",
                            label: "风险传播路径相似度",
                weight: 30,
                            dataProgress: 65,
                            calcProgress: 0,
                            highlighted: true, // 添加高亮标记
                            style: {
                                fill: "#1A3A2E", // 更特别的背景色
                                stroke: "#FF6B35", // 醒目的橙红色边框
                                lineWidth: 6, // 更粗的边框
                                radius: 12, // 更大的圆角
                                shadowColor: "rgba(255, 107, 53, 0.6)", // 发光效果
                                shadowBlur: 15,
                            },
                            children: [
                                {
                                    id: "cross-risk-cascade",
                                    label: "风险级联层数匹配度",
                                    weight: 35,
                                    dataProgress: 80,
                                    calcProgress: 0,
                                    style: {
                                        fill: "#0F1520",
                                        stroke: "#20D49C",
                                        lineWidth: 4,
                                        radius: 8,
                                    },
                                },
                                {
                                    id: "cross-risk-asset",
                                    label: "风险关联资产匹配度",
                                    weight: 35,
                                    dataProgress: 70,
                                    calcProgress: 0,
                                    style: {
                                        fill: "#0F1520",
                                        stroke: "#20D49C",
                                        lineWidth: 4,
                                        radius: 8,
                                    },
                                },
                                {
                                    id: "cross-risk-sequence",
                                    label: "风险传播时序一致性",
                                    weight: 30,
                                    dataProgress: 45,
                calcProgress: 0,
                style: {
                  fill: "#0F1520",
                  stroke: "#20D49C",
                  lineWidth: 4,
                  radius: 8,
                },
                                },
                            ],
              },
            ],
          },
        ],
      };

      // 初始化思维导图
      function initMindMap() {
        // 创建G6画布
        const container = document.getElementById("mind-map-container");
        const width = container.scrollWidth;
        const height = container.scrollHeight || 600;

        // 自定义节点
        G6.registerNode("mind-map-node", {
          draw: (cfg, group) => {
                    const { label, weight, result, dataProgress, calcProgress, highlighted } = cfg;
            const style = cfg.style || {};
            const isRoot = cfg.id === "root";
            const isLeaf = !cfg.children || cfg.children.length === 0;
            
            // 判断节点状态
            const isProcessing = result === "processing";
            const isCompleted = result === "completed";
                    const isHighlighted = highlighted === true; // 检查是否为高亮节点

                    // 节点内容
                    const nodeWidth = isRoot ? 400 : (isLeaf ? 400 : 400);
                    const nodeHeight = isRoot ? 160 : ((isLeaf || isHighlighted) ? 300 : 180);
                    const textColor = style.textColor || "#E0E6F0";
            
            // 根据节点状态设置样式
                    let nodeStyle = {};

                    if (isHighlighted) {
                        // 高亮节点使用特殊样式
                        nodeStyle = {
                            fill: style.fill || "#1A3A2E",
                            stroke: style.stroke || "#FF6B35",
                            lineWidth: style.lineWidth || 6,
                            radius: style.radius || 12,
                            shadowColor: style.shadowColor || "rgba(255, 107, 53, 0.6)",
                            shadowBlur: style.shadowBlur || 15,
                        };
                    } else {
                        // 普通节点样式
                        nodeStyle = {
              fill: isRoot ? "#1A202E" : (isProcessing ? "#2A3A5A" : (isCompleted ? "#1A202E" : "#1A202E")),
              stroke: isRoot ? "#0095FF" : (isProcessing ? "#FFB800" : (isCompleted ? "#00C48C" : "#E0E6F0")),
              lineWidth: isRoot ? 3 : 2,
              radius: 8,
            };
                    }
            
            // 创建节点背景
            const keyShape = group.addShape("rect", {
              attrs: {
                x: 0,
                y: 0,
                width: nodeWidth,
                height: nodeHeight,
                ...nodeStyle,
              },
              name: "node-shape",
            });
            
                    // 添加标题（高亮节点使用特殊颜色）
            group.addShape("text", {
              attrs: {
                text: label,
                x: nodeWidth / 2,
                y: isRoot ? 40 : 40,
                textAlign: "center",
                textBaseline: "middle",
                            fill: isHighlighted ? "#FFE5D1" : textColor, // 高亮节点使用暖色文字
                fontSize: 30,
                            fontWeight: isRoot ? "bold" : (isHighlighted ? "bold" : "normal"), // 高亮节点加粗
                cursor: "pointer",
              },
              name: "node-label",
            });

            // 非根节点显示计算结果和权重
            if (!isRoot) {
              // 添加计算结果
              group.addShape("text", {
                attrs: {
                  text: result !== undefined ? result : "计算中",
                  x: nodeWidth / 2,
                  y: 90,
                  textAlign: "center",
                  textBaseline: "middle",
                                fill: isHighlighted ? "#FF6B35" : (isProcessing ? "#FFB800" : (isCompleted ? "#00C48C" : "#E0E6F0")),
                  fontSize: 30,
                  fontWeight: "bold",
                },
                name: "node-result",
              });

              // 添加权重
              group.addShape("text", {
                attrs: {
                  text: `权重: ${weight || 0}%`,
                  x: nodeWidth / 2,
                  y: 140,
                  textAlign: "center",
                  textBaseline: "middle",
                                fill: isHighlighted ? "#FFE5D1" : textColor,
                  fontSize: 30,
                },
                name: "node-weight",
              });

                        // 叶子节点或非叶子节点都显示进度条
                        if (isLeaf || isHighlighted) {
                // 数据采集进度条
                const dataProgressBarWidth = nodeWidth - 40;
                const dataProgressBarHeight = 30;
                const dataProgressBarY = 190;

                // 进度条背景
                group.addShape("rect", {
                  attrs: {
                    x: 20,
                    y: dataProgressBarY,
                    width: dataProgressBarWidth,
                    height: dataProgressBarHeight,
                                    fill: isHighlighted ? "#2A1A1A" : "#2A3A5A",
                    radius: 4,
                  },
                  name: "data-progress-bg",
                });

                // 进度条
                group.addShape("rect", {
                  attrs: {
                    x: 20,
                    y: dataProgressBarY,
                    width: (dataProgressBarWidth * (dataProgress || 0)) / 100,
                    height: dataProgressBarHeight,
                                    fill: isHighlighted ? "#FF8C42" : "#0095FF",
                    radius: 4,
                  },
                  name: "data-progress-bar",
                });

                // 进度文字
                group.addShape("text", {
                  attrs: {
                    text: `数据采集: ${dataProgress || 0}%`,
                    x: nodeWidth / 2,
                    y: dataProgressBarY + dataProgressBarHeight / 2,
                    textAlign: "center",
                    textBaseline: "middle",
                                    fill: isHighlighted ? "#FFE5D1" : textColor,
                    fontSize: 30,
                  },
                  name: "data-progress-text",
                });

                // 指标计算进度条
                const calcProgressBarWidth = nodeWidth - 40;
                const calcProgressBarHeight = 30;
                const calcProgressBarY = 240;

                // 进度条背景
                group.addShape("rect", {
                  attrs: {
                    x: 20,
                    y: calcProgressBarY,
                    width: calcProgressBarWidth,
                    height: calcProgressBarHeight,
                                    fill: isHighlighted ? "#2A1A1A" : "#2A3A5A",
                    radius: 4,
                  },
                  name: "calc-progress-bg",
                });

                // 进度条
                group.addShape("rect", {
                  attrs: {
                    x: 20,
                    y: calcProgressBarY,
                    width: (calcProgressBarWidth * (calcProgress || 0)) / 100,
                    height: calcProgressBarHeight,
                                    fill: isHighlighted ? "#FF6B35" : "#00C48C",
                    radius: 4,
                  },
                  name: "calc-progress-bar",
                });

                // 进度文字
                group.addShape("text", {
                  attrs: {
                    text: `指标计算: ${calcProgress || 0}%`,
                    x: nodeWidth / 2,
                    y: calcProgressBarY + calcProgressBarHeight / 2,
                    textAlign: "center",
                    textBaseline: "middle",
                                    fill: isHighlighted ? "#FFE5D1" : textColor,
                    fontSize: 30,
                  },
                  name: "calc-progress-text",
                });
              }
            }

            // 如果是根节点，添加总体进度信息
            if (isRoot) {
              const totalDataProgress = dataProgress || 0;
              const totalCalcProgress = calcProgress || 0;

              // 数据采集进度条背景
              group.addShape("rect", {
                attrs: {
                  x: 10,
                  y: nodeHeight - 25,
                  width: nodeWidth - 20,
                  height: 6,
                  fill: "rgba(255, 255, 255, 0.1)",
                  radius: 3,
                },
                name: "root-data-progress-bg",
              });

              // 数据采集进度条填充
              group.addShape("rect", {
                attrs: {
                  x: 10,
                  y: nodeHeight - 25,
                  width: (nodeWidth - 20) * (totalDataProgress / 100),
                  height: 6,
                  fill: "#0095FF",
                  radius: 3,
                },
                name: "root-data-progress-fill",
              });

              // 计算进度条背景
              group.addShape("rect", {
                attrs: {
                  x: 10,
                  y: nodeHeight - 12,
                  width: nodeWidth - 20,
                  height: 6,
                  fill: "rgba(255, 255, 255, 0.1)",
                  radius: 3,
                },
                name: "root-calc-progress-bg",
              });

              // 计算进度条填充
              group.addShape("rect", {
                attrs: {
                  x: 10,
                  y: nodeHeight - 12,
                  width: (nodeWidth - 20) * (totalCalcProgress / 100),
                  height: 6,
                  fill: "#00C48C",
                  radius: 3,
                },
                name: "root-calc-progress-fill",
              });

              // 添加进度文本标签
              group.addShape("text", {
                attrs: {
                  text: `数据: ${totalDataProgress}%`,
                  x: 15,
                  y: nodeHeight - 34,
                  textAlign: "left",
                  textBaseline: "middle",
                  fill: "rgba(224, 230, 240, 0.9)",
                  fontSize: 14,
                },
                name: "root-data-progress-label",
              });

              group.addShape("text", {
                attrs: {
                  text: `计算: ${totalCalcProgress}%`,
                  x: nodeWidth - 15,
                  y: nodeHeight - 34,
                  textAlign: "right",
                  textBaseline: "middle",
                  fill: "rgba(224, 230, 240, 0.9)",
                  fontSize: 14,
                },
                name: "root-calc-progress-label",
              });
            }

            return keyShape;
          },
          update: (cfg, item) => {
            const group = item.getContainer();
            const nodeShape = group.find((e) => e.get("name") === "node-shape");
            const { dataProgress, calcProgress, result } = cfg;
            const style = cfg.style || {};
            const isLeaf = !cfg.children || cfg.children.length === 0;
            const isRoot = cfg.id === "root";
            
            // 判断节点状态
            const isProcessing = isLeaf ? 
                ((dataProgress !== undefined && dataProgress > 0 && dataProgress < 100) || 
                (calcProgress !== undefined && calcProgress > 0 && calcProgress < 100)) :
                (cfg.children && cfg.children.some(child => 
                    (child.dataProgress > 0 && child.dataProgress < 100) || 
                    (child.calcProgress > 0 && child.calcProgress < 100)));
                
            const isCompleted = isLeaf ? 
                ((dataProgress !== undefined && dataProgress === 100) && 
                (calcProgress !== undefined && calcProgress === 100)) :
                (cfg.children && cfg.children.every(child => 
                    (child.dataProgress === 100) && (child.calcProgress === 100)));
            
            // 根据状态设置节点样式
            let nodeStroke = style.stroke || "#3E9BFF";
            let nodeShadow = "rgba(0,0,0,0.3)";
            let nodeShadowBlur = 5;
            let strokeWidth = style.lineWidth || 1;
            
            if (isProcessing) {
                // 计算中节点发光
                nodeStroke = "#00C48C";
                nodeShadow = "rgba(0,196,140,0.6)";
                nodeShadowBlur = 10;
                strokeWidth = 2;
            } else if (isCompleted && result !== undefined) {
                // 完成节点突出显示
                nodeStroke = "#FFD700";
                nodeShadow = "rgba(255,215,0,0.4)";
                nodeShadowBlur = 8;
                strokeWidth = 2;
            }

            // 更新样式
            nodeShape.attr({
              stroke: nodeStroke,
              fill: style.fill || "#1A202E",
              lineWidth: strokeWidth,
              shadowColor: nodeShadow,
              shadowBlur: nodeShadowBlur
            });
          },
        });

        // 自定义边
        G6.registerEdge("mind-map-edge", {
          draw(cfg, group) {
            const { startPoint, endPoint } = cfg;
            const style = cfg.style || {};

            const shape = group.addShape("path", {
              attrs: {
                path: [
                  ["M", startPoint.x, startPoint.y],
                  [
                    "C",
                    startPoint.x + (endPoint.x - startPoint.x) / 2,
                    startPoint.y,
                    endPoint.x - (endPoint.x - startPoint.x) / 2,
                    endPoint.y,
                    endPoint.x,
                    endPoint.y,
                  ],
                ],
                stroke: style.stroke || "#3E9BFF",
                lineWidth: style.lineWidth || 3.5,
                endArrow: false,
              },
              name: "edge-shape",
            });

            return shape;
          },
          update: (cfg, item) => {
            const shape = item.get("keyShape");
            const { startPoint, endPoint } = cfg;
            const style = cfg.style || {};

            // 更新路径和样式
            shape.attr({
              path: [
                ["M", startPoint.x, startPoint.y],
                [
                  "C",
                  startPoint.x + (endPoint.x - startPoint.x) / 2,
                  startPoint.y,
                  endPoint.x - (endPoint.x - startPoint.x) / 2,
                  endPoint.y,
                  endPoint.x,
                  endPoint.y,
                ],
              ],
              stroke: style.stroke || "#3E9BFF",
              lineWidth: style.lineWidth || 3.5,
            });
          },
        });

        // 创建图实例
        graph = new G6.TreeGraph({
          container: "mind-map-container",
          width,
          height,
          linkCenter: true,
          modes: {
            default: [
              "drag-canvas",
              "zoom-canvas",
              "drag-node",
              {
                type: "tooltip",
                formatText(model) {
                  const { label, weight, result, dataProgress, calcProgress } =
                    model;
                  const isRoot = model.id === "root";
                  const isLeaf = !model.children || model.children.length === 0;

                  let text = `<div style="padding: 8px 0;"><strong>${label}</strong></div>`;

                  if (!isRoot) {
                    text += `<div>权重: ${weight}%</div>`;
                  }

                  if (result !== undefined) {
                    text += `<div>评估结果: ${result}</div>`;
                  }

                  if (isLeaf || isRoot) {
                    if (dataProgress !== undefined) {
                      text += `<div>数据采集: ${dataProgress}%</div>`;
                    }

                    if (calcProgress !== undefined) {
                      text += `<div>指标计算: ${calcProgress}%</div>`;
                    }
                  }

                  return text;
                },
              },
            ],
          },
          defaultNode: {
            type: "mind-map-node",
            anchorPoints: [
              [0, 0.5], // 左侧中点
              [1, 0.5], // 右侧中点
            ],
            style: {
              fill: "#1A202E",
              stroke: "#3E9BFF",
              lineWidth: 1,
              radius: 4,
              textColor: "#E0E6F0",
            },
          },
          defaultEdge: {
            type: "cubic-horizontal",
            style: {
              stroke: "#1b8a99",
              lineWidth: 4,
              endArrow: false,
            },
          },
          layout: {
            type: "mindmap",
            direction: "H",
            getHeight: () => {
              return 350; // 增加节点高度间距
            },
            getWidth: (node) => {
              if (node.id === "root") return 400;
              return node.children && node.children.length > 0 ? 400 : 400;
            },
            getVGap: () => {
              return 120; // 增加垂直间距
            },
            getHGap: () => {
              return 200; // 增加水平间距
            },
            // 添加防止节点重叠的配置
            radial: false, // 禁用径向布局
            nodeSep: 120, // 同一层级节点间距离
            rankSep: 250, // 不同层级之间的间距
            preventOverlap: true, // 防止节点重叠
            nodeSize: 400, // 节点大小
          },
          animate: true,
        });

        // 加载数据
        if (isFocused && focusedNode) {
          // 只显示该节点（无子节点）
          const singleNodeData = { ...focusedNode, children: [] };
          graph.data(singleNodeData);
        } else {
          graph.data(mindMapData);
        }
        graph.render();
        graph.fitView(20); // 适应视图，保留20的边距

        // 绑定事件
        graph.on("node:click", (e) => {
            const node = e.item.get("model");
            const tooltip = document.getElementById("nodeTooltip");
            
            // 设置当前节点（用于上下文菜单）
            currentNode = node;
            
            // 在工具提示框中显示节点信息
            let tooltipContent = `<div class="tooltip-title">${node.label}</div>`;
            
            if (node.weight) {
                tooltipContent += `<div class="tooltip-info">权重: ${node.weight}%</div>`;
            }
            
            if (node.dataProgress !== undefined) {
                tooltipContent += `<div class="tooltip-info">数据采集进度: ${node.dataProgress}%</div>`;
            }
            
            if (node.calcProgress !== undefined) {
                tooltipContent += `<div class="tooltip-info">计算进度: ${node.calcProgress}%</div>`;
            }
            
            if (node.result !== undefined) {
                tooltipContent += `<div class="tooltip-info">计算结果: ${node.result}</div>`;
            }
            
            tooltip.innerHTML = tooltipContent;
            
            // 显示工具提示框
            const bbox = e.item.getBBox();
            const point = graph.getCanvasByPoint(bbox.centerX, bbox.centerY);
            tooltip.style.left = point.x + "px";
            tooltip.style.top = point.y - 70 + "px";
            tooltip.style.display = "block";
            
            // 显示上下文菜单
            showContextMenu(e.clientX, e.clientY, node);
            
            // 更新关联任务卡片
            updateRelatedTasks(node);
        });

        // 新增：双击节点只显示该节点并切换右侧为算法配置
        graph.on("node:dblclick", (e) => {
          const node = e.item.get("model");
          focusedNode = node;
          isFocused = true;
          // 只显示该节点（无子节点）
          graph.changeData({ ...node, children: [] });
          // 居中显示该节点
          setTimeout(() => {
            const nodes = graph.getNodes();
            if (nodes.length === 1) {
              const item = nodes[0];
              const model = item.getModel();
              const nodeCenter = item.getBBox();
              // 获取画布中心点
              const container = graph.get('container');
              const width = container.scrollWidth;
              const height = container.scrollHeight;
              const canvasCenter = graph.getPointByCanvas(width / 2, height / 2);
              // 节点当前中心
              const nodeX = model.x;
              const nodeY = model.y;
              // 计算平移量
              const dx = canvasCenter.x - nodeX;
              const dy = canvasCenter.y - nodeY;
              graph.translate(dx, dy);
            }
          }, 0);
          // 右侧切换显示
          document.getElementById("relatedTasksContainer").style.display = "none";
          document.getElementById("algorithmConfigContainer").style.display = "block";
          // 初始化算法选择表单内容
          document.getElementById("algorithmSelect").value = node.selectedAlgorithm || "alg1";
          document.getElementById("algorithmParams").value = node.algorithmParams || "";
          // 显示返回按钮
          document.getElementById("backToMindMapBtn").style.display = "inline-flex";
        });

        // 函数：更新关联任务卡片
        function updateRelatedTasks(node) {
            // 更新当前指标名称
            const currentMetricName = document.getElementById('currentMetricName');
            if (currentMetricName) {
                currentMetricName.textContent = `当前指标：${node.label}`;
            }
            
            // 根据不同指标更新关联的采集任务和计算任务
            // 这里使用示例数据，实际应用中应该从后端获取数据
            const taskData = {
                '配电网拓扑结构精度': {
                    collection: {
                        name: '配电网设备参数采集任务',
                        type: '实时数据采集',
                        progress: 75,
                        startTime: '2024-04-01 08:30:00',
                        endTime: '2024-04-09 18:00:00',
                        status: 'running'
                    },
                    computation: {
                        name: '拓扑结构一致性计算',
                        type: '模型比对',
                        progress: 40,
                        startTime: '2024-04-02 10:15:00',
                        endTime: '2024-04-08 16:30:00',
                        status: 'waiting'
                    }
                },
                '负荷预测精度': {
                    collection: {
                        name: '区域负荷数据采集任务',
                        type: '历史数据采集',
                        progress: 100,
                        startTime: '2024-03-25 09:00:00',
                        endTime: '2024-04-01 17:00:00',
                        status: 'completed'
                    },
                    computation: {
                        name: '负荷预测算法评估',
                        type: '预测误差分析',
                        progress: 65,
                        startTime: '2024-04-01 18:00:00',
                        endTime: '2024-04-07 12:00:00',
                        status: 'running'
                    }
                },
                '电网故障响应特性': {
                    collection: {
                        name: '电网故障事件数据采集',
                        type: '事件数据采集',
                        progress: 88,
                        startTime: '2024-03-28 10:30:00',
                        endTime: '2024-04-05 15:00:00',
                        status: 'running'
                    },
                    computation: {
                        name: '故障响应时间分析',
                        type: '时序分析',
                        progress: 50,
                        startTime: '2024-04-03 09:00:00',
                        endTime: '2024-04-10 18:00:00',
                        status: 'running'
                    }
                },
                '通信网络容量仿真度': {
                    collection: {
                        name: '通信网络流量采集',
                        type: '网络流量采集',
                        progress: 95,
                        startTime: '2024-03-27 08:00:00',
                        endTime: '2024-04-03 16:00:00',
                        status: 'running'
                    },
                    computation: {
                        name: '网络容量分析计算',
                        type: '资源利用率分析',
                        progress: 60,
                        startTime: '2024-04-01 10:00:00',
                        endTime: '2024-04-06 18:00:00',
                        status: 'running'
                    }
                },
                '通信时延仿真度': {
                    collection: {
                        name: '通信网络时延数据采集',
                        type: '实时监测采集',
                        progress: 70,
                        startTime: '2024-03-30 09:30:00',
                        endTime: '2024-04-07 16:30:00',
                        status: 'running'
                    },
                    computation: {
                        name: '时延特性分析',
                        type: '统计分析',
                        progress: 35,
                        startTime: '2024-04-02 14:00:00',
                        endTime: '2024-04-09 12:00:00',
                        status: 'waiting'
                    }
                    },
                    '风险传播路径相似度': {
                        collection: {
                            name: '风险传播路径数据采集',
                            type: '事件数据采集',
                            progress: 80,
                            startTime: '2024-03-29 11:00:00',
                            endTime: '2024-04-06 17:00:00',
                            status: 'running'
                        },
                        computation: {
                            name: '风险传播路径分析',
                            type: '图论分析',
                            progress: 55,
                            startTime: '2024-04-02 10:30:00',
                            endTime: '2024-04-09 15:00:00',
                            status: 'running'
                        }
                    },
                    '风险级联层数匹配度': {
                        collection: {
                            name: '风险级联层数数据采集',
                            type: '结构数据采集',
                            progress: 90,
                            startTime: '2024-03-28 09:00:00',
                            endTime: '2024-04-05 16:00:00',
                            status: 'running'
                        },
                        computation: {
                            name: '风险级联层数计算',
                            type: '图论分析',
                            progress: 60,
                            startTime: '2024-04-01 11:00:00',
                            endTime: '2024-04-08 17:00:00',
                            status: 'running'
                        }
                    },
                    '风险关联资产匹配度': {
                        collection: {
                            name: '风险关联资产数据采集',
                            type: '关联数据采集',
                            progress: 75,
                            startTime: '2024-03-27 10:00:00',
                            endTime: '2024-04-04 18:00:00',
                            status: 'running'
                        },
                        computation: {
                            name: '风险关联资产计算',
                            type: '关联分析',
                            progress: 50,
                            startTime: '2024-04-01 12:00:00',
                            endTime: '2024-04-08 20:00:00',
                            status: 'running'
                        }
                    },
                    '风险传播时序一致性': {
                        collection: {
                            name: '风险传播时序数据采集',
                            type: '时序数据采集',
                            progress: 85,
                            startTime: '2024-03-26 11:00:00',
                            endTime: '2024-04-03 19:00:00',
                            status: 'running'
                        },
                        computation: {
                            name: '风险传播时序分析',
                            type: '时序分析',
                            progress: 45,
                            startTime: '2024-04-01 13:00:00',
                            endTime: '2024-04-09 21:00:00',
                            status: 'running'
                    }
                }
            };
            
            // 默认数据（当指标没有特定数据时使用）
            const defaultData = {
                collection: {
                    name: '数据采集任务',
                    type: '自动采集',
                    progress: 50,
                    startTime: '2024-04-01 00:00:00',
                    endTime: '2024-04-10 00:00:00',
                    status: 'running'
                },
                computation: {
                    name: '指标计算任务',
                    type: '标准计算',
                    progress: 30,
                    startTime: '2024-04-02 00:00:00',
                    endTime: '2024-04-11 00:00:00',
                    status: 'waiting'
                }
            };
            
            // 获取当前指标的数据，如果没有则使用默认数据
            const data = taskData[node.label] || defaultData;
            
            // 更新采集任务卡片
            updateCollectionTaskCard(data.collection);
            
            // 更新计算任务卡片
            updateComputationTaskCard(data.computation);
        }
        
        // 更新采集任务卡片
        function updateCollectionTaskCard(data) {
            // 更新任务名称
            document.getElementById('collectionTaskName').textContent = data.name;
            
            // 更新采集类型
            document.getElementById('collectionTaskType').textContent = data.type;
            
            // 更新进度条
            document.getElementById('collectionTaskProgress').style.width = `${data.progress}%`;
            
            // 更新进度值
            document.getElementById('collectionTaskProgressValue').textContent = `${data.progress}%`;
            
            // 更新时间
            document.getElementById('collectionTaskStartTime').textContent = data.startTime;
            document.getElementById('collectionTaskEndTime').textContent = data.endTime;
            
            // 更新状态
            const statusBadge = document.querySelector('#collectionTaskCard .status-badge');
            statusBadge.className = 'status-badge';
            
                switch (data.status) {
                case 'running':
                    statusBadge.classList.add('status-running');
                    statusBadge.textContent = '运行中';
                    break;
                case 'waiting':
                    statusBadge.classList.add('status-waiting');
                    statusBadge.textContent = '等待中';
                    break;
                case 'completed':
                    statusBadge.classList.add('status-completed');
                    statusBadge.textContent = '已完成';
                    break;
                case 'error':
                    statusBadge.classList.add('status-error');
                    statusBadge.textContent = '错误';
                    break;
                default:
                    statusBadge.classList.add('status-running');
                    statusBadge.textContent = '运行中';
            }
            
            // 更新按钮
            const buttons = document.querySelectorAll('#collectionTaskCard .task-card-footer button');
            if (data.status === 'completed') {
                buttons[1].innerHTML = '<i class="fas fa-redo"></i> 重新运行';
            } else if (data.status === 'running') {
                buttons[1].innerHTML = '<i class="fas fa-pause"></i> 暂停任务';
            } else if (data.status === 'waiting') {
                buttons[1].innerHTML = '<i class="fas fa-play"></i> 启动任务';
            }
        }
        
        // 更新计算任务卡片
        function updateComputationTaskCard(data) {
            // 更新任务名称
            document.getElementById('computationTaskName').textContent = data.name;
            
            // 更新计算类型
            document.getElementById('computationTaskType').textContent = data.type;
            
            // 更新进度条
            document.getElementById('computationTaskProgress').style.width = `${data.progress}%`;
            
            // 更新进度值
            document.getElementById('computationTaskProgressValue').textContent = `${data.progress}%`;
            
            // 更新时间
            document.getElementById('computationTaskStartTime').textContent = data.startTime;
            document.getElementById('computationTaskEndTime').textContent = data.endTime;
            
            // 更新状态
            const statusBadge = document.querySelector('#computationTaskCard .status-badge');
            statusBadge.className = 'status-badge';
            
                switch (data.status) {
                case 'running':
                    statusBadge.classList.add('status-running');
                    statusBadge.textContent = '运行中';
                    break;
                case 'waiting':
                    statusBadge.classList.add('status-waiting');
                    statusBadge.textContent = '等待中';
                    break;
                case 'completed':
                    statusBadge.classList.add('status-completed');
                    statusBadge.textContent = '已完成';
                    break;
                case 'error':
                    statusBadge.classList.add('status-error');
                    statusBadge.textContent = '错误';
                    break;
                default:
                    statusBadge.classList.add('status-waiting');
                    statusBadge.textContent = '等待中';
            }
            
            // 更新按钮
            const buttons = document.querySelectorAll('#computationTaskCard .task-card-footer button');
            if (data.status === 'completed') {
                buttons[1].innerHTML = '<i class="fas fa-redo"></i> 重新运行';
                buttons[1].className = 'btn btn-sm';
            } else if (data.status === 'running') {
                buttons[1].innerHTML = '<i class="fas fa-pause"></i> 暂停任务';
                buttons[1].className = 'btn btn-sm';
            } else if (data.status === 'waiting') {
                buttons[1].innerHTML = '<i class="fas fa-play"></i> 启动任务';
                buttons[1].className = 'btn btn-sm btn-primary';
            }

                // 重新绑定查看详情按钮事件
                setTimeout(() => {
                    bindTaskDetailButtons();
                }, 100);
        }

        graph.on("node:contextmenu", (e) => {
          e.preventDefault();
          const nodeModel = e.item.get("model");
          currentNode = nodeModel;
          showContextMenu(e.clientX, e.clientY, nodeModel);
        });

        // 创建右键菜单
        function showContextMenu(x, y, node) {
          if (nodeMenu) {
            nodeMenu.remove();
          }

          const isRoot = node.id === "root";

          nodeMenu = document.createElement("div");
          nodeMenu.className = "node-form";
          nodeMenu.style.position = "fixed";
          nodeMenu.style.left = x + "px";
          nodeMenu.style.top = y + "px";
          nodeMenu.style.width = "150px";
          nodeMenu.style.zIndex = "1000";

          // 创建菜单项
          let menuContent = "";

          if (!isRoot) {
            menuContent += `<div class="mind-map-btn" onclick="editNode('${node.id}')"><i class="fas fa-edit"></i> 编辑节点</div>`;
            menuContent += `<div class="mind-map-btn" onclick="addChildNode('${node.id}')"><i class="fas fa-plus"></i> 添加子节点</div>`;
            menuContent += `<div class="mind-map-btn danger" onclick="deleteNode('${node.id}')"><i class="fas fa-trash"></i> 删除节点</div>`;
          } else {
            menuContent += `<div class="mind-map-btn" onclick="editNode('${node.id}')"><i class="fas fa-edit"></i> 编辑节点</div>`;
            menuContent += `<div class="mind-map-btn" onclick="addChildNode('${node.id}')"><i class="fas fa-plus"></i> 添加子节点</div>`;
          }

          nodeMenu.innerHTML = menuContent;
          document.body.appendChild(nodeMenu);

          // 点击其他区域关闭菜单
          document.addEventListener("click", hideContextMenu);
        }

        function hideContextMenu() {
          if (nodeMenu) {
            nodeMenu.remove();
            nodeMenu = null;
          }
          document.removeEventListener("click", hideContextMenu);
        }

        // 绑定按钮事件
        document.getElementById("zoomInBtn").addEventListener("click", () => {
          const zoom = graph.getZoom();
          graph.zoomTo(zoom * 1.2);
        });

        document.getElementById("zoomOutBtn").addEventListener("click", () => {
          const zoom = graph.getZoom();
          graph.zoomTo(zoom / 1.2);
        });

        document.getElementById("fitViewBtn").addEventListener("click", () => {
          graph.fitView(20);
        });

        document
          .getElementById("saveLayoutBtn")
          .addEventListener("click", () => {
            showNotification("布局已保存", "success");
          });

        document.getElementById("addNodeBtn").addEventListener("click", () => {
          if (!currentNode) {
            currentNode = mindMapData;
          }
          addChildNode(currentNode.id);
        });

        // 自适应窗口大小
        window.addEventListener("resize", () => {
          if (!graph || graph.get("destroyed")) return;
          const container = document.getElementById("mind-map-container");
          if (!container) return;
          graph.changeSize(container.scrollWidth, container.scrollHeight);
        });
      }

      // 计算圆弧路径
      function describeArc(x, y, radius, startAngle, endAngle) {
        const start = polarToCartesian(x, y, radius, endAngle);
        const end = polarToCartesian(x, y, radius, startAngle);
        const largeArcFlag = endAngle - startAngle <= 180 ? 0 : 1;

        return [
          ["M", start.x, start.y],
          ["A", radius, radius, 0, largeArcFlag, 0, end.x, end.y],
        ];
      }

      function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
        const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;
        return {
          x: centerX + radius * Math.cos(angleInRadians),
          y: centerY + radius * Math.sin(angleInRadians),
        };
      }

      // 编辑节点
      function editNode(nodeId) {
        const form = document.getElementById("nodeForm");
        const nodeData = findNodeById(mindMapData, nodeId);
        if (!nodeData) return;

        currentNode = nodeData;

        // 填充表单
        document.getElementById("nodeId").value = nodeData.id;
        document.getElementById("nodeName").value = nodeData.label;
        document.getElementById("nodeWeight").value = nodeData.weight || "";
        document.getElementById("nodeType").value = nodeData.nodeType || "composite";
        document.getElementById("nodeDescription").value = nodeData.description || "";

        const isLeaf = !nodeData.children || nodeData.children.length === 0;
        const leafFields = document.getElementById("leafNodeFields");

        if (isLeaf) {
          leafFields.style.display = "block";
        } else {
          leafFields.style.display = "none";
        }

        // 显示表单
        form.style.display = "block";
        form.style.left = "50%";
        form.style.top = "50%";
        form.style.transform = "translate(-50%, -50%)";

        // 绑定事件
        document.getElementById("saveNodeEdit").onclick = saveNodeEdit;
        document.getElementById("cancelNodeEdit").onclick = cancelNodeEdit;
      }

      // 添加子节点
      function addChildNode(parentId) {
        const parentNode = findNodeById(mindMapData, parentId);
        if (!parentNode) return;

        // 生成唯一ID
        const newId = "node-" + Date.now();

        // 创建新节点
        const newNode = {
          id: newId,
          label: "新指标",
          weight: 0,
          dataProgress: 0,
          calcProgress: 0,
          style: {
            fill: "#0F1520",
            stroke: "#3E9BFF",
            lineWidth: 4,
            radius: 8,
          },
        };

        // 添加到父节点
        if (!parentNode.children) {
          parentNode.children = [];
        }
        parentNode.children.push(newNode);

        // 更新图形
        graph.changeData(mindMapData);
        graph.fitView(20);

        // 编辑新节点
        editNode(newId);

        showNotification("已添加新指标节点", "success");
      }

      // 删除节点
      function deleteNode(nodeId) {
        // 不能删除根节点
        if (nodeId === "root") {
          showNotification("根节点不能被删除", "error");
          return;
        }

        // 查找节点并删除
        const result = removeNodeById(mindMapData, nodeId);
        if (result) {
          // 更新图形
          graph.changeData(mindMapData);
          graph.fitView(20);
          showNotification("指标节点已删除", "success");
        } else {
          showNotification("未找到节点", "error");
        }
      }

      // 保存节点编辑
      function saveNodeEdit() {
        const form = document.getElementById("nodeForm");
        const nodeId = document.getElementById("nodeId").value;
        const nodeName = document.getElementById("nodeName").value;
        const nodeWeight = parseFloat(
          document.getElementById("nodeWeight").value
        );
        const nodeDescription = document.getElementById("nodeDescription").value;

        const node = findNodeById(mindMapData, nodeId);
        if (!node) return;

        // 更新节点数据
        node.label = nodeName;
        if (!isNaN(nodeWeight)) {
          node.weight = nodeWeight;
        }
        node.nodeType = document.getElementById("nodeType").value;
        node.description = nodeDescription;

        // 更新图形
        graph.changeData(mindMapData);

        // 隐藏表单
        form.style.display = "none";

        showNotification("指标节点已更新", "success");
      }

      // 取消编辑
      function cancelNodeEdit() {
        const form = document.getElementById("nodeForm");
        form.style.display = "none";
      }

      // 查找节点
      function findNodeById(data, id) {
        if (data.id === id) {
          return data;
        }

        if (data.children) {
          for (let i = 0; i < data.children.length; i++) {
            const found = findNodeById(data.children[i], id);
            if (found) {
              return found;
            }
          }
        }

        return null;
      }

      // 删除节点
      function removeNodeById(data, id) {
        if (data.children) {
          for (let i = 0; i < data.children.length; i++) {
            if (data.children[i].id === id) {
              data.children.splice(i, 1);
              return true;
            }

            if (removeNodeById(data.children[i], id)) {
              return true;
            }
          }
        }

        return false;
      }

      // 在文档加载后初始化思维导图
      document.addEventListener("DOMContentLoaded", () => {
        // 备份原始数据
        originalMindMapData = JSON.parse(JSON.stringify(mindMapData));
        initMindMap();
      });

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化主题切换功能
            const themeSwitch = document.querySelector('.theme-switch');
            const htmlRoot = document.documentElement;
            
            // 检查本地存储中的主题设置，默认为暗色主题
            const savedTheme = localStorage.getItem('theme');
            // 只有当明确保存了"light"时才应用亮色主题
            if (savedTheme === 'light') {
                htmlRoot.classList.add('light-theme');
            } else {
                // 如果没有保存或者保存的不是"light"，则确保是暗色主题
                localStorage.setItem('theme', 'dark');
            }
            
            // 切换主题的函数
            function toggleTheme() {
                if (htmlRoot.classList.contains('light-theme')) {
                    htmlRoot.classList.remove('light-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    htmlRoot.classList.add('light-theme');
                    localStorage.setItem('theme', 'light');
                }
                
                // 重新触发窗口大小改变事件，以便图表可以重新渲染
                window.dispatchEvent(new Event('resize'));
            }
            
            // 为主题切换按钮添加点击事件
            if (themeSwitch) {
                themeSwitch.addEventListener('click', toggleTheme);
            }
        });

        // 任务选项数据
        const dataTaskOptions = [
          { id: 'collect1', name: '配电网设备参数采集任务' },
          { id: 'collect2', name: '区域负荷数据采集任务' },
          { id: 'collect3', name: '电网故障事件数据采集' },
          { id: 'collect4', name: '通信网络流量采集' },
          { id: 'collect5', name: '通信网络时延数据采集' }
        ];
        const calcTaskOptions = [
          { id: 'calc1', name: '拓扑结构一致性计算' },
          { id: 'calc2', name: '负荷预测算法评估' },
          { id: 'calc3', name: '故障响应时间分析' },
          { id: 'calc4', name: '网络容量分析计算' },
          { id: 'calc5', name: '时延特性分析' }
        ];
        function fillSelectOptions(select, options, selectedId) {
          select.innerHTML = '';
          options.forEach(opt => {
            const option = document.createElement('option');
            option.value = opt.id;
            option.textContent = opt.name;
            if (opt.id === selectedId) option.selected = true;
            select.appendChild(option);
          });
        }

        // 算法配置相关按钮事件
        document.addEventListener("DOMContentLoaded", function () {
          const backBtn = document.getElementById("backToMindMapBtn");
          if (backBtn) {
            backBtn.addEventListener("click", function () {
              isFocused = false;
              focusedNode = null;
              // 恢复原始思维导图
              if (graph) {
                graph.changeData(originalMindMapData);
                graph.fitView(20);
              }
              // 右侧切换显示
              document.getElementById("relatedTasksContainer").style.display = "block";
              document.getElementById("algorithmConfigContainer").style.display = "none";
            });
          }
          const saveAlgBtn = document.getElementById("saveAlgorithmConfigBtn");
          if (saveAlgBtn) {
            saveAlgBtn.addEventListener("click", function () {
              if (focusedNode) {
                focusedNode.selectedAlgorithm = document.getElementById("algorithmSelect").value;
                focusedNode.algorithmParams = document.getElementById("algorithmParams").value;
                showNotification("算法配置已保存", "success");
              }
            });
          }
        });

        // 关闭拓扑结构详情弹窗
        function closeTopoModal() {
          const modal = document.getElementById("topoModal");
          modal.classList.add("hide");
          setTimeout(() => {
            modal.style.display = "none";
            modal.classList.remove("hide");
            // 清空iframe内容以释放资源
            document.getElementById("topoIframe").src = "";
          }, 300);
        }

        // 显示拓扑结构详情弹窗
        function showTopoModal() {
          const modal = document.getElementById("topoModal");
          const iframe = document.getElementById("topoIframe");
          
          // 设置iframe源地址为13.Topo.html
          iframe.src = "13.Topo.html";
          
          // 显示弹窗
          modal.style.display = "block";
          modal.classList.add("show");
          
          // 移除show类，为下次显示做准备
          setTimeout(() => {
            modal.classList.remove("show");
          }, 300);
        }

        // 点击遮罩层关闭弹窗
        document.addEventListener('DOMContentLoaded', function() {
          const modal = document.getElementById("topoModal");
          const modalContent = modal.querySelector(".modal-content");
          
          modal.addEventListener('click', function(e) {
            if (e.target === modal) {
              closeTopoModal();
            }
          });
          
          // 阻止点击弹窗内容时关闭弹窗
          modalContent.addEventListener('click', function(e) {
            e.stopPropagation();
          });
          
          // 为查看详情按钮添加点击事件
          const viewDetailBtn = document.getElementById("viewComputationTaskDetail");
          if (viewDetailBtn) {
            viewDetailBtn.addEventListener('click', function() {
              showTopoModal();
            });
          }
          
          // ESC键关闭弹窗
          document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
              const modal = document.getElementById("topoModal");
              if (modal.style.display === 'block') {
                closeTopoModal();
              }
            }
          });
        });
    </script>
</body>
</html>
