<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>首页 - 城市级关基级联的网络仿真度评估系统</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* 页面整体放大120% */
      html {
        zoom: 110%;
      }

      :root {
        --primary-color: #0096ff;
        --secondary-color: #00e0ff;
        --background-dark: #0f1520;
        --background-card: #1a202e;
        --text-color: #e0e6f0;
        --text-secondary: #a0a8b8;
        --border-color: #2a3142;
        --highlight-color: #3e9bff;
        --success-color: #00c48c;
        --warning-color: #ffb946;
        --danger-color: #f25767;
        --info-color: #0095ff;
      }

      /* 亮色主题变量 */
      :root.light-theme {
        --primary-color: #0078d4;
        --secondary-color: #00a2e0;
        --background-dark: #ffffff;
        --background-card: #ffffff;
        --text-color: #333333;
        --text-secondary: #666666;
        --border-color: #e0e6ed;
        --highlight-color: #106ebe;
        --success-color: #0a9d5a;
        --warning-color: #f29d41;
        --danger-color: #e74c3c;
        --info-color: #0078d4;
      }

      /* 主题切换开关样式 */
      .theme-switch {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 60px;
        height: 30px;
        background: rgba(0, 149, 255, 0.15);
        border-radius: 30px;
        padding: 3px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
      }

      .theme-switch:hover {
        transform: scale(1.05);
      }

      .theme-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-color);
        font-size: 14px;
      }

      .theme-switch-slider {
        position: absolute;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: var(--primary-color);
        transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        left: 3px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      }

      :root.light-theme .theme-switch-slider {
        transform: translateX(30px);
      }

      /* 亮色主题下的按钮样式 */
      :root.light-theme .btn-primary {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }

      :root.light-theme .btn-primary:hover {
        background-color: var(--highlight-color);
        border-color: var(--highlight-color);
      }

      :root.light-theme .action-btn.primary {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }

      :root.light-theme .action-btn.primary:hover {
        background-color: var(--highlight-color);
        border-color: var(--highlight-color);
      }

      /* 弹窗样式 */
      .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
      }

      .modal-content {
        background-color: var(--background-card);
        border-radius: 12px;
        width: 650px;
        max-width: 90%;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
        max-height: 80vh;
        display: flex;
        flex-direction: column;
      }

      .modal-header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
      }

      .modal-header h3 {
        font-size: 22px;
        font-weight: 600;
        margin: 0;
        color: var(--text-color);
      }

      .fas.fa-times {
        color: red; /* 设置颜色为红色 */
        font-size: 18px; /* 设置字体大小 */
      }

      .close-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 28px;
        cursor: pointer;
        padding: 0;
        line-height: 1;
      }

      .modal-body {
        padding: 24px;
        overflow-y: auto;
        flex: 1;
      }

      .form-group {
        margin-bottom: 24px;
      }

      .form-group label {
        display: block;
        margin-bottom: 10px;
        color: var(--text-color);
        font-size: 16px;
        font-weight: 500;
      }

      .radio-group {
        display: flex;
        gap: 24px;
      }

      .radio-label {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        color: var(--text-color);
        font-size: 16px;
      }

      .radio-label input[type='radio'] {
        margin: 0;
        width: 18px;
        height: 18px;
      }

      .form-select {
        width: 100%;
        padding: 12px 16px;
        background-color: var(--background-dark);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        color: var(--text-color);
        font-size: 16px;
      }

      textarea.form-select {
        min-height: 120px;
        resize: vertical;
        line-height: 1.5;
      }

      .form-select:focus {
        border-color: var(--primary-color);
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 149, 255, 0.2);
      }

      .modal-footer {
        padding: 20px 24px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        flex-shrink: 0;
      }

      .btn {
        padding: 12px 24px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        transition: all 0.2s ease;
      }

      .btn-primary {
        background-color: var(--primary-color);
        color: white;
      }

      .btn-primary:hover {
        background-color: var(--highlight-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 149, 255, 0.3);
      }

      .btn-secondary {
        background-color: var(--background-dark);
        color: var(--text-color);
        border: 1px solid var(--border-color);
      }

      .btn-secondary:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      /* Logo样式 */
      .logo {
        zoom: 110%;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 0 10px;
      }

      .logo-icon {
        font-size: 24px;
        color: var(--primary-color);
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        filter: drop-shadow(0 0 2px rgba(0, 224, 255, 0.4));
      }

      .logo span {
        font-size: 18px;
        font-weight: 600;
        letter-spacing: 0.5px;
        background: linear-gradient(90deg, var(--text-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
      }

      body {
        background-color: var(--background-dark);
        color: var(--text-color);
        line-height: 1.6;
        min-height: 100vh;
      }

      .container {
        width: 100%;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .admin-info {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        background-color: rgba(0, 149, 255, 0.1);
        border-radius: 20px;
        color: var(--primary-color);
        font-size: 14px;
        transition: all 0.2s ease;
        position: absolute;
        top: 20px;
        right: 30px;
      }

      .admin-info:hover {
        background-color: rgba(0, 149, 255, 0.2);
      }

      .admin-info i {
        font-size: 16px;
      }

      .nav-tools {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background-color: var(--background-card);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        color: var(--text-color);
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .action-btn.primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
      }

      .action-btn.primary:hover {
        background-color: var(--highlight-color);
      }

      /* 主要内容区域 */
      .main-content {
        padding: 20px 20px;
        width: 100%;
        margin: 0 auto;
        background: linear-gradient(180deg, rgba(26, 32, 46, 0.3) 0%, rgba(15, 21, 32, 0.8) 100%);
        flex: 1;
      }

      /* 页面标题区域 */
      .page-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        /* 移除原有的直线边框 */
        /* border-bottom: 1px solid rgba(42, 49, 66, 0.3); */
        position: relative;
      }

      /* 添加科技感曲线装饰 */
      .page-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: transparent;
        /* 使用多重渐变创建曲线效果 */
        background-image:
          radial-gradient(circle at 20% 50%, rgba(0, 149, 255, 0.25) 0%, transparent 50%),
          radial-gradient(circle at 40% 50%, rgba(0, 224, 255, 0.15) 0%, transparent 40%),
          radial-gradient(circle at 60% 50%, rgba(0, 149, 255, 0.2) 0%, transparent 45%),
          radial-gradient(circle at 80% 50%, rgba(0, 224, 255, 0.15) 0%, transparent 40%),
          linear-gradient(90deg, transparent 5%, rgba(42, 49, 66, 0.2) 50%, transparent 95%);
        filter: blur(0.5px);
        opacity: 0.6;
        transform: scaleY(2);
      }

      /* 流程卡片容器 */
      .process-container {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        margin-top: 15px;
        position: relative;
        flex-wrap: nowrap;
        padding: 15px 0;
      }

      /* 评估任务卡片容器 */
      .assessment-container {
        display: grid;
        grid-template-columns: 3fr 3fr 3fr 1fr;
        gap: 12px;
        margin-bottom: 15px;
      }

      /* 流程卡片样式 */
      .process-card {
        background: linear-gradient(145deg, rgba(26, 32, 46, 0.8), rgba(15, 21, 32, 0.9));
        border: 1px solid rgba(62, 155, 255, 0.2);
        border-radius: 10px;
        padding: 20px 18px 18px;
        text-align: center;
        position: relative;
        transition:
          all 0.3s ease,
          transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        cursor: pointer;
        flex: 1;
        min-width: 200px;
        box-shadow:
          0 4px 10px rgba(0, 0, 0, 0.1),
          0 0 0 1px rgba(62, 155, 255, 0.05);
        overflow: visible;
        z-index: 1;
      }

      .process-card:hover {
        transform: translateY(-8px);
        border-color: var(--primary-color);
        box-shadow:
          0 12px 28px rgba(0, 0, 0, 0.2),
          0 0 20px rgba(0, 149, 255, 0.1),
          0 0 0 1px rgba(62, 155, 255, 0.3);
      }

      /* 发光效果 */
      .process-card::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        border-radius: 12px;
        background: linear-gradient(
          45deg,
          var(--primary-color),
          transparent,
          var(--secondary-color)
        );
        opacity: 0.1;
        z-index: -1;
        transition: opacity 0.3s ease;
      }

      .process-card:hover::before {
        opacity: 0.25;
      }

      .process-card::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -20px;
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), rgba(42, 49, 66, 0.1));
        z-index: 0;
      }

      .process-card:last-child::after {
        display: none;
      }

      .process-number {
        position: absolute;
        top: -22px;
        left: 50%;
        transform: translateX(-50%);
        width: 44px;
        height: 44px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 20px;
        z-index: 5;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        box-shadow: 0 4px 15px rgba(0, 149, 255, 0.4);
        border: 3px solid var(--background-dark);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      .process-icon {
        width: 70px;
        height: 70px;
        margin: 16px auto 20px;
        background: linear-gradient(135deg, rgba(0, 149, 255, 0.1), rgba(0, 224, 255, 0.05));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: var(--primary-color);
        position: relative;
        transition: all 0.3s ease;
        box-shadow: 0 0 20px rgba(0, 149, 255, 0.1);
      }

      .process-icon::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: linear-gradient(
          45deg,
          var(--primary-color),
          transparent,
          var(--secondary-color)
        );
        opacity: 0.15;
        z-index: -1;
      }

      .process-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: var(--text-color);
        text-shadow: 0 0 10px rgba(0, 224, 255, 0.1);
        letter-spacing: 0.5px;
      }

      .process-description {
        font-size: 15px;
        color: var(--text-secondary);
        margin-bottom: 20px;
        transition: all 0.3s ease;
      }

      .process-card:hover .process-description {
        color: var(--text-color);
      }

      .process-details {
        text-align: center;
        font-size: 14px;
        color: var(--text-secondary);
        margin-top: 18px;
        padding-top: 18px;
        border-top: 1px solid rgba(42, 49, 66, 0.5);
      }

      .process-details ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .process-details li {
        margin-bottom: 10px;
        text-align: center;
        transition: all 0.3s ease;
      }

      .process-details li:hover {
        color: var(--text-color);
        transform: translateX(3px);
      }

      .process-details li i {
        margin-right: 10px;
        font-size: 14px;
        color: var(--primary-color);
        transition: all 0.3s ease;
      }

      .process-details li:hover i {
        color: var(--secondary-color);
      }

      /* 流程卡片高亮样式 */
      .process-card.completed {
        border-color: var(--success-color);
        background: linear-gradient(145deg, rgba(0, 196, 140, 0.05), rgba(15, 21, 32, 0.8));
      }

      .process-card.completed .process-icon {
        background-color: rgba(0, 196, 140, 0.1);
        color: var(--success-color);
      }

      .process-card.completed .process-number {
        background: linear-gradient(135deg, var(--success-color), #00e0a0);
      }

      .process-card.completed::before {
        background: linear-gradient(45deg, var(--success-color), transparent, #00e0a0);
      }

      .process-card.in-progress {
        border-color: var(--primary-color);
        background: linear-gradient(145deg, rgba(0, 149, 255, 0.08), rgba(15, 21, 32, 0.8));
        animation: glowing 2s infinite;
      }

      .process-card.in-progress .process-icon {
        background-color: rgba(0, 149, 255, 0.15);
        color: var(--primary-color);
      }

      /* Loading 效果 */
      .process-card.in-progress .process-icon::before {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        border: 2px solid var(--primary-color);
        border-top-color: transparent;
        border-radius: 50%;
        animation: spin 1.5s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .process-card.not-started {
        opacity: 0.6;
      }

      /* 发光动画 */
      @keyframes glowing {
        0% {
          box-shadow: 0 0 10px rgba(0, 149, 255, 0.3);
        }
        50% {
          box-shadow: 0 0 25px rgba(0, 149, 255, 0.5);
        }
        100% {
          box-shadow: 0 0 10px rgba(0, 149, 255, 0.3);
        }
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(0, 149, 255, 0.6);
        }
        70% {
          box-shadow: 0 0 0 10px rgba(0, 149, 255, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(0, 149, 255, 0);
        }
      }

      /* 评估任务卡片样式 */
      .assessment-card {
        background: linear-gradient(135deg, rgba(26, 32, 46, 0.9), rgba(15, 21, 32, 0.95));
        border: 1px solid rgba(62, 155, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: left;
        position: relative;
        transition: all 0.3s ease;
        cursor: pointer;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }

      .status-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 3px 8px;
        border-radius: 16px;
        font-size: 10px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 3px;
        backdrop-filter: blur(4px);
        z-index: 2;
      }

      .status-badge i {
        font-size: 10px;
      }

      .status-badge.in-progress {
        background-color: rgba(0, 149, 255, 0.15);
        color: var(--primary-color);
        box-shadow: 0 0 10px rgba(0, 149, 255, 0.2);
      }

      .status-badge.completed {
        background-color: rgba(0, 196, 140, 0.15);
        color: var(--success-color);
        box-shadow: 0 0 10px rgba(0, 196, 140, 0.2);
      }

      .status-badge.not-started {
        background-color: rgba(160, 168, 184, 0.15);
        color: var(--text-secondary);
      }

      .assessment-card:hover {
        transform: translateY(-3px);
        border-color: rgba(62, 155, 255, 0.3);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      }

      .assessment-card.active {
        border: none;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
        transform: translateY(-5px) scale(1.05);
        position: relative;
        z-index: 10;
        transition: all 0.3s ease;
      }

      /* 当前选中任务的边框效果 */
      .assessment-card.active::before {
        content: '';
        position: absolute;
        inset: 0;
        padding: 2px; /* 边框厚度 */
        border-radius: 10px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: 1;
        pointer-events: none;
      }

      .assessment-card.active::after {
        content: '';
        position: absolute;
        inset: -1px;
        border-radius: 11px;
        background: linear-gradient(135deg, rgba(0, 149, 255, 0.1), rgba(0, 224, 255, 0.1));
        filter: blur(6px);
        z-index: -1;
      }

      .assessment-icon {
        width: 32px;
        height: 32px;
        margin-bottom: 8px;
        background: linear-gradient(135deg, rgba(0, 149, 255, 0.08), rgba(0, 224, 255, 0.05));
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: var(--primary-color);
      }

      .assessment-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 3px;
        color: var(--text-color);
        line-height: 1.2;
      }

      .assessment-description {
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 3px;
        color: var(--text-color);
        line-height: 1.2;
      }

      .assessment-info {
        margin: 8px 0;
        font-size: 11px;
        color: var(--text-secondary);
      }

      .assessment-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 3px;
      }

      .assessment-info-item i {
        width: 12px;
        margin-right: 5px;
        color: var(--primary-color);
        font-size: 11px;
      }

      .progress-container {
        margin: 8px 0;
      }

      .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 11px;
        color: var(--text-secondary);
      }

      .progress-bar {
        width: 100%;
        height: 4px;
        background-color: rgba(42, 49, 66, 0.5);
        border-radius: 2px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
        transition: width 0.3s ease;
        box-shadow: 0 0 4px rgba(0, 149, 255, 0.3);
      }

      .assessment-actions {
        display: flex;
        gap: 6px;
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid rgba(42, 49, 66, 0.5);
      }

      .action-button {
        flex: 1;
        padding: 5px 3px;
        border: none;
        border-radius: 3px;
        background-color: rgba(42, 49, 66, 0.3);
        color: var(--text-color);
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
      }

      .action-button i {
        font-size: 11px;
        transition: transform 0.2s ease;
      }

      .action-button:hover {
        background-color: var(--primary-color);
        color: white;
      }

      .action-button:active {
        transform: scale(0.98);
      }

      .action-button.delete:hover {
        background-color: var(--danger-color);
      }

      /* 响应式设计 */
      @media (max-width: 1024px) {
        .process-container {
          flex-wrap: wrap;
        }
        .process-card {
          flex: 1 1 calc(50% - 24px);
          min-width: 280px;
        }
        .process-card::after {
          display: none;
        }
      }

      @media (max-width: 640px) {
        .process-card {
          flex: 1 1 100%;
        }
      }

      /* 评估任务区域 */
      .assessment-section {
        margin: 20px 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
      }

      .section-title-container {
        display: flex;
        flex-direction: row;
      }

      .section-title-text {
        display: flex;
        flex-direction: column;
      }

      .section-title-group {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        color: var(--text-color);
      }

      .section-title-group i {
        font-size: 24px;
        margin-right: 12px;
        color: var(--primary-color);
      }

      .section-title {
        font-size: 18px;
        margin: 0;
        font-weight: 600;
      }

      .section-title-description {
        font-size: 13px;
        color: var(--text-secondary);
        margin-top: 5px;
        font-weight: normal;
        max-width: 500px;
      }

      /* 新建任务按钮 */
      .new-task-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 10px 18px;
        background-color: var(--primary-color);
        border: none;
        border-radius: 8px;
        color: white;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 149, 255, 0.4);
        position: relative;
        overflow: hidden;
        z-index: 1;
        letter-spacing: 0.5px;
      }

      .new-task-button::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background-color: rgba(255, 255, 255, 0.1);
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 8px;
      }

      .new-task-button:hover {
        transform: translateY(-3px) scale(1.03);
        background-color: #0086e5;
        box-shadow: 0 8px 25px rgba(0, 149, 255, 0.5);
      }

      .new-task-button:hover::before {
        opacity: 0.6;
      }

      .new-task-button i {
        font-size: 16px;
        animation: pulse-icon 1.5s infinite;
      }

      /* 关联线效果 */
      .assessment-flow-container {
        position: relative;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      /* 流程标题 */
      .process-section-title {
        font-size: 18px;
        font-weight: 500;
        color: var(--text-color);
        text-align: center;
        margin-bottom: 10px;
        position: relative;
        padding-bottom: 10px;
      }

      .process-section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      }

      /* 科技感对话框样式 */
      .process-section {
        position: relative;
        background: linear-gradient(135deg, rgba(26, 32, 46, 0.9), rgba(15, 21, 32, 0.95));
        border-radius: 16px;
        border: 1px solid rgba(62, 155, 255, 0.3);
        padding: 20px;
        margin-top: 40px;
        box-shadow:
          0 0 30px rgba(0, 149, 255, 0.15),
          inset 0 0 15px rgba(0, 224, 255, 0.05);
        overflow: visible;
      }

      /* 发光边框 */
      .process-section::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        border-radius: 16px;
        background: linear-gradient(
          45deg,
          var(--primary-color),
          transparent,
          var(--secondary-color)
        );
        opacity: 0.3;
        z-index: -1;
        pointer-events: none;
      }

      /* 三角形箭头 */
      .process-section::after {
        content: '';
        position: absolute;
        top: -20px;
        left: var(--arrow-position, 50%);
        width: 0;
        height: 0;
        border-left: 20px solid transparent;
        border-right: 20px solid transparent;
        border-bottom: 20px solid rgba(62, 155, 255, 0.3);
        transform: translateX(-50%);
        z-index: 1;
        transition: left 0.3s ease;
      }

      /* 内部填充三角形 */
      .arrow-inner {
        content: '';
        position: absolute;
        top: -18px;
        left: var(--arrow-position, 50%);
        width: 0;
        height: 0;
        border-left: 18px solid transparent;
        border-right: 18px solid transparent;
        border-bottom: 18px solid rgba(62, 155, 255, 0.3);
        transform: translateX(-50%);
        z-index: 2;
        transition: left 0.3s ease;
        pointer-events: none;
      }

      .assessment-actions .action-button.delete:hover {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
      }

      /* 查看更多卡片样式 */
      .view-more-card {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: linear-gradient(135deg, rgba(26, 32, 46, 0.8), rgba(15, 21, 32, 0.9));
        border: 1px dashed rgba(0, 149, 255, 0.4);
        transition: all 0.3s ease;
        cursor: pointer;
        text-align: center;
      }

      .view-more-card:hover {
        border: 1px solid var(--primary-color);
        background: linear-gradient(135deg, rgba(0, 149, 255, 0.1), rgba(15, 21, 32, 0.9));
        transform: translateY(-3px);
      }

      .view-more-card .assessment-icon {
        background-color: rgba(0, 149, 255, 0.05);
        margin: 0 auto 10px;
        width: 40px;
        height: 40px;
      }

      .view-more-button {
        margin-top: 15px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(0, 149, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);
        font-size: 16px;
        transition: all 0.3s ease;
      }

      .view-more-card:hover .view-more-button {
        background-color: var(--primary-color);
        color: white;
        transform: scale(1.1);
      }

      /* 自定义滚动条 */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: var(--background-dark);
      }

      ::-webkit-scrollbar-thumb {
        background: var(--border-color);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-color);
      }

      /* 科技感菜单样式 - 修改为适应新的导航栏位置 */
      .menu-toggle {
        width: 36px;
        height: 36px;
        background: rgba(0, 149, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        box-shadow: 0 0 15px rgba(0, 149, 255, 0.3);
        border: 1px solid rgba(0, 224, 255, 0.3);
      }

      .menu-toggle:hover {
        background: rgba(0, 149, 255, 0.3);
        transform: scale(1.05);
        box-shadow: 0 0 20px rgba(0, 149, 255, 0.4);
      }

      .menu-toggle .menu-icon {
        position: relative;
        width: 18px;
        height: 2px;
        background: var(--primary-color);
        transition: all 0.3s ease;
      }

      .menu-toggle .menu-icon::before,
      .menu-toggle .menu-icon::after {
        content: '';
        position: absolute;
        width: 18px;
        height: 2px;
        background: var(--primary-color);
        transition: all 0.3s ease;
      }

      .menu-toggle .menu-icon::before {
        transform: translateY(-6px);
      }

      .menu-toggle .menu-icon::after {
        transform: translateY(6px);
      }

      .menu-toggle.active .menu-icon {
        background: transparent;
      }

      .menu-toggle.active .menu-icon::before {
        transform: rotate(45deg);
      }

      .menu-toggle.active .menu-icon::after {
        transform: rotate(-45deg);
      }

      .menu-toggle::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 2px solid transparent;
        animation: pulse 2s infinite;
        box-sizing: border-box;
        display: none;
      }

      .side-menu {
        position: fixed;
        top: 0;
        right: -300px;
        width: 300px;
        height: 100%;
        background: rgba(15, 21, 32, 0.95);
        backdrop-filter: blur(10px);
        z-index: 999;
        transition: left 0.4s cubic-bezier(0.77, 0, 0.175, 1);
        border-right: 1px solid rgba(62, 155, 255, 0.2);
        box-shadow: 5px 0 25px rgba(0, 0, 0, 0.3);
        overflow-y: auto;
        padding: 80px 0 30px;
      }

      .side-menu.active {
        right: 0;
      }

      .side-menu-overlay {
        position: fixed;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(3px);
        z-index: 998;
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .side-menu-overlay.active {
        display: block;
        opacity: 1;
      }

      .side-menu-header {
        padding: 0 20px 20px;
        margin-bottom: 20px;
        border-bottom: 1px solid rgba(62, 155, 255, 0.1);
        text-align: center;
      }

      .side-menu-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-color);
        margin-bottom: 5px;
      }

      .side-menu-subtitle {
        font-size: 12px;
        color: var(--text-secondary);
      }

      .side-menu-nav {
        padding: 0 15px;
      }

      .side-menu-item {
        margin-bottom: 5px;
        list-style: none;
      }

      .side-menu-link {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        color: var(--text-color);
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.2s ease;
        position: relative;
      }

      .side-menu-link:hover {
        background: rgba(62, 155, 255, 0.1);
        color: var(--primary-color);
      }

      .side-menu-link.active {
        background: rgba(62, 155, 255, 0.15);
        color: var(--primary-color);
      }

      .side-menu-link i {
        font-size: 16px;
        margin-right: 10px;
        width: 20px;
        text-align: center;
      }

      .side-menu-link .arrow {
        margin-left: auto;
        transition: transform 0.2s ease;
      }

      .side-menu-link.expanded .arrow {
        transform: rotate(180deg);
      }

      .side-submenu {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        margin-left: 20px;
        list-style: none;
        padding-left: 15px;
      }

      .side-menu-item.active .side-submenu {
        max-height: 500px; /* 足够大的值以确保所有子菜单都能显示 */
      }

      .side-submenu-item {
        margin: 5px 0;
      }

      .side-submenu-link {
        display: flex;
        align-items: center;
        padding: 8px 15px;
        color: var(--text-secondary);
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.2s ease;
        font-size: 13px;
      }

      .side-submenu-link:hover {
        color: var(--primary-color);
        background: rgba(62, 155, 255, 0.05);
      }

      .side-submenu-link.active {
        color: var(--primary-color);
        background: rgba(62, 155, 255, 0.1);
      }

      .side-submenu-link i {
        font-size: 14px;
        margin-right: 8px;
        width: 16px;
        text-align: center;
      }

      /* 菜单底部 */
      .side-menu-footer {
        padding: 20px 15px;
        margin-top: 30px;
        border-top: 1px solid rgba(62, 155, 255, 0.1);
      }

      .user-profile-mini {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background: rgba(62, 155, 255, 0.05);
        border-radius: 8px;
      }

      .user-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        margin-right: 10px;
        border: 2px solid rgba(62, 155, 255, 0.3);
      }

      .user-info {
        flex: 1;
      }

      .user-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);
      }

      .user-role {
        font-size: 12px;
        color: var(--text-secondary);
      }

      .side-menu-settings {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid rgba(62, 155, 255, 0.1);
      }

      .side-menu-setting-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 5px;
        color: var(--text-color);
        font-size: 14px;
      }

      .side-menu-setting-item span {
        font-weight: 500;
      }

      :root.light-theme .process-card {
        box-shadow:
          0 4px 10px rgba(0, 0, 0, 0.05),
          0 0 0 1px rgba(62, 155, 255, 0.05);
      }

      :root.light-theme .process-card:hover {
        box-shadow:
          0 12px 28px rgba(0, 0, 0, 0.1),
          0 0 20px rgba(0, 149, 255, 0.1),
          0 0 0 1px rgba(62, 155, 255, 0.2);
      }

      /* 亮色主题的模态弹窗调整 */
      :root.light-theme .modal-content {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
      }

      /* 亮色主题的侧边菜单调整 */
      :root.light-theme .side-menu {
        background: rgba(245, 247, 250, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 2px 0 15px rgba(0, 0, 0, 0.1);
      }

      :root.light-theme .side-menu-link:hover {
        background: rgba(62, 155, 255, 0.05);
      }

      :root.light-theme .side-menu-link.active {
        background: rgba(62, 155, 255, 0.1);
      }

      /* 亮色主题的进度条调整 */
      :root.light-theme .progress-bar {
        background-color: rgba(42, 49, 66, 0.1);
      }

      /* 亮色主题的卡片和评估任务卡片调整 */
      :root.light-theme .assessment-card {
        background: linear-gradient(135deg, #ffffff, #f8fafd);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05);
      }

      :root.light-theme .assessment-card:hover {
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      }

      :root.light-theme .assessment-card.active {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      /* 亮色主题的处理流程区域调整 */
      :root.light-theme .process-section {
        background: #f8fafd;
        border: 1px solid rgba(62, 155, 255, 0.15);
        box-shadow: 0 0 30px rgba(0, 149, 255, 0.05);
      }

      :root.light-theme .process-section::before {
        background: linear-gradient(
          45deg,
          var(--primary-color),
          transparent,
          var(--secondary-color)
        );
        opacity: 0.1;
      }

      /* 亮色主题的主要内容区域调整 */
      :root.light-theme .main-content {
        background: #ffffff;
      }

      /* 亮色主题查看更多卡片 */
      :root.light-theme .view-more-card {
        background: #ffffff;
        border: 1px dashed rgba(0, 149, 255, 0.4);
      }

      :root.light-theme .view-more-card:hover {
        background: rgba(0, 149, 255, 0.02);
      }

      /* 亮色主题下的按钮 */
      :root.light-theme .btn-secondary {
        background-color: #eaeef2;
      }

      :root.light-theme .btn-secondary:hover {
        background-color: #e0e6ed;
      }

      :root.light-theme .new-task-button {
        box-shadow: 0 4px 15px rgba(0, 149, 255, 0.2);
      }

      :root.light-theme .new-task-button:hover {
        box-shadow: 0 8px 25px rgba(0, 149, 255, 0.3);
      }

      /* 亮色主题下的菜单和图标 */
      :root.light-theme .menu-toggle {
        background: rgba(0, 149, 255, 0.1);
        box-shadow: 0 0 15px rgba(0, 149, 255, 0.15);
      }

      :root.light-theme .menu-toggle:hover {
        background: rgba(0, 149, 255, 0.15);
        box-shadow: 0 0 20px rgba(0, 149, 255, 0.2);
      }

      /* 亮色主题下的状态标签 */
      :root.light-theme .status-badge.in-progress {
        background-color: rgba(0, 149, 255, 0.08);
        box-shadow: 0 0 10px rgba(0, 149, 255, 0.1);
      }

      :root.light-theme .status-badge.completed {
        background-color: rgba(0, 196, 140, 0.08);
        box-shadow: 0 0 10px rgba(0, 196, 140, 0.1);
      }

      :root.light-theme .status-badge.not-started {
        background-color: rgba(160, 168, 184, 0.08);
      }

      /* 亮色主题的表单元素 */
      :root.light-theme .form-select {
        background-color: #ffffff;
        border: 1px solid #e0e6ed;
      }

      :root.light-theme .form-select:focus {
        box-shadow: 0 0 0 2px rgba(0, 149, 255, 0.1);
      }

      /* 亮色主题下的边框和分隔线 */
      :root.light-theme .process-details {
        border-top: 1px solid rgba(42, 49, 66, 0.1);
      }

      :root.light-theme .assessment-actions {
        border-top: 1px solid rgba(42, 49, 66, 0.1);
      }

      :root.light-theme .action-button {
        background-color: rgba(42, 49, 66, 0.05);
      }

      :root.light-theme .action-button:hover {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }

      :root.light-theme .action-button.delete:hover {
        background-color: var(--danger-color);
        color: white;
        border-color: var(--danger-color);
      }

      /* 亮色主题下的箭头 */
      :root.light-theme .process-section::after,
      :root.light-theme .arrow-inner {
        border-bottom-color: rgba(62, 155, 255, 0.15);
      }

      /* 亮色主题下任务进度卡片的进一步优化 */
      :root.light-theme .process-card {
        background: #ffffff;
        border: 1px solid rgba(62, 155, 255, 0.15);
        box-shadow:
          0 4px 10px rgba(0, 0, 0, 0.03),
          0 0 0 1px rgba(62, 155, 255, 0.03);
      }

      :root.light-theme .process-card::before {
        background: linear-gradient(
          45deg,
          var(--primary-color),
          transparent,
          var(--secondary-color)
        );
        opacity: 0.03;
      }

      :root.light-theme .process-card:hover::before {
        opacity: 0.1;
      }

      :root.light-theme .process-card::after {
        background: linear-gradient(90deg, var(--primary-color), rgba(225, 230, 240, 0.5));
      }

      :root.light-theme .process-number {
        border: 3px solid var(--background-card);
        box-shadow: 0 4px 10px rgba(0, 149, 255, 0.2);
      }

      :root.light-theme .process-icon {
        background: linear-gradient(135deg, rgba(0, 149, 255, 0.05), rgba(0, 224, 255, 0.02));
        box-shadow: 0 0 15px rgba(0, 149, 255, 0.07);
      }

      :root.light-theme .process-details {
        color: #666666;
      }

      :root.light-theme .process-details li i {
        color: var(--primary-color);
      }

      /* 亮色主题下不同状态的流程卡片 */
      :root.light-theme .process-card.completed {
        background: #ffffff;
        border-color: rgba(0, 196, 140, 0.3);
      }

      :root.light-theme .process-card.completed .process-icon {
        background: linear-gradient(135deg, rgba(0, 196, 140, 0.05), rgba(0, 224, 160, 0.02));
      }

      :root.light-theme .process-card.completed::before {
        background: linear-gradient(45deg, var(--success-color), transparent, #00e0a0);
        opacity: 0.05;
      }

      :root.light-theme .process-card.completed:hover::before {
        opacity: 0.1;
      }

      :root.light-theme .process-card.in-progress {
        background: #ffffff;
        border-color: rgba(0, 149, 255, 0.3);
        animation: glowing-light 2s infinite;
      }

      @keyframes glowing-light {
        0% {
          box-shadow: 0 0 10px rgba(0, 149, 255, 0.15);
        }
        50% {
          box-shadow: 0 0 20px rgba(0, 149, 255, 0.25);
        }
        100% {
          box-shadow: 0 0 10px rgba(0, 149, 255, 0.15);
        }
      }

      :root.light-theme .process-card.not-started {
        opacity: 0.75;
        background: #ffffff;
        border-color: rgba(62, 155, 255, 0.1);
      }

      /* 亮色主题下的待完成项样式 */
      :root.light-theme .process-details li.not-started {
        color: #666666;
        background-color: rgba(160, 168, 184, 0.05);
        border: 1px solid rgba(160, 168, 184, 0.15);
      }

      :root.light-theme .process-details li.not-started i {
        color: #666666;
      }

      /* 亮色主题下的用户信息面板 */
      :root.light-theme .user-profile-mini {
        background: rgba(62, 155, 255, 0.03);
      }

      /* 亮色主题下的设置面板 */
      :root.light-theme .side-menu-settings {
        border-top: 1px solid rgba(62, 155, 255, 0.1);
      }

      :root.light-theme .side-menu-setting-item {
        color: #505a6e;
      }

      /* 亮色主题下管理员信息区域 */
      :root.light-theme .admin-info {
        background-color: rgba(0, 149, 255, 0.05);
      }

      :root.light-theme .admin-info:hover {
        background-color: rgba(0, 149, 255, 0.1);
      }

      /* 顶部导航栏样式 */
      .header {
        background-color: rgba(16, 22, 36, 0.95);
        border-bottom: 1px solid var(--border-color);
        position: sticky;
        top: 0;
        z-index: 1000;
        backdrop-filter: blur(10px);
      }

      .navbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 60px;
      }

      .nav-menu {
        display: none;
        list-style: none;
        margin: 0;
        padding: 0;
        height: 100%;
      }

      .nav-item {
        position: relative;
        height: 100%;
        display: flex;
        align-items: center;
      }

      .nav-link {
        color: var(--text-color);
        text-decoration: none;
        padding: 0 15px;
        height: 100%;
        display: flex;
        align-items: center;
        position: relative;
        transition: all 0.3s ease;
      }

      .nav-link:hover {
        color: var(--primary-color);
      }

      .nav-link.active {
        color: var(--primary-color);
      }

      .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background-color: var(--primary-color);
        border-radius: 3px 3px 0 0;
      }

      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background-color: var(--background-card);
        border: 1px solid var(--border-color);
        border-radius: 0 0 6px 6px;
        min-width: 180px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: all 0.3s ease;
        z-index: 100;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .nav-item:hover .dropdown-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .dropdown-item {
        display: block;
        padding: 10px 15px;
        color: var(--text-color);
        text-decoration: none;
        transition: all 0.2s ease;
        font-size: 14px;
        border-bottom: 1px solid var(--border-color);
      }

      .dropdown-menu {
        list-style-type: none; /* 去掉前面的点 */
      }

      .dropdown-item:last-child {
        border-bottom: none;
        border-radius: 0 0 6px 6px;
      }

      .dropdown-item:hover {
        background-color: rgba(62, 155, 255, 0.1);
        color: var(--primary-color);
        padding-left: 20px;
      }

      .dropdown-icon {
        margin-right: 8px;
      }

      .nav-tools {
        display: flex;
        align-items: center;
        gap: 20px;
      }

      .notification-icon,
      .user-profile {
        position: relative;
        cursor: pointer;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
        display: none;
      }

      .notification-icon:hover,
      .user-profile:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      .notification-badge {
        position: absolute;
        top: -2px;
        right: -2px;
        background-color: var(--danger-color);
        color: white;
        font-size: 10px;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .user-profile img {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        object-fit: cover;
      }

      /* 亮色主题的导航栏调整 */
      :root.light-theme .header {
        background-color: rgba(245, 247, 250, 0.95);
        border-bottom: 1px solid var(--border-color);
      }

      :root.light-theme .nav-link:hover {
        color: var(--primary-color);
      }

      :root.light-theme .dropdown-menu {
        background-color: var(--background-card);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      :root.light-theme .dropdown-item:hover {
        background-color: rgba(62, 155, 255, 0.05);
      }

      /* 主题切换开关样式替换成新版本 */

      /* 流程项状态样式 */
      .process-details li.completed {
        color: var(--text-color);
        background-color: rgba(0, 196, 140, 0.08);
        border: 1px solid rgba(0, 196, 140, 0.2);
        text-align: center;
        padding: 4px 8px;
        border-radius: 4px;
      }

      .process-details li.completed i {
        color: var(--success-color);
      }

      .process-details li.in-progress {
        color: var(--primary-color);
        background-color: rgba(0, 149, 255, 0.08);
        border: 1px solid rgba(0, 149, 255, 0.2);
        animation: pulse 2s infinite;
        text-align: center;
        padding: 4px 8px;
        border-radius: 4px;
      }

      .process-details li.in-progress i {
        color: var(--primary-color);
      }

      .process-details li.not-started {
        color: var(--text-secondary);
        background-color: rgba(160, 168, 184, 0.05);
        border: 1px solid rgba(160, 168, 184, 0.15);
        text-align: center;
        padding: 4px 8px;
        border-radius: 4px;
      }

      .process-details li.not-started i {
        color: var(--text-secondary);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 保留原始侧边菜单 -->
      <div class="side-menu">
        <ul class="side-menu-nav">
          <li class="side-menu-item">
            <a href="1.Dashboard.html" class="side-menu-link active">
              <i class="fas fa-home"></i>
              <span>首页</span>
            </a>
          </li>

          <li class="side-menu-item">
            <a href="#" class="side-menu-link">
              <i class="fas fa-tasks"></i>
              <span>评估任务</span>
              <i class="fas fa-chevron-down arrow"></i>
            </a>
            <ul class="side-submenu">
              <li class="side-submenu-item">
                <a href="6.EvaluationTaskList.html" class="side-submenu-link">
                  <i class="fas fa-list"></i>
                  <span>评估任务列表</span>
                </a>
              </li>
              <li class="side-submenu-item">
                <a href="7.IndustryEvaluation.html" class="side-submenu-link">
                  <i class="fas fa-plus"></i>
                  <span>新建行业仿真度评估任务</span>
                </a>
              </li>
              <li class="side-submenu-item">
                <a href="8.InterIndustryRiskEvaluation.html" class="side-submenu-link">
                  <i class="fas fa-project-diagram"></i>
                  <span>新建跨行业仿真度评估任务</span>
                </a>
              </li>
            </ul>
          </li>

          <li class="side-menu-item">
            <a href="#" class="side-menu-link">
              <i class="fas fa-cube"></i>
              <span>模版管理</span>
              <i class="fas fa-chevron-down arrow"></i>
            </a>
            <ul class="side-submenu">
              <li class="side-submenu-item">
                <a href="2.TemplateLibList.html" class="side-submenu-link">
                  <i class="fas fa-list"></i>
                  <span>测评模版库</span>
                </a>
              </li>
              <li class="side-submenu-item">
                <a href="3.TemplateDetails.html" class="side-submenu-link">
                  <i class="fas fa-file-alt"></i>
                  <span>新建模版</span>
                </a>
              </li>
            </ul>
          </li>

          <li class="side-menu-item">
            <a href="#" class="side-menu-link">
              <i class="fas fa-database"></i>
              <span>数据采集</span>
              <i class="fas fa-chevron-down arrow"></i>
            </a>
            <ul class="side-submenu">
              <li class="side-submenu-item">
                <a href="4.DataCollectTaskList.html" class="side-submenu-link">
                  <i class="fas fa-tasks"></i>
                  <span>采集任务列表</span>
                </a>
              </li>
              <li class="side-submenu-item">
                <a href="4.DataCollectTaskList.html" class="side-submenu-link">
                  <i class="fas fa-file-alt"></i>
                  <span>新建采集任务</span>
                </a>
              </li>
            </ul>
          </li>

          <li class="side-menu-item">
            <a href="#" class="side-menu-link">
              <i class="fas fa-microchip"></i>
              <span>算法库</span>
              <i class="fas fa-chevron-down arrow"></i>
            </a>
            <ul class="side-submenu">
              <li class="side-submenu-item">
                <a href="5.AlgorithmLibList.html" class="side-submenu-link">
                  <i class="fas fa-list"></i>
                  <span>算法列表</span>
                </a>
              </li>
              <li class="side-submenu-item">
                <a href="5.AlgorithmLibList.html" class="side-submenu-link">
                  <i class="fas fa-file-code"></i>
                  <span>新建算法</span>
                </a>
              </li>
            </ul>
          </li>

          <!-- <li class="side-menu-item">
                    <a href="#" class="side-menu-link">
                        <i class="fas fa-cog"></i>
                        <span>系统管理</span>
                        <i class="fas fa-chevron-down arrow"></i>
                    </a>
                    <ul class="side-submenu">
                        <li class="side-submenu-item">
                            <a href="9.UserManagement.html" class="side-submenu-link">
                                <i class="fas fa-users"></i>
                                <span>用户管理</span>
                            </a>
                        </li>
                        <li class="side-submenu-item">
                            <a href="10.RolePermissionManagement.html" class="side-submenu-link">
                                <i class="fas fa-user-tag"></i>
                                <span>角色/权限管理</span>
                            </a>
                        </li>
                        <li class="side-submenu-item">
                            <a href="11.OperationLogs.html" class="side-submenu-link">
                                <i class="fas fa-history"></i>
                                <span>操作日志</span>
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="side-menu-item">
                    <a href="#" class="side-menu-link">
                        <i class="fas fa-book"></i>
                        <span>资源中心</span>
                        <i class="fas fa-chevron-down arrow"></i>
                    </a>
                    <ul class="side-submenu">
                        <li class="side-submenu-item">
                            <a href="12.DocumentsCenter.html" class="side-submenu-link">
                                <i class="fas fa-graduation-cap"></i>
                                <span>文档中心</span>
                            </a>
                        </li>
                    </ul>
                </li> -->
        </ul>

        <div class="side-menu-footer">
          <div class="user-profile-mini">
            <img
              src="https://randomuser.me/api/portraits/men/4.jpg"
              alt="用户头像"
              class="user-avatar"
            />
            <div class="user-info">
              <div class="user-name">管理员</div>
              <div class="user-role">系统管理员</div>
            </div>
          </div>
          <div class="side-menu-settings">
            <div class="side-menu-setting-item">
              <span>切换主题</span>
              <div class="theme-switch">
                <div class="theme-icon">
                  <i class="fas fa-sun"></i>
                </div>
                <div class="theme-icon">
                  <i class="fas fa-moon"></i>
                </div>
                <div class="theme-switch-slider"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="side-menu-overlay"></div>

      <main class="main-content">
        <div class="assessment-flow-container">
          <div class="assessment-section">
            <div class="section-header">
              <div class="section-title-container">
                <div class="section-title-group">
                  <div class="section-title-text">
                    <h2 class="section-title"><i class="fas fa-clock"></i>仿真度评估任务</h2>
                    <div class="section-title-description">
                      显示最近创建或操作的仿真度评估任务，点击卡片可查看任务进度详情
                    </div>
                  </div>
                </div>
              </div>
              <button class="new-task-button" style="display: none">
                <i class="fas fa-plus"></i>
                新建行业仿真度评估任务
              </button>
            </div>

            <div class="assessment-container">
              <div class="assessment-card active" data-task-id="communication-transport">
                <div class="status-badge in-progress">
                  <i class="fas fa-spinner fa-spin"></i>
                  进行中
                </div>
                <div class="assessment-icon">
                  <i class="fas fa-tower-broadcast"></i>
                </div>
                <h3 class="assessment-title">通信-交通行业级联风险仿真度评估</h3>
                <p class="assessment-description">
                  <span
                    style="
                      background-color: #ff7700;
                      color: white;
                      padding: 4px 8px;
                      border-radius: 4px;
                      font-size: 13px;
                      font-weight: 500;
                      display: inline-block;
                    "
                    >仿真场景：通信-交通行业级联风险场景</span
                  >
                </p>
                <div class="assessment-info">
                  <div class="assessment-info-item">
                    <i class="fas fa-calendar"></i>
                    <span>创建时间：2024-03-15</span>
                  </div>
                  <div class="assessment-info-item">
                    <i class="fas fa-clock"></i>
                    <span>运行时间：2小时30分钟</span>
                  </div>
                </div>
                <div class="progress-container">
                  <div class="progress-label">
                    <span>任务进度</span>
                    <span>75%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 75%"></div>
                  </div>
                </div>
                <div class="assessment-actions">
                  <button class="action-button">
                    <i class="fas fa-eye"></i>
                    查看
                  </button>
                  <button class="action-button">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="action-button pause-resume">
                    <i class="fas fa-pause"></i>
                    暂停
                  </button>
                </div>
              </div>

              <div class="assessment-card" data-task-id="government">
                <div class="status-badge completed">
                  <i class="fas fa-check"></i>
                  已完成
                </div>
                <div class="assessment-icon">
                  <i class="fas fa-building-user"></i>
                </div>
                <h3 class="assessment-title">政务行业企业网场景仿真度评估</h3>
                <p class="assessment-description">
                  <span
                    style="
                      background-color: #ff7700;
                      color: white;
                      padding: 4px 8px;
                      border-radius: 4px;
                      font-size: 13px;
                      font-weight: 500;
                      display: inline-block;
                    "
                    >仿真场景：政务行业企业网场景</span
                  >
                </p>
                <div class="assessment-info">
                  <div class="assessment-info-item">
                    <i class="fas fa-calendar"></i>
                    <span>创建时间：2024-03-10</span>
                  </div>
                  <div class="assessment-info-item">
                    <i class="fas fa-clock"></i>
                    <span>运行时间：4小时15分钟</span>
                  </div>
                </div>
                <div class="progress-container">
                  <div class="progress-label">
                    <span>任务进度</span>
                    <span>100%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%"></div>
                  </div>
                </div>
                <div class="assessment-actions">
                  <button class="action-button">
                    <i class="fas fa-eye"></i>
                    查看
                  </button>
                  <button class="action-button">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="action-button pause-resume">
                    <i class="fas fa-sync-alt"></i>
                    重启
                  </button>
                </div>
              </div>

              <div class="assessment-card" data-task-id="transport-scenario">
                <div class="status-badge not-started">
                  <i class="fas fa-clock"></i>
                  未开始
                </div>
                <div class="assessment-icon">
                  <i class="fas fa-traffic-light"></i>
                </div>
                <h3 class="assessment-title">交通行业典型场景仿真度评估</h3>
                <p class="assessment-description">
                  <span
                    style="
                      background-color: #ff7700;
                      color: white;
                      padding: 4px 8px;
                      border-radius: 4px;
                      font-size: 13px;
                      font-weight: 500;
                      display: inline-block;
                    "
                    >仿真场景：交通行业典型场景</span
                  >
                </p>
                <div class="assessment-info">
                  <div class="assessment-info-item">
                    <i class="fas fa-calendar"></i>
                    <span>创建时间：2024-03-20</span>
                  </div>
                  <div class="assessment-info-item">
                    <i class="fas fa-clock"></i>
                    <span>运行时间：未开始</span>
                  </div>
                </div>
                <div class="progress-container">
                  <div class="progress-label">
                    <span>任务进度</span>
                    <span>0%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                  </div>
                </div>
                <div class="assessment-actions">
                  <button class="action-button">
                    <i class="fas fa-eye"></i>
                    查看
                  </button>
                  <button class="action-button">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="action-button pause-resume">
                    <i class="fas fa-play"></i>
                    开始
                  </button>
                </div>
              </div>

              <div class="assessment-card view-more-card">
                <div class="assessment-icon">
                  <i class="fas fa-ellipsis-h"></i>
                </div>
                <h3 class="assessment-title">查看更多任务</h3>
                <p class="assessment-description">浏览所有评估任务</p>
                <div class="view-more-button">
                  <i class="fas fa-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>

          <div class="process-section">
            <h3 class="process-section-title">任务进度</h3>
            <div class="arrow-inner"></div>
            <div class="process-container">
              <div class="process-card">
                <div class="process-number">1</div>
                <div class="process-icon">
                  <i class="fas fa-play-circle"></i>
                </div>
                <h3 class="process-title">创建评估任务</h3>
                <div class="process-details">
                  <ul>
                    <li class="in-progress"><i class="fas fa-spinner fa-spin"></i>关联目标场景</li>
                    <li class="not-started"><i class="fas fa-clock"></i>选择任务类型</li>
                    <li class="not-started"><i class="fas fa-clock"></i>填写任务信息</li>
                  </ul>
                </div>
              </div>

              <div class="process-card">
                <div class="process-number">2</div>
                <div class="process-icon">
                  <i class="fas fa-list-check"></i>
                </div>
                <h3 class="process-title">构建指标体系</h3>
                <div class="process-details">
                  <ul>
                    <li class="completed"><i class="fas fa-check"></i>建立评估指标</li>
                    <li class="completed"><i class="fas fa-check"></i>关联指标算法</li>
                    <li class="completed"><i class="fas fa-check"></i>关联数据采集</li>
                  </ul>
                </div>
              </div>

              <div class="process-card">
                <div class="process-number">3</div>
                <div class="process-icon">
                  <i class="fas fa-code"></i>
                </div>
                <h3 class="process-title">采集指标数据</h3>
                <div class="process-details">
                  <ul
                    style="height: 120px; overflow-x: hidden; overflow-y: auto; margin-right: -10px"
                  >
                    <li class="completed"><i class="fas fa-check"></i>采集通信网络覆盖数据</li>
                    <li class="completed"><i class="fas fa-check"></i>采集电路负荷数据</li>
                    <li class="completed"><i class="fas fa-check"></i>采集电网故障响应数据</li>
                    <li class="completed"><i class="fas fa-check"></i>采集通信容量数据</li>
                  </ul>
                </div>
              </div>

              <div class="process-card">
                <div class="process-number">4</div>
                <div class="process-icon">
                  <i class="fas fa-database"></i>
                </div>
                <h3 class="process-title">计算指标结果</h3>
                <div class="process-details">
                  <ul
                    style="height: 120px; overflow-x: hidden; overflow-y: auto; margin-right: -10px"
                  >
                    <li class="completed"><i class="fas fa-check"></i>计算配电网拓扑结构精度</li>
                    <li class="completed"><i class="fas fa-check"></i>计算电网故障响应特性</li>
                    <li class="completed"><i class="fas fa-check"></i>计算通信网络容量精度</li>
                    <li class="completed"><i class="fas fa-check"></i>计算通信时延精度</li>
                    <li class="completed"><i class="fas fa-check"></i>计算通信网络覆盖精度</li>
                    <li class="completed"><i class="fas fa-check"></i>计算交通流量精度</li>
                    <li class="in-progress">
                      <i class="fas fa-spinner fa-spin"></i>计算跨行业交互影响
                    </li>
                  </ul>
                </div>
              </div>

              <div class="process-card">
                <div class="process-number">5</div>
                <div class="process-icon">
                  <i class="fas fa-file-lines"></i>
                </div>
                <h3 class="process-title">生成评估结果</h3>
                <div class="process-details">
                  <ul>
                    <li class="not-started"><i class="fas fa-clock"></i>汇总评估结果</li>
                    <li class="not-started"><i class="fas fa-clock"></i>生成评估总览</li>
                    <li class="not-started"><i class="fas fa-clock"></i>生成评估报告</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 新建评估任务弹窗 -->
    <div class="modal" id="newEvalTaskModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>新建评估任务</h3>
          <button class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
          <!-- 添加任务名称输入框 -->
          <div class="form-group">
            <label for="taskName">任务名称</label>
            <input type="text" id="taskName" class="form-select" placeholder="请输入任务名称" />
          </div>

          <!-- 评估对象选择 -->
          <div class="form-group">
            <label>选择评估场景</label>
            <select class="form-select" id="assessmentSceneSelect">
              <option value="">请选评估场景</option>
              <option value="power">中小企业网场景</option>
              <option value="transport">轨道交通场景</option>
              <option value="communication">危险气体缓冲工艺场景</option>
              <option value="water">通信-电力级联风险场景</option>
              <option value="gas">通信-交通级联风险场景</option>
              <option value="power-transport">电力-交通级联风险场景</option>
            </select>
          </div>

          <div class="form-group">
            <label>选择创建方式</label>
            <div class="radio-group">
              <label class="radio-label">
                <input type="radio" name="createType" value="template" checked />
                <span>从模版创建</span>
              </label>
              <label class="radio-label">
                <input type="radio" name="createType" value="empty" />
                <span>创建空的评估任务</span>
              </label>
            </div>
          </div>
          <div class="form-group template-select" id="templateSelectGroup">
            <label for="templateType">选择模版</label>
            <select class="form-select" id="templateType">
              <option value="">请选择模版</option>
              <option value="template1">通信-交通级联风险仿真评估模版</option>
              <option value="template1">电力系统稳态仿真评估模版</option>
              <option value="template2">交通流量模拟评估模版</option>
              <option value="template3">供水系统压力仿真评估模版</option>
              <option value="template4">燃气管网压力监测模版</option>
            </select>
          </div>

          <div class="form-group simulation-info" id="simulationTypeGroup" style="display: none">
            <label for="simType">评估任务类型</label>
            <select id="simType" class="form-select">
              <option value="">请选择评估任务类型</option>
              <option value="steady">跨行业级联风险仿真度评估</option>
              <option value="dynamic">电力行业仿真度评估</option>
              <option value="monte-carlo">通信行业仿真度评估</option>
              <option value="scenario">交通行业仿真度评估</option>
              <option value="cascade">燃气行业仿真度评估</option>
            </select>
          </div>

          <div class="form-group simulation-info" id="simulationDescGroup" style="display: none">
            <label for="simDescription">描述信息</label>
            <textarea
              id="simDescription"
              class="form-select"
              rows="3"
              placeholder="请输入描述信息"
            ></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="cancelBtn">取消</button>
          <button class="btn btn-primary" id="confirmBtn">确定</button>
        </div>
      </div>
    </div>

    <script>
      // 定义任务进度数据
      const taskProgressData = {
        'communication-transport': {
          status: 'in-progress',
          progress: 75,
          processSteps: [
            { step: 0, status: 'completed' }, // 指标体系
            { step: 1, status: 'completed' }, // 算法模版
            { step: 2, status: 'completed' }, // 数据采集
            { step: 3, status: 'in-progress' }, // 执行评估
            { step: 4, status: 'not-started' }, // 评估报告
          ],
        },
        government: {
          status: 'completed',
          progress: 100,
          processSteps: [
            { step: 0, status: 'completed' },
            { step: 1, status: 'completed' },
            { step: 2, status: 'completed' },
            { step: 3, status: 'completed' },
            { step: 4, status: 'completed' },
          ],
        },
        'transport-scenario': {
          status: 'not-started',
          progress: 0,
          processSteps: [
            { step: 0, status: 'not-started' },
            { step: 1, status: 'not-started' },
            { step: 2, status: 'not-started' },
            { step: 3, status: 'not-started' },
            { step: 4, status: 'not-started' },
          ],
        },
      }

      // 获取所有流程卡片
      const processCards = document.querySelectorAll('.process-container .process-card')

      // 清除所有高亮效果
      function clearHighlights() {
        processCards.forEach((card) => {
          card.classList.remove('completed', 'in-progress', 'not-started')
        })
      }

      // 根据任务ID更新流程卡片状态
      function updateProcessCards(taskId) {
        clearHighlights()
        const taskData = taskProgressData[taskId]
        if (taskData) {
          taskData.processSteps.forEach((step, index) => {
            // 更新流程卡片的状态
            // 强制设置步骤1和2为已完成状态
            if (index === 0 || index === 1) {
              processCards[index].classList.add('completed')
            } else {
              processCards[index].classList.add(step.status)
            }

            // 更新流程项的状态
            const processList = processCards[index].querySelectorAll('.process-details li')
            processList.forEach((item, itemIndex) => {
              // 移除所有可能的状态类
              item.classList.remove('completed', 'in-progress', 'not-started')

              // 根据流程卡片状态设置流程项状态
              if (index === 0 || index === 1) {
                // 强制设置步骤1和2的所有子项为已完成
                item.classList.add('completed')
              } else if (step.status === 'completed') {
                item.classList.add('completed')
              } else if (step.status === 'in-progress') {
                // 当状态为in-progress时，只有第一项是进行中的，其余是未开始的
                if (itemIndex === 0) {
                  item.classList.add('in-progress')
                } else {
                  item.classList.add('not-started')
                }
              } else {
                // not-started
                item.classList.add('not-started')
              }

              // 更新图标
              const icon = item.querySelector('i')
              icon.className = '' // 清除所有类

              if (item.classList.contains('completed')) {
                icon.classList.add('fas', 'fa-check')
              } else if (item.classList.contains('in-progress')) {
                icon.classList.add('fas', 'fa-spinner', 'fa-spin')
              } else {
                // not-started
                icon.classList.add('fas', 'fa-clock')
              }
            })
          })
        }
      }

      // 为评估任务卡片添加点击事件
      document.querySelectorAll('.assessment-card').forEach((card) => {
        card.addEventListener('click', function (e) {
          // 如果点击的是操作按钮，不触发卡片的点击事件
          if (e.target.closest('.action-button')) {
            return
          }

          // 移除其他卡片的选中效果
          document.querySelectorAll('.assessment-card').forEach((c) => {
            c.classList.remove('active')
          })

          // 添加当前卡片的选中效果
          this.classList.add('active')

          // 获取任务ID并更新流程卡片状态
          const taskId = this.getAttribute('data-task-id')
          updateProcessCards(taskId)

          // 更新指向箭头位置
          updateArrowPosition(this)
        })
      })

      // 更新指向箭头位置
      function updateArrowPosition(card) {
        const arrow = document.querySelector('.process-section')
        const innerArrow = document.querySelector('.arrow-inner')
        const cardRect = card.getBoundingClientRect()
        const arrowContainer = document
          .querySelector('.assessment-flow-container')
          .getBoundingClientRect()

        // 计算卡片中心相对于容器的位置
        const cardCenterX = cardRect.left + cardRect.width / 2
        const relativePosPercent =
          ((cardCenterX - arrowContainer.left) / arrowContainer.width) * 100

        // 设置箭头位置
        arrow.style.setProperty('--arrow-position', `${relativePosPercent}%`)
        innerArrow.style.left = `${relativePosPercent}%`
      }

      // 操作按钮点击事件
      document.querySelectorAll('.action-button').forEach((button) => {
        button.addEventListener('click', function (e) {
          e.stopPropagation() // 阻止事件冒泡
          const action = this.textContent.trim()
          const card = this.closest('.assessment-card')
          const taskName = card.querySelector('.assessment-title').textContent

          switch (action) {
            case '查看':
            case '编辑':
              const taskId = card.getAttribute('data-task-id')
              // 根据任务ID判断跳转页面
              if (taskId === 'communication-transport') {
                window.location.href = '8.InterIndustryRiskEvaluation.html'
              } else {
                window.location.href = '7.IndustryEvaluation.html'
              }
              break
            case '暂停':
              const isPaused = this.querySelector('i').classList.contains('fa-pause')
              if (isPaused) {
                this.querySelector('i').classList.remove('fa-pause')
                this.querySelector('i').classList.add('fa-play')
                alert(`暂停任务：${taskName}`)
              } else {
                this.querySelector('i').classList.remove('fa-play')
                this.querySelector('i').classList.add('fa-pause')
                alert(`恢复任务：${taskName}`)
              }
              break
            case '重启':
              const isRestarted = this.querySelector('i').classList.contains('fa-sync-alt')
              if (isRestarted) {
                this.querySelector('i').classList.remove('fa-sync-alt')
                this.querySelector('i').classList.add('fa-play')
                alert(`重启任务：${taskName}`)
              } else {
                this.querySelector('i').classList.remove('fa-play')
                this.querySelector('i').classList.add('fa-sync-alt')
                alert(`重启任务：${taskName}`)
              }
              break
            case '开始':
              const isStarted = this.querySelector('i').classList.contains('fa-play')
              if (isStarted) {
                this.querySelector('i').classList.remove('fa-play')
                this.querySelector('i').classList.add('fa-pause')
                alert(`暂停任务：${taskName}`)
              } else {
                this.querySelector('i').classList.remove('fa-pause')
                this.querySelector('i').classList.add('fa-play')
                alert(`开始任务：${taskName}`)
              }
              break
          }
        })
      })

      // 新建任务按钮点击事件
      document.querySelector('.new-task-button').addEventListener('click', function () {
        newEvalTaskModal.style.display = 'flex'
      })

      // 查看更多按钮点击事件
      document.querySelector('.view-more-card').addEventListener('click', function () {
        window.location.href = '6.EvaluationTaskList.html'
      })

      // 初始化时默认选中第一个任务并更新箭头位置
      document.addEventListener('DOMContentLoaded', function () {
        const firstCard = document.querySelector('.assessment-card')
        firstCard.classList.add('active')
        updateProcessCards(firstCard.getAttribute('data-task-id'))
        updateArrowPosition(firstCard)

        // 初始化弹窗相关变量
        const newEvalTaskModal = document.getElementById('newEvalTaskModal')
        const createTypeRadios = document.querySelectorAll('input[name="createType"]')
        const templateSelectGroup = document.getElementById('templateSelectGroup')
        const confirmBtn = document.getElementById('confirmBtn')
        const cancelBtn = document.getElementById('cancelBtn')
        const closeBtn = document.querySelector('.close-btn')

        // 关闭弹窗
        function closeModal() {
          newEvalTaskModal.style.display = 'none'
        }

        // 关闭按钮事件
        closeBtn.addEventListener('click', closeModal)
        cancelBtn.addEventListener('click', closeModal)

        // 点击弹窗外部关闭
        newEvalTaskModal.addEventListener('click', function (e) {
          if (e.target === newEvalTaskModal) {
            closeModal()
          }
        })

        // 创建方式切换
        createTypeRadios.forEach((radio) => {
          radio.addEventListener('change', function () {
            if (this.value === 'empty') {
              templateSelectGroup.style.display = 'none'
              document.querySelectorAll('.simulation-info').forEach((el) => {
                el.style.display = 'block'
              })
            } else {
              templateSelectGroup.style.display = 'block'
              document.querySelectorAll('.simulation-info').forEach((el) => {
                el.style.display = 'none'
              })
            }
          })
        })

        // 确认按钮点击事件
        confirmBtn.addEventListener('click', function () {
          const selectedScene = document.getElementById('assessmentSceneSelect').value
          if (!selectedScene) {
            alert('请选择评估场景')
            return
          }

          const selectedType = document.querySelector('input[name="createType"]:checked').value

          if (selectedType === 'empty') {
            // 创建空的评估任务
            const selectedSimType = document.getElementById('simType').value
            if (!selectedSimType) {
              alert('请选择评估任务类型')
              return
            }
            // 根据模版选择跳转
            if (selectedSimType === 'steady') {
              window.location.href = '8.InterIndustryRiskEvaluation.html'
            } else {
              window.location.href = '7.IndustryEvaluation.html'
            }

            closeModal()
          } else {
            // 从模版创建
            const selectedTemplate = document.getElementById('templateType').value
            if (!selectedTemplate) {
              alert('请选择模版')
              return
            }

            // 根据模版选择跳转
            if (selectedTemplate === 'template1') {
              window.location.href = '8.InterIndustryRiskEvaluation.html'
            } else {
              window.location.href = '7.IndustryEvaluation.html'
            }

            closeModal()
          }
        })

        // 为流程卡片添加点击事件
        const processCards = document.querySelectorAll('.process-card')
        processCards.forEach((card, index) => {
          card.style.cursor = 'pointer' // 添加鼠标悬停样式
          card.addEventListener('click', function () {
            switch (index) {
              case 0: // 创建评估任务
                window.location.href = '8.InterIndustryRiskEvaluation.html?tab=0'
                break
              case 1: // 构建指标体系
                window.location.href = '8.InterIndustryRiskEvaluation.html?tab=0'
                break
              case 2: // 采集指标数据
                window.location.href = '8.InterIndustryRiskEvaluation.html?tab=1'
                break
              case 3: // 计算指标结果
                window.location.href = '8.InterIndustryRiskEvaluation.html?tab=2'
                break
              case 4: // 生成评估结果
                window.location.href = '8.InterIndustryRiskEvaluation.html?tab=3'
                break
            }
          })
        })
      })
    </script>

    <!-- 菜单交互脚本 -->
    <script>
      // 菜单切换功能
      const menuToggle = document.querySelector('.menu-toggle')
      const sideMenu = document.querySelector('.side-menu')
      const overlay = document.querySelector('.side-menu-overlay')

      // 展开/收起菜单
      menuToggle.addEventListener('click', () => {
        menuToggle.classList.toggle('active')
        sideMenu.classList.toggle('active')
        overlay.classList.toggle('active')
      })

      // 点击遮罩层关闭菜单
      overlay.addEventListener('click', () => {
        menuToggle.classList.remove('active')
        sideMenu.classList.remove('active')
        overlay.classList.remove('active')
      })

      // 子菜单展开/收起功能
      const menuItems = document.querySelectorAll('.side-menu-item')

      menuItems.forEach((item) => {
        const link = item.querySelector('.side-menu-link')
        const hasSubmenu = item.querySelector('.side-submenu')

        if (hasSubmenu) {
          link.addEventListener('click', (e) => {
            e.preventDefault()

            // 切换当前菜单的活动状态
            item.classList.toggle('active')
            link.classList.toggle('expanded')

            // 关闭其他展开的菜单
            menuItems.forEach((otherItem) => {
              if (otherItem !== item && otherItem.classList.contains('active')) {
                otherItem.classList.remove('active')
                otherItem.querySelector('.side-menu-link').classList.remove('expanded')
              }
            })
          })
        }
      })

      // 页面加载时自动展开当前活动菜单
      document.addEventListener('DOMContentLoaded', () => {
        const activeSubmenuLink = document.querySelector('.side-submenu-link.active')
        if (activeSubmenuLink) {
          const parentItem = activeSubmenuLink.closest('.side-menu-item')
          parentItem.classList.add('active')
          parentItem.querySelector('.side-menu-link').classList.add('expanded')
        }
      })
    </script>

    <!-- 主题切换脚本 -->
    <script>
      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        // 主题切换功能初始化
        const themeSwitch = document.querySelector('.theme-switch')
        const htmlRoot = document.documentElement

        // 检查本地存储中的主题设置，默认为暗色主题
        const savedTheme = localStorage.getItem('theme')
        // 只有当明确保存了"light"时才应用亮色主题
        if (savedTheme === 'light') {
          htmlRoot.classList.add('light-theme')
        } else {
          // 如果没有保存或者保存的不是"light"，则确保是暗色主题
          localStorage.setItem('theme', 'dark')
        }

        // 切换主题的函数
        function toggleTheme() {
          if (htmlRoot.classList.contains('light-theme')) {
            htmlRoot.classList.remove('light-theme')
            localStorage.setItem('theme', 'dark')
          } else {
            htmlRoot.classList.add('light-theme')
            localStorage.setItem('theme', 'light')
          }
        }

        // 为主题切换按钮添加点击事件
        themeSwitch.addEventListener('click', toggleTheme)
      })
    </script>
  </body>
</html>
