<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法库列表 - 城市级关基级联的网络仿真度评估系统</title>
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Echarts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面整体放大120% */
        html {
            zoom: 110%;
        }

        :root {
            --primary-color: #0096FF;
            --secondary-color: #00E0FF;
            --background-dark: #0F1520;
            --background-card: #1A202E;
            --text-color: #E0E6F0;
            --text-secondary: #A0A8B8;
            --border-color: #2A3142;
            --highlight-color: #3E9BFF;
            --success-color: #00C48C;
            --warning-color: #FFB946;
            --danger-color: #F25767;
            --info-color: #0095FF;
            --add-color: #27ae60;
            --remove-color: #e74c3c;
            --modify-color: #f39c12;
        }

        /* 亮色主题变量 */
        :root.light-theme {
            --primary-color: #0078D4;
            --secondary-color: #00A2E0;
            --background-dark: #FFFFFF;
            --background-card: #FFFFFF;
            --text-color: #333333;
            --text-secondary: #666666;
            --border-color: #E0E6ED;
            --highlight-color: #106EBE;
            --success-color: #0A9D5A;
            --warning-color: #F29D41;
            --danger-color: #E74C3C;
            --info-color: #0078D4;
            --add-color: #27ae60;
            --remove-color: #e74c3c;
            --modify-color: #f39c12;
        }

        /* Logo样式 */
        .logo {
	zoom: 110%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 10px;
        }

        .logo-icon {
            font-size: 24px;
            color: var(--primary-color);
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 2px rgba(0, 224, 255, 0.4));
        }

        .logo span {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 0.5px;
            background: linear-gradient(90deg, var(--text-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: var(--background-dark);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            min-height: 100vh;
        }

        /* 顶部导航栏样式 */
        .header {
            background-color: rgba(16, 22, 36, 0.95);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        
        /* 亮色主题的导航栏调整 */
        :root.light-theme .header {
            background-color: rgba(245, 247, 250, 0.95);
            border-bottom: 1px solid var(--border-color);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 60px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .nav-item {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .nav-link {
            color: var(--text-color);
            text-decoration: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link.active {
            color: var(--primary-color);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 0 0 6px 6px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            list-style-type: none;
        }
        
        :root.light-theme .dropdown-menu {
            background-color: var(--background-card);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            border-bottom: 1px solid var(--border-color);
        }

        .dropdown-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px;
        }

        .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            padding-left: 20px;
        }
        
        :root.light-theme .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }

        .dropdown-icon {
            margin-right: 8px;
        }

        .nav-tools {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-icon,
        .user-profile {
            position: relative;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .notification-icon:hover,
        .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        :root.light-theme .notification-icon:hover,
        :root.light-theme .user-profile:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-profile img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 24px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
        }

        .page-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .algorithm-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: var(--highlight-color);
        }

        /* 搜索和筛选区域 */
        .filter-section {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
        }

        .search-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .search-input-wrapper {
            position: relative;
            flex: 1;
        }

        .search-input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.08);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .advanced-filter-toggle {
            white-space: nowrap;
            color: var(--primary-color);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 0 10px;
        }

        .advanced-filter-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .filter-select,
        .filter-input {
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-select:focus,
        .filter-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
        }

        /* 统计卡片区域 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 5px 0 0 5px;
        }

        .stat-card.success::before {
            background-color: var(--success-color);
        }

        .stat-card.warning::before {
            background-color: var(--warning-color);
        }

        .stat-card.danger::before {
            background-color: var(--danger-color);
        }

        .stat-card.info::before {
            background-color: var(--info-color);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
        }

        .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-description {
            font-size: 13px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-trend-up {
            color: var(--success-color);
        }

        .stat-trend-down {
            color: var(--danger-color);
        }

        /* 算法列表表格 */
        .algorithms-section {
            margin-bottom: 24px;
        }

        .table-responsive {
            background-color: var(--background-card);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .table-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .table-title {
            font-size: 16px;
            font-weight: 500;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .bulk-select {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-right: 15px;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
        }

        .bulk-action-btn {
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .bulk-action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .bulk-action-btn.delete {
            color: var(--danger-color);
            border-color: rgba(242, 87, 103, 0.3);
        }

        .bulk-action-btn.delete:hover {
            background-color: rgba(242, 87, 103, 0.1);
            border-color: var(--danger-color);
        }

        .algorithm-table {
            width: 100%;
            border-collapse: collapse;
        }

        .algorithm-table th,
        .algorithm-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .algorithm-table th {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 13px;
            background-color: rgba(255, 255, 255, 0.02);
        }

        .algorithm-table tbody tr {
            transition: all 0.2s ease;
        }

        .algorithm-table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .algorithm-table tbody tr:last-child td {
            border-bottom: none;
        }

        .checkbox-cell {
            width: 30px;
        }

        .algorithm-id {
            font-family: monospace;
            color: var(--text-secondary);
        }

        .algorithm-name {
            font-weight: 500;
        }

        .algorithm-desc {
            color: var(--text-secondary);
            font-size: 13px;
            max-width: 250px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .algorithm-status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .status-draft {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .status-archived {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .status-review {
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--info-color);
        }

        .algorithm-actions-cell {
            text-align: right;
            white-space: nowrap;
        }

        .algorithm-action {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 5px;
        }

        .algorithm-action:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .algorithm-action.edit:hover {
            color: var(--info-color);
            border-color: var(--info-color);
            background-color: rgba(0, 149, 255, 0.1);
        }

        .algorithm-action.copy:hover {
            color: var(--warning-color);
            border-color: var(--warning-color);
            background-color: rgba(255, 185, 70, 0.1);
        }

        .algorithm-action.delete:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
            background-color: rgba(242, 87, 103, 0.1);
        }

        .favorite-action {
            color: var(--text-secondary);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .favorite-action:hover,
        .favorite-action.active {
            color: var(--warning-color);
        }

        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
        }

        .page-info {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .pagination {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .page-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .page-btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .page-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 自定义复选框 */
        .custom-checkbox {
            position: relative;
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 5px;
            vertical-align: middle;
        }

        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 18px;
            width: 18px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .custom-checkbox input:checked~.checkmark {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 2px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input:checked~.checkmark:after {
            display: block;
        }

        /* 最近活动卡片 */
        .recent-activity-card {
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .card-header {
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .card-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .card-action:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
        }

        .card-body {
            padding: 20px;
        }

        .activity-timeline {
            position: relative;
            padding-left: 24px;
            max-height: 350px;
            overflow-y: auto;
        }

        .activity-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 8px;
            height: 100%;
            width: 2px;
            background-color: var(--border-color);
        }

        .activity-item {
            position: relative;
            padding-bottom: 20px;
        }

        .activity-item:last-child {
            padding-bottom: 0;
        }

        .activity-dot {
            position: absolute;
            left: -24px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 3px solid var(--background-card);
            z-index: 1;
        }

        .activity-item.success .activity-dot {
            background-color: var(--success-color);
        }

        .activity-item.warning .activity-dot {
            background-color: var(--warning-color);
        }

        .activity-item.danger .activity-dot {
            background-color: var(--danger-color);
        }

        .activity-content {
            padding-bottom: 12px;
            border-bottom: 1px dashed var(--border-color);
        }

        .activity-item:last-child .activity-content {
            border-bottom: none;
        }

        .activity-title {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .activity-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 模态窗口通用样式 */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 2000;
            animation: fadeIn 0.3s ease;
        }

        .modal {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid var(--border-color);
            overflow: hidden;
            max-width: 90%;
            width: 900px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            animation: slideIn 0.3s ease;
        }

        .modal-lg {
            width: 1100px;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 500;
        }

        .modal-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 22px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            color: var(--text-color);
        }

        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .modal-form-group {
            margin-bottom: 20px;
        }

        .modal-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .modal-sublabel {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .modal-input,
        .modal-select,
        .modal-textarea {
            width: 100%;
            padding: 10px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .modal-input:focus,
        .modal-select:focus,
        .modal-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.08);
        }

        .modal-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .modal-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .modal-checkbox input {
            margin-right: 8px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: var(--background-card);
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--highlight-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: rgba(0, 196, 140, 0.8);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: rgba(242, 87, 103, 0.8);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: var(--border-color);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }

        /* 上传/编辑算法弹窗特定样式 */
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-col {
            flex: 1;
        }

        .form-section {
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px dashed var(--border-color);
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(62, 155, 255, 0.1);
            border-radius: 6px;
            color: var(--primary-color);
        }

        .tag-input-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            min-height: 42px;
        }

        .tag {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            background-color: rgba(62, 155, 255, 0.1);
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid rgba(62, 155, 255, 0.2);
        }

        .tag-close {
            cursor: pointer;
        }

        .tag-input {
            flex: 1;
            min-width: 100px;
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 14px;
            outline: none;
        }

        .file-upload-container {
            border: 2px dashed var(--border-color);
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .file-upload-container:hover {
            border-color: var(--primary-color);
            background-color: rgba(62, 155, 255, 0.05);
        }

        .upload-icon {
            font-size: 32px;
            color: var(--text-secondary);
            margin-bottom: 10px;
        }

        .upload-text {
            margin-bottom: 15px;
        }

        .upload-browse {
            color: var(--primary-color);
            cursor: pointer;
        }

        .upload-file-list {
            max-height: 150px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .upload-file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-icon {
            font-size: 18px;
            color: var(--primary-color);
        }

        .file-name {
            font-size: 14px;
        }

        .file-size {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .file-progress {
            width: 100px;
            height: 4px;
            background-color: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }

        .file-progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 2px;
        }

        .file-actions {
            display: flex;
            gap: 5px;
        }

        .file-action {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .file-action:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-color);
        }

        .file-action.delete:hover {
            color: var(--danger-color);
        }

        .nav-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .nav-tab {
            padding: 10px 16px;
            font-size: 14px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .nav-tab:hover {
            color: var(--text-color);
        }

        .nav-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        /* 参数配置表格 */
        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .params-table th {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-secondary);
            font-size: 13px;
            font-weight: 500;
            text-align: left;
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .params-table td {
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
        }

        .params-table tr:last-child td {
            border-bottom: none;
        }

        .param-name-cell {
            width: 25%;
        }

        .param-type-cell {
            width: 15%;
        }

        .param-default-cell {
            width: 20%;
        }

        .param-desc-cell {
            width: 30%;
        }

        .param-actions-cell {
            width: 10%;
            text-align: right;
        }

        .param-action {
            width: 26px;
            height: 26px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-secondary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 2px;
        }

        .param-action:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-color);
        }

        .param-action.delete:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .add-param-btn {
            margin-bottom: 20px;
        }

        /* 环境配置样式 */
        .env-requirements {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .requirement-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--border-color);
            border-radius: 6px;
        }

        .requirement-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(62, 155, 255, 0.1);
            border-radius: 6px;
            color: var(--primary-color);
            font-size: 14px;
        }

        .requirement-info {
            flex: 1;
        }

        .requirement-name {
            font-size: 14px;
            font-weight: 500;
        }

        .requirement-value {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 算法测试弹窗样式 */
        .test-config-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .test-section-title {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 10px;
            grid-column: 1 / -1;
        }

        .test-dataset-select {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .dataset-option {
            flex: 1;
            padding: 12px;
            background-color: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }

        .dataset-option:hover {
            background-color: rgba(62, 155, 255, 0.05);
            border-color: rgba(62, 155, 255, 0.3);
        }

        .dataset-option.active {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .dataset-option-icon {
            font-size: 18px;
            margin-bottom: 8px;
            color: var(--text-secondary);
        }

        .dataset-option.active .dataset-option-icon {
            color: var(--primary-color);
        }

        .dataset-option-label {
            font-size: 14px;
        }

        .test-params {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .param-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .param-label {
            width: 120px;
            font-size: 14px;
        }

        .param-control {
            flex: 1;
        }

        /* 字段映射样式 */
        .field-mapping-section {
            margin-bottom: 20px;
        }

        .mapping-header {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 10px;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.02);
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .mapping-row {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 10px;
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .mapping-col {
            display: flex;
            align-items: center;
        }

        .btn-icon {
            padding: 6px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .btn-icon:hover {
            color: var(--danger-color);
        }

        /* 测试结果区域样式 */
        .test-progress-section {
            margin-bottom: 25px;
        }

        .progress-container {
            margin-bottom: 15px;
        }

        .progress-bar {
            height: 8px;
            background-color: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-status {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .test-status {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 15px;
        }

        .status-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            background-color: rgba(255, 255, 255, 0.05);
        }

        .status-icon.success {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .status-icon.warning {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .status-icon.error {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .status-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .test-result-section {
            margin-bottom: 25px;
        }

        .result-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 15px;
        }

        .result-tab {
            padding: 8px 16px;
            font-size: 13px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .result-tab:hover {
            color: var(--text-color);
        }

        .result-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .result-content {
            display: none;
        }

        .result-content.active {
            display: block;
        }

        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .result-table th {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-secondary);
            font-size: 12px;
            font-weight: 500;
            text-align: left;
            padding: 8px 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .result-table td {
            padding: 8px 10px;
            border-bottom: 1px solid var(--border-color);
            font-size: 13px;
        }

        .result-table tr:last-child td {
            border-bottom: none;
        }

        .metrics-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .metrics-name {
            font-size: 13px;
        }

        .metrics-value {
            font-size: 13px;
            font-weight: 500;
        }

        .metrics-value.good {
            color: var(--success-color);
        }

        .metrics-value.warning {
            color: var(--warning-color);
        }

        .metrics-value.bad {
            color: var(--danger-color);
        }

        .chart-container {
            height: 300px;
            width: 100%;
            margin-bottom: 15px;
        }

        .logs-container {
            height: 300px;
            overflow-y: auto;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            border: 1px solid var(--border-color);
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            color: #e0e0e0;
        }

        .log-line {
            margin-bottom: 2px;
        }

        .log-time {
            color: var(--text-secondary);
            margin-right: 8px;
        }

        .log-level {
            padding: 1px 4px;
            border-radius: 3px;
            margin-right: 8px;
            font-size: 10px;
        }

        .log-level.info {
            background-color: rgba(0, 149, 255, 0.2);
            color: var(--info-color);
        }

        .log-level.warning {
            background-color: rgba(255, 185, 70, 0.2);
            color: var(--warning-color);
        }

        .log-level.error {
            background-color: rgba(242, 87, 103, 0.2);
            color: var(--danger-color);
        }

        .log-level.success {
            background-color: rgba(0, 196, 140, 0.2);
            color: var(--success-color);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                transform: translate(-50%, -60%);
                opacity: 0;
            }

            to {
                transform: translate(-50%, -50%);
                opacity: 1;
            }
        }

        /* 响应式设计 */
        @media screen and (max-width: 1200px) {
            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }

            .advanced-filter-row {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-row {
                flex-direction: column;
            }

            .modal {
                width: 95%;
            }

            .env-requirements {
                grid-template-columns: 1fr;
            }

            .test-config-section {
                grid-template-columns: 1fr;
            }
        }

        @media screen and (max-width: 768px) {
            .stats-cards {
                grid-template-columns: 1fr;
            }

            .algorithm-table {
                min-width: 800px;
            }

            .table-responsive {
                overflow-x: auto;
            }

            .advanced-filter-row {
                grid-template-columns: 1fr;
            }

            .bulk-actions {
                display: none;
            }

            .test-status {
                flex-direction: column;
                align-items: center;
            }
        }

        @media screen and (max-width: 576px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .algorithm-actions {
                width: 100%;
            }

            .search-row {
                flex-direction: column;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 测试概览样式 */
        .result-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .summary-card {
            background: var(--background-card);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .summary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .summary-card.success {
            border-left: 4px solid var(--success-color);
        }

        .summary-card.info {
            border-left: 4px solid var(--info-color);
        }

        .summary-card.warning {
            border-left: 4px solid var(--warning-color);
        }

        .summary-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .summary-card .summary-icon {
            background: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
        }

        .summary-card.success .summary-icon {
            background: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .summary-card.info .summary-icon {
            background: rgba(62, 155, 255, 0.1);
            color: var(--info-color);
        }

        .summary-card.warning .summary-icon {
            background: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .summary-info {
            flex: 1;
        }

        .summary-label {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .summary-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-color);
        }

        .summary-card.success .summary-value {
            color: var(--success-color);
        }

        .summary-card.info .summary-value {
            color: var(--info-color);
        }

        .summary-card.warning .summary-value {
            color: var(--warning-color);
        }

        @media (max-width: 768px) {
            .result-summary {
                grid-template-columns: 1fr;
            }
        }

        .status-badge.error {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        /* 测试结果状态样式 */
        .test-result-status {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            text-align: center;
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease;
        }

        .status-success, .status-fail {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 500;
            animation: scaleIn 0.3s ease;
        }

        .status-success i, .status-fail i {
            margin-right: 8px;
            font-size: 24px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes scaleIn {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* 进度条样式优化 */
        .progress-fill {
            transition: width 0.8s ease-in-out;
        }

        /* 算法详情弹窗样式 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            background-color: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
        }

        .info-item.wide {
            grid-column: 1 / -1;
        }

        .info-label {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .info-value {
            font-size: 15px;
            color: var(--text-color);
            line-height: 1.6;
        }

        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .info-tag {
            display: inline-block;
            padding: 5px 10px;
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(62, 155, 255, 0.3);
            border-radius: 20px;
            font-size: 12px;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .param-table th,
        .param-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .param-table th {
            background-color: rgba(255, 255, 255, 0.02);
            font-weight: 600;
            font-size: 14px;
        }

        .param-table tr:last-child td {
            border-bottom: none;
        }

        .status-success, .status-fail {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 500;
            animation: scaleIn 0.3s ease;
        }

        /* 主题切换开关样式 */
        .theme-switch {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 60px;
            height: 30px;
            background: rgba(0, 149, 255, 0.15);
            border-radius: 30px;
            padding: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .theme-switch:hover {
            transform: scale(1.05);
        }
        
        .theme-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-size: 14px;
        }
        
        .theme-switch-slider {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            left: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        :root.light-theme .theme-switch-slider {
            transform: translateX(30px);
        }

        /* 亮色主题的按钮样式调整 */
        :root.light-theme .action-btn {
            background-color: #F5F7FA;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .action-btn.primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .action-btn.primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        /* 亮色主题的统计卡片 */
        :root.light-theme .stat-card {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .stat-card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .stat-icon {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        /* 亮色主题的表格调整 */
        :root.light-theme .table-responsive {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .table-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .bulk-action-btn {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .bulk-action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .algorithm-table th {
            background-color: rgba(0, 0, 0, 0.01);
        }
        
        :root.light-theme .algorithm-table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        :root.light-theme .status-active {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .status-draft {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .status-archived {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        :root.light-theme .status-review {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .algorithm-action {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .algorithm-action:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        /* 亮色主题的弹窗样式调整 */
        :root.light-theme .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.4);
        }
        
        :root.light-theme .modal-content {
            background-color: var(--background-card);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .modal-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .modal-footer {
            border-top: 1px solid var(--border-color);
        }
        
        /* 亮色主题的表单元素 */
        :root.light-theme .form-control {
            background-color: #F8FAFD;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .form-control:focus {
            border-color: var(--primary-color);
            background-color: #FFFFFF;
        }
        
        /* 亮色主题的滚动条 */
        :root.light-theme ::-webkit-scrollbar-track {
            background: #F5F7FA;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb {
            background: #D0D6E0;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
        
        /* 亮色主题的代码编辑器 */
        :root.light-theme .code-editor {
            background-color: #F8FAFD;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .tabs-header {
            background-color: #F0F2F5;
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .tab {
            background-color: rgba(0, 0, 0, 0.02);
            border: 1px solid var(--border-color);
            border-bottom: none;
        }
        
        :root.light-theme .tab.active {
            background-color: #F8FAFD;
            border-bottom-color: #F8FAFD;
        }
    </style>

<body>
    <div class="container">

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题和操作按钮 -->
            <div class="page-header">
                
                <div class="algorithm-actions">
                    <!-- Removed buttons as requested -->
                </div>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="filter-section">
                <div class="search-row">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索算法名称、ID、创建人...">
                    </div>
                    <button class="advanced-filter-toggle">
                        <i class="fas fa-sliders-h"></i> 高级筛选
                    </button>
                </div>
                <div class="advanced-filter-row">
                    <div class="filter-group">
                        <label class="filter-label">算法状态</label>
                        <select class="filter-select">
                            <option value="">全部状态</option>
                            <option value="active">已发布</option>
                            <option value="draft">待审核</option>
                            <option value="review">审核中</option>
                            <option value="archived">已驳回</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">所属领域</label>
                        <select class="filter-select">
                            <option value="">全部领域</option>
                            <option value="power">电力系统</option>
                            <option value="transportation">交通系统</option>
                            <option value="water">水利系统</option>
                            <option value="gas">燃气系统</option>
                            <option value="communication">通信系统</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">创建时间</label>
                        <select class="filter-select">
                            <option value="">全部时间</option>
                            <option value="today">今天</option>
                            <option value="yesterday">昨天</option>
                            <option value="last7days">最近7天</option>
                            <option value="last30days">最近30天</option>
                            <option value="custom">自定义范围</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">创建人</label>
                        <select class="filter-select">
                            <option value="">全部创建人</option>
                            <option value="self">我创建的</option>
                            <option value="team">我团队的</option>
                        </select>
                    </div>
                </div>
                <div class="filter-actions">
                    <button class="action-btn">重置筛选</button>
                    <button class="action-btn primary">应用筛选</button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-cards">
                <div class="stat-card info">
                    <div class="stat-header">
                        <div class="stat-title">算法总数</div>
                        <div class="stat-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                    </div>
                    <div class="stat-value">87</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长 12 个</span>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-header">
                        <div class="stat-title">已发布</div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">62</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长 8 个</span>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-header">
                        <div class="stat-title">待审核</div>
                        <div class="stat-icon">
                            <i class="fas fa-hourglass-half"></i>
                        </div>
                    </div>
                    <div class="stat-value">13</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长 4 个</span>
                    </div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-header">
                        <div class="stat-title">已驳回</div>
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">8</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-down stat-trend-down"></i>
                        <span>较上月减少 3 个</span>
                    </div>
                </div>
            </div>

            <!-- 算法列表 -->
            <div class="algorithms-section">
                <div class="table-responsive">
                    <div class="table-header">
                        <div class="table-title">全部算法</div>
                        <div class="table-actions">
                            <div class="bulk-select">
                                <label class="custom-checkbox">
                                    <input type="checkbox" id="select-all">
                                    <span class="checkmark"></span>
                                </label>
                                <span>全选</span>
                            </div>
                            <div class="bulk-actions">
                                <button class="bulk-action-btn">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                                <button class="bulk-action-btn">
                                    <i class="fas fa-archive"></i> 归档
                                </button>
                                <button class="bulk-action-btn delete">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                    <table class="algorithm-table">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </th>
                                <th>算法名称</th>
                                <th>ID</th>
                                <th>领域</th>
                                <th>状态</th>
                                <th>创建人</th>
                                <th>创建时间</th>
                                <th>使用次数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="algorithm-name">电压稳定性指标计算算法</span>
                                    <div class="algorithm-desc">基于电压裕度和暂态稳定性的综合指标计算</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250401-001</span></td>
                                <td>电力系统</td>
                                <td><span class="algorithm-status-badge status-active">已发布</span></td>
                                <td>张三</td>
                                <td>2025-03-15</td>
                                <td>142</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑"
                                        onclick="openUploadAlgorithmModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="algorithm-name">潮流计算准确性评估算法</span>
                                    <div class="algorithm-desc">评估潮流计算与实际运行数据之间的偏差</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250402-002</span></td>
                                <td>电力系统</td>
                                <td><span class="algorithm-status-badge status-active">已发布</span></td>
                                <td>李四</td>
                                <td>2025-03-20</td>
                                <td>128</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">负荷模型仿真度评估算法</span>
                                    <div class="algorithm-desc">评估不同类型负荷模型的仿真准确度</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250403-003</span></td>
                                <td>电力系统</td>
                                <td><span class="algorithm-status-badge status-review">审核中</span></td>
                                <td>王五</td>
                                <td>2025-03-28</td>
                                <td>56</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">交通流量仿真精度计算算法</span>
                                    <div class="algorithm-desc">基于实测数据评估交通流量模型精度</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250404-004</span></td>
                                <td>交通系统</td>
                                <td><span class="algorithm-status-badge status-draft">待审核</span></td>
                                <td>赵六</td>
                                <td>2025-04-01</td>
                                <td>12</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">通信网络时延评估算法</span>
                                    <div class="algorithm-desc">计算通信网络模型与实际网络时延差异</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250405-005</span></td>
                                <td>通信系统</td>
                                <td><span class="algorithm-status-badge status-archived">已驳回</span></td>
                                <td>钱七</td>
                                <td>2025-02-10</td>
                                <td>87</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">发电机组暂态模型评估算法</span>
                                    <div class="algorithm-desc">基于暂态数据评估发电机组建模准确性</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250406-006</span></td>
                                <td>电力系统</td>
                                <td><span class="algorithm-status-badge status-active">已发布</span></td>
                                <td>张三</td>
                                <td>2025-03-05</td>
                                <td>93</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">跨行业级联效应评估算法</span>
                                    <div class="algorithm-desc">评估电力-通信-交通系统间级联故障传播仿真精度</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250407-007</span></td>
                                <td>跨行业</td>
                                <td><span class="algorithm-status-badge status-review">审核中</span></td>
                                <td>李四</td>
                                <td>2025-03-30</td>
                                <td>18</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">最优潮流算法</span>
                                    <div class="algorithm-desc">基于混合整数规划的电力系统最优潮流计算方法</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250404-004</span></td>
                                <td>电力系统</td>
                                <td><span class="algorithm-status-badge status-review">审核中</span></td>
                                <td>王五</td>
                                <td>2025-04-02</td>
                                <td>76</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">网络拓扑分析算法</span>
                                    <div class="algorithm-desc">电力网络拓扑特性和脆弱性分析</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250405-005</span></td>
                                <td>电力系统</td>
                                <td><span class="algorithm-status-badge status-draft">待审核</span></td>
                                <td>赵六</td>
                                <td>2025-04-05</td>
                                <td>42</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">级联失效风险评估算法</span>
                                    <div class="algorithm-desc">电力系统级联失效风险的量化评估方法</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250406-006</span></td>
                                <td>电力系统</td>
                                <td><span class="algorithm-status-badge status-archived">已驳回</span></td>
                                <td>孙七</td>
                                <td>2025-04-06</td>
                                <td>28</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="algorithm-name">跨行业耦合分析算法</span>
                                    <div class="algorithm-desc">电力与交通系统耦合特性分析</div>
                                </td>
                                <td><span class="algorithm-id">ALG-20250407-007</span></td>
                                <td>多领域</td>
                                <td><span class="algorithm-status-badge status-active">已发布</span></td>
                                <td>周八</td>
                                <td>2025-04-07</td>
                                <td>56</td>
                                <td class="algorithm-actions-cell">
                                    <button class="algorithm-action view" title="查看" onclick="openAlgorithmDetailModal()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="algorithm-action edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="algorithm-action test" title="测试" onclick="openTestAlgorithmModal()">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="algorithm-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-footer">
                        <div class="page-info">
                            显示 1 到 7 条，共 87 条记录
                        </div>
                        <div class="pagination">
                            <button class="page-btn disabled">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button class="page-btn disabled">
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">13</button>
                            <button class="page-btn">
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button class="page-btn">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近算法活动卡片 -->
            <div class="recent-activity-card">
                <div class="card-header">
                    <div class="card-title">算法活动日志</div>
                    <div class="card-actions">
                        <button class="card-action"><i class="fas fa-sync-alt"></i></button>
                        <button class="card-action"><i class="fas fa-ellipsis-v"></i></button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="activity-timeline">
                        <div class="activity-item success">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">李四更新了"跨行业级联效应评估算法"，提交审核</div>
                                <div class="activity-time">今天 10:23</div>
                            </div>
                        </div>
                        <div class="activity-item warning">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">系统检测到"电压稳定性指标计算算法"测试结果出现异常</div>
                                <div class="activity-time">昨天 16:45</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">赵六创建了"交通流量仿真精度计算算法"</div>
                                <div class="activity-time">昨天 14:23</div>
                            </div>
                        </div>
                        <div class="activity-item success">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">王五的"负荷模型仿真度评估算法"通过了初步测试</div>
                                <div class="activity-time">04-02 11:05</div>
                            </div>
                        </div>
                        <div class="activity-item danger">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">管理员驳回"通信网络时延评估算法"的旧版本</div>
                                <div class="activity-time">04-01 09:17</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">系统完成了所有电力系统算法的季度性能评估</div>
                                <div class="activity-time">04-01 08:30</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 上传/配置算法弹窗 -->
    <div class="modal-overlay" id="uploadAlgorithmModal">
        <div class="modal modal-lg">
            <div class="modal-header">
                <div>
                    <h2 class="modal-title">上传/配置算法</h2>
                    <p class="modal-subtitle">上传新算法或更新已有算法的配置</p>
                </div>
                <button class="modal-close" onclick="closeModal('uploadAlgorithmModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-section">
                    <div class="section-title">
                        <div class="section-icon"><i class="fas fa-info-circle"></i></div>
                        <span>基本信息</span>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="modal-form-group">
                                <label class="modal-label">算法名称</label>
                                <input type="text" class="modal-input" placeholder="输入算法名称" value="电压稳定性指标计算算法">
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="modal-form-group">
                                <label class="modal-label">所属领域</label>
                                <select class="modal-select">
                                    <option value="power" selected>电力系统</option>
                                    <option value="transportation">交通系统</option>
                                    <option value="water">水利系统</option>
                                    <option value="gas">燃气系统</option>
                                    <option value="communication">通信系统</option>
                                    <option value="multi">跨行业</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="modal-form-group">
                                <label class="modal-label">算法版本</label>
                                <input type="text" class="modal-input" placeholder="输入版本号" value="1.2.3">
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="modal-form-group">
                                <label class="modal-label">入口文件/函数</label>
                                <input type="text" class="modal-input" placeholder="算法入口文件或函数名" value="calculate_vsi">
                            </div>
                        </div>
                    </div>
                    <div class="modal-form-group">
                        <label class="modal-label">算法描述</label>
                        <textarea class="modal-textarea"
                            placeholder="描述算法的功能、原理和应用场景">基于电压裕度和暂态稳定性的综合指标计算算法，结合静态电压稳定裕度和动态暂态稳定性评估，用于评估电力系统仿真模型对电压稳定性的模拟精度。该算法采用基于灵敏度分析的方法计算节点电压稳定指标(VSI)，并与实际电力系统运行数据进行对比，评估仿真模型的准确性。</textarea>
                    </div>
                    <div class="modal-form-group">
                        <label class="modal-label">标签</label>
                        <div class="tag-input-container">
                            <div class="tag">
                                电压稳定性 <span class="tag-close"><i class="fas fa-times"></i></span>
                            </div>
                            <div class="tag">
                                仿真度评估 <span class="tag-close"><i class="fas fa-times"></i></span>
                            </div>
                            <div class="tag">
                                电力系统 <span class="tag-close"><i class="fas fa-times"></i></span>
                            </div>
                            <div class="tag">
                                指标计算 <span class="tag-close"><i class="fas fa-times"></i></span>
                            </div>
                            <input type="text" class="tag-input" placeholder="输入标签后按回车添加">
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <div class="section-title">
                        <div class="section-icon"><i class="fas fa-file-upload"></i></div>
                        <span>算法文件上传</span>
                    </div>
                    <div class="file-upload-container">
                        <div class="upload-icon"><i class="fas fa-cloud-upload-alt"></i></div>
                        <div class="upload-text">拖拽算法文件到此处或 <span class="upload-browse">浏览文件</span></div>
                        <div class="upload-desc">仅支持 Python (.py) 文件, 单个文件不超过50MB
                        </div>
                    </div>
                    <div class="upload-file-list">
                        <div class="upload-file-item">
                            <div class="file-info">
                                <div class="file-icon"><i class="fas fa-file-code"></i></div>
                                <div>
                                    <div class="file-name">voltage_stability_index.py</div>
                                    <div class="file-size">2.8 MB</div>
                                </div>
                            </div>
                            <div class="file-progress">
                                <div class="file-progress-bar" style="width: 100%;"></div>
                            </div>
                            <div class="file-actions">
                                <button class="file-action"><i class="fas fa-eye"></i></button>
                                <button class="file-action delete"><i class="fas fa-trash-alt"></i></button>
                            </div>
                        </div>
                        <div class="upload-file-item">
                            <div class="file-info">
                                <div class="file-icon"><i class="fas fa-file-archive"></i></div>
                                <div>
                                    <div class="file-name">lib_dependencies.zip</div>
                                    <div class="file-size">5.2 MB</div>
                                </div>
                            </div>
                            <div class="file-progress">
                                <div class="file-progress-bar" style="width: 100%;"></div>
                            </div>
                            <div class="file-actions">
                                <button class="file-action"><i class="fas fa-eye"></i></button>
                                <button class="file-action delete"><i class="fas fa-trash-alt"></i></button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <div class="section-icon"><i class="fas fa-sliders-h"></i></div>
                        <span>参数配置</span>
                    </div>
                    <button class="btn btn-secondary add-param-btn">
                        <i class="fas fa-plus"></i> 添加参数
                    </button>
                    <table class="params-table">
                        <thead>
                            <tr>
                                <th class="param-name-cell">参数名称</th>
                                <th class="param-type-cell">类型</th>
                                <th class="param-default-cell">默认值</th>
                                <th class="param-desc-cell">描述</th>
                                <th class="param-actions-cell">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>threshold</td>
                                <td>float</td>
                                <td>0.75</td>
                                <td>电压稳定裕度阈值，用于判断系统稳定状态</td>
                                <td class="param-actions-cell">
                                    <button class="param-action"><i class="fas fa-edit"></i></button>
                                    <button class="param-action delete"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>window_size</td>
                                <td>int</td>
                                <td>10</td>
                                <td>计算移动平均时的窗口大小（采样点数）</td>
                                <td class="param-actions-cell">
                                    <button class="param-action"><i class="fas fa-edit"></i></button>
                                    <button class="param-action delete"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>critical_buses</td>
                                <td>list</td>
                                <td>[1, 5, 9, 12]</td>
                                <td>重点监测的关键母线编号列表</td>
                                <td class="param-actions-cell">
                                    <button class="param-action"><i class="fas fa-edit"></i></button>
                                    <button class="param-action delete"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>weight_static</td>
                                <td>float</td>
                                <td>0.6</td>
                                <td>静态电压稳定性评估权重</td>
                                <td class="param-actions-cell">
                                    <button class="param-action"><i class="fas fa-edit"></i></button>
                                    <button class="param-action delete"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>weight_dynamic</td>
                                <td>float</td>
                                <td>0.4</td>
                                <td>动态暂态稳定性评估权重</td>
                                <td class="param-actions-cell">
                                    <button class="param-action"><i class="fas fa-edit"></i></button>
                                    <button class="param-action delete"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <div class="section-icon"><i class="fas fa-server"></i></div>
                        <span>执行环境要求</span>
                    </div>
                    <div class="env-requirements">
                        <div class="requirement-item">
                            <div class="requirement-icon">
                                <i class="fab fa-python"></i>
                            </div>
                            <div class="requirement-info">
                                <div class="requirement-name">Python 版本</div>
                                <div class="requirement-value">Python 3.8 或更高</div>
                            </div>
                        </div>
                        <div class="requirement-item">
                            <div class="requirement-icon">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <div class="requirement-info">
                                <div class="requirement-name">CPU 需求</div>
                                <div class="requirement-value">4核 2.5GHz 或更高</div>
                            </div>
                        </div>
                        <div class="requirement-item">
                            <div class="requirement-icon">
                                <i class="fas fa-memory"></i>
                            </div>
                            <div class="requirement-info">
                                <div class="requirement-name">内存需求</div>
                                <div class="requirement-value">最小 8GB，推荐 16GB</div>
                            </div>
                        </div>
                        <div class="requirement-item">
                            <div class="requirement-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="requirement-info">
                                <div class="requirement-name">典型执行时间</div>
                                <div class="requirement-value">
                                    < 2分钟 (中等规模系统)</div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-form-group">
                            <label class="modal-label">依赖库/包</label>
                            <textarea class="modal-textarea" placeholder="每行一个依赖库/包">numpy>=1.20.0
pandas>=1.3.0
scipy>=1.7.0
matplotlib>=3.4.0
pandapower>=2.7.0
scikit-learn>=0.24.0</textarea>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon"><i class="fas fa-exchange-alt"></i></div>
                            <span>输入/输出配置</span>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="modal-form-group">
                                    <label class="modal-label">输入数据格式</label>
                                    <select class="modal-select">
                                        <option value="csv" selected>CSV文件</option>
                                        <option value="json">JSON文件</option>
                                        <option value="excel">Excel文件</option>
                                        <option value="xml">XML文件</option>
                                        <option value="db">数据库表</option>
                                    </select>
                                </div>
                                <div class="modal-form-group">
                                    <label class="modal-label">输入数据描述</label>
                                    <textarea class="modal-textarea" placeholder="描述输入数据的结构和要求">1. 电力系统拓扑文件：包含节点、线路和设备信息的CSV文件
2. 电压测量数据：包含母线电压幅值和相角的时间序列数据
3. 负荷功率数据：包含有功和无功负荷的时间序列数据
4. 发电机出力数据：包含发电机有功和无功出力的时间序列数据</textarea>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="modal-form-group">
                                    <label class="modal-label">输出数据格式</label>
                                    <select class="modal-select">
                                        <option value="csv">CSV文件</option>
                                        <option value="json" selected>JSON文件</option>
                                        <option value="excel">Excel文件</option>
                                        <option value="xml">XML文件</option>
                                        <option value="db">数据库表</option>
                                    </select>
                                </div>
                                <div class="modal-form-group">
                                    <label class="modal-label">输出数据描述</label>
                                    <textarea class="modal-textarea" placeholder="描述输出数据的结构和格式">1. 综合电压稳定性仿真度指标：0-100的数值
2. 静态电压稳定裕度评估：每个关键母线的稳定裕度值
3. 动态暂态稳定性评估：系统恢复时间和过冲幅值
4. 节点电压重要性排序：按照灵敏度排序的关键节点列表
5. 详细评估报告：包含各项子指标的JSON数据</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon"><i class="fas fa-file-alt"></i></div>
                            <span>文档与说明</span>
                        </div>
                        <div class="file-upload-container">
                            <div class="upload-icon"><i class="fas fa-cloud-upload-alt"></i></div>
                            <div class="upload-text">拖拽文档文件到此处或 <span class="upload-browse">浏览文件</span></div>
                            <div class="upload-desc">支持 PDF, Word, Markdown 文件，单个文件不超过20MB</div>
                        </div>
                        <div class="upload-file-list">
                            <div class="upload-file-item">
                                <div class="file-info">
                                    <div class="file-icon"><i class="fas fa-file-pdf"></i></div>
                                    <div>
                                        <div class="file-name">电压稳定性指标计算算法说明文档.pdf</div>
                                        <div class="file-size">3.4 MB</div>
                                    </div>
                                </div>
                                <div class="file-progress">
                                    <div class="file-progress-bar" style="width: 100%;"></div>
                                </div>
                                <div class="file-actions">
                                    <button class="file-action"><i class="fas fa-eye"></i></button>
                                    <button class="file-action delete"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                            <div class="upload-file-item">
                                <div class="file-info">
                                    <div class="file-icon"><i class="fas fa-file-word"></i></div>
                                    <div>
                                        <div class="file-name">算法使用说明.docx</div>
                                        <div class="file-size">1.2 MB</div>
                                    </div>
                                </div>
                                <div class="file-progress">
                                    <div class="file-progress-bar" style="width: 100%;"></div>
                                </div>
                                <div class="file-actions">
                                    <button class="file-action"><i class="fas fa-eye"></i></button>
                                    <button class="file-action delete"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon"><i class="fas fa-link"></i></div>
                            <span>关联模版</span>
                        </div>
                        <div class="modal-form-group">
                            <label class="modal-label">关联测评模版</label>
                            <select class="modal-select" multiple size="4">
                                <option value="tpl1" selected>电力系统仿真度标准评估模版</option>
                                <option value="tpl2">电力系统暂态仿真专项评估模版</option>
                                <option value="tpl3" selected>城市级联关基行业综合仿真度评估模版</option>
                                <option value="tpl4">电力系统稳定性评估模版</option>
                            </select>
                            <div class="modal-sublabel">可多选，按住Ctrl键选择多个模版</div>
                        </div>
                    </div> -->
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('uploadAlgorithmModal')">取消</button>
                    <button class="btn btn-secondary">保存为草稿</button>
                    <button class="btn btn-primary" onclick="openTestAlgorithmModal()">保存并测试</button>
                </div>
            </div>
        </div>

        <!-- 算法测试/验证弹窗 -->
        <div class="modal-overlay" id="testAlgorithmModal">
            <div class="modal modal-lg">
                <div class="modal-header">
                    <div>
                        <h2 class="modal-title">算法测试与验证</h2>
                        <p class="modal-subtitle">电压稳定性指标计算算法 (v1.2.3)</p>
                    </div>
                    <button class="modal-close" onclick="closeModal('testAlgorithmModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon"><i class="fas fa-cog"></i></div>
                            <span>测试配置</span>
                        </div>

                        <div class="test-config-section">
                            <div class="test-section-title">选择测试数据集</div>
                            <div class="test-dataset-select">
                                <div class="dataset-option active">
                                    <div class="dataset-option-icon"><i class="fas fa-database"></i></div>
                                    <div class="dataset-option-label">选择已有的测试数据</div>
                                </div>
                                <div class="dataset-option">
                                    <div class="dataset-option-icon"><i class="fas fa-upload"></i></div>
                                    <div class="dataset-option-label">使用上传的测试数据</div>
                                </div>
                            </div>

                            <div class="modal-form-group">
                                <label class="modal-label">选择具体数据集</label>
                                <select class="modal-select">
                                    <option value="dataset1" selected>IEEE 39节点系统数据集</option>
                                    <option value="dataset2">IEEE 118节点系统数据集</option>
                                    <option value="dataset3">省级电网实测数据集</option>
                                    <option value="dataset4">城市配电网数据集</option>
                                </select>
                            </div>
                        </div>

                            <div class="modal-form-group">
                                <label class="modal-label">测试环境</label>
                                <select class="modal-select">
                                    <option value="env1" selected>node1 - CentOS 7 - 4核CPU - 256G内存 - 1T硬盘</option>
                                    <option value="env2">node2 - Ubuntu 20.04 - 8核CPU - 512G内存 - 2T硬盘</option>
                                    <option value="env3">node3 - CentOS 8 - 16核CPU - 1T内存 - 4T硬盘</option>
                                    <option value="env4">node4 - Debian 11 - 32核CPU - 2T内存 - 8T硬盘</option>
                                </select>
                            </div>

                        <div class="test-section-title">算法参数调整</div>
                        <div class="test-params">
                            <div class="param-row">
                                <div class="param-label">threshold</div>
                                <div class="param-control">
                                    <input type="number" class="modal-input" value="0.75" step="0.01">
                                </div>
                            </div>
                            <div class="param-row">
                                <div class="param-label">window_size</div>
                                <div class="param-control">
                                    <input type="number" class="modal-input" value="10" step="1">
                                </div>
                            </div>
                            <div class="param-row">
                                <div class="param-label">weight_static</div>
                                <div class="param-control">
                                    <input type="number" class="modal-input" value="0.6" step="0.1" min="0" max="1">
                                </div>
                            </div>
                            <div class="param-row">
                                <div class="param-label">weight_dynamic</div>
                                <div class="param-control">
                                    <input type="number" class="modal-input" value="0.4" step="0.1" min="0" max="1">
                                </div>
                            </div>
                        </div>

                        <div class="test-section-title" style="margin-top: 20px;">输入字段映射</div>
                        <div class="field-mapping-section">
                            <div class="mapping-header">
                                <div class="mapping-col">算法输入参数</div>
                                <div class="mapping-col">映射到测试数据字段</div>
                                <div class="mapping-col">操作</div>
                            </div>
                            <div id="inputMappings">
                                <div class="mapping-row">
                                    <div class="mapping-col">
                                        <select class="modal-select">
                                            <option value="">选择算法输入参数</option>
                                            <option value="threshold">inputVar1</option>
                                            <option value="window_size">inputVar2</option>
                                        </select>
                                    </div>
                                    <div class="mapping-col">
                                        <select class="modal-select">
                                            <option value="">选择测试数据字段</option>
                                            <option value="voltage">电压数据</option>
                                            <option value="current">电流数据</option>
                                            <option value="power">功率数据</option>
                                            <option value="frequency">频率数据</option>
                                        </select>
                                    </div>
                                    <div class="mapping-col">
                                        <button class="btn btn-icon" onclick="removeMapping(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-secondary" onclick="addInputMapping()">
                                <i class="fas fa-plus"></i> 添加输入映射
                            </button>
                        </div>

                        <div class="test-section-title">输出字段映射</div>
                        <div class="field-mapping-section">
                            <div class="mapping-header">
                                <div class="mapping-col">算法输出序号</div>
                                <div class="mapping-col">映射到测试数据字段</div>
                            </div>
                            <div id="outputMappings">
                                <div class="mapping-row">
                                    <div class="mapping-col">
                                        <input type="text" class="modal-input" value="输出1" readonly>
                                    </div>
                                    <div class="mapping-col">
                                        <select class="modal-select" required>
                                            <option value="">选择测试数据字段</option>
                                            <option value="voltage">电压数据</option>
                                            <option value="current">电流数据</option>
                                            <option value="power">功率数据</option>
                                            <option value="frequency">频率数据</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="test-section-title">测试目标设置</div>
                        <div class="test-params">
                            <div class="param-row">
                                <div class="param-label" style="flex: 1;">通过率:</div>
                                <div class="param-control">
                                    <input type="number" class="modal-input" id="passRateInput" value="80" step="1" min="0" max="100">
                                </div>
                                <span> % </span>
                            </div>
                            
                        </div>

                        <div class="modal-form-group" style="margin-top: 20px;">
                            <button class="btn btn-primary" style="width: 100%;" onclick="runAlgorithmTest()">
                                <i class="fas fa-play"></i> 开始测试
                            </button>
                        </div>
                    </div>

                    <div class="form-section" id="testResultSection" style="display: none;">
                        <div class="section-title">
                            <div class="section-icon"><i class="fas fa-chart-line"></i></div>
                            <span>测试结果</span>
                        </div>

                        <div class="test-progress-section">
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="testProgress" style="width: 0%"></div>
                                </div>
                                <div class="progress-status">
                                    <span>测试进度</span>
                                    <span id="progressText">0%</span>
                                </div>
                            </div>
                            <div class="test-status">
                                <div class="status-item">
                                    <div class="status-icon success" id="successCount">0</div>
                                    <div class="status-label">通过</div>
                                </div>
                                <div class="status-item" style="display: none;">
                                    <div class="status-icon warning" id="warningCount">0</div>
                                    <div class="status-label">警告</div>
                                </div>
                                <div class="status-item">
                                    <div class="status-icon error" id="errorCount">0</div>
                                    <div class="status-label">失败</div>
                                </div>
                            </div>
                            <!-- 添加测试结果通过/不通过标记 -->
                            <div class="test-result-status" id="testResultStatus" style="display: none;">
                                <div class="status-success" id="statusSuccess" style="display: none;">
                                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 24px;"></i>
                                    <span style="color: #28a745; font-weight: bold; margin-left: 8px;">测试通过</span>
                                </div>
                                <div class="status-fail" id="statusFail" style="display: none;">
                                    <i class="fas fa-times-circle" style="color: #dc3545; font-size: 24px;"></i>
                                    <span style="color: #dc3545; font-weight: bold; margin-left: 8px;">测试不通过</span>
                                </div>
                            </div>
                        </div>

                        <div class="test-result-section">
                            <div class="result-tabs">
                                <div class="result-tab active" onclick="switchResultTab('summary')">测试概览</div>
                                <div class="result-tab" onclick="switchResultTab('charts')">图表分析</div>
                                <div class="result-tab" onclick="switchResultTab('details')">详细数据</div>
                            </div>

                            <div id="summaryTab" class="result-content active">
                                <div class="result-summary">
                                    <div class="summary-card">
                                        <div class="summary-icon">
                                            <i class="fas fa-database"></i>
                                        </div>
                                        <div class="summary-info">
                                            <div class="summary-label">测试数据总量</div>
                                            <div class="summary-value" id="totalDataCount">0</div>
                                        </div>
                                    </div>
                                    <div class="summary-card success">
                                        <div class="summary-icon">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="summary-info">
                                            <div class="summary-label">通过率</div>
                                            <div class="summary-value" id="passRate">0%</div>
                                        </div>
                                    </div>
                                    <div class="summary-card info">
                                        <div class="summary-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="summary-info">
                                            <div class="summary-label">平均偏差</div>
                                            <div class="summary-value" id="avgDeviation">0</div>
                                        </div>
                                    </div>
                                    <div class="summary-card warning">
                                        <div class="summary-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="summary-info">
                                            <div class="summary-label">最大偏差</div>
                                            <div class="summary-value" id="maxDeviation">0</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="chartsTab" class="result-content">
                                <div class="chart-container">
                                    <canvas id="confidenceChart"></canvas>
                                </div>
                                <div class="chart-container">
                                    <canvas id="deviationChart"></canvas>
                                </div>
                            </div>

                            <div id="detailsTab" class="result-content">
                                <div class="result-table-container">
                                    <table class="result-table">
                                        <thead>
                                            <tr>
                                                <th>序号</th>
                                                <th>输入值</th>
                                                <th>算法输出</th>
                                                <th>置信区间</th>
                                                <th>偏差</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody id="resultTableBody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('testAlgorithmModal')">关闭</button>
                    <button class="btn btn-primary" id="retestBtn" style="display: none;">重新测试</button>
                    <button class="btn btn-success" id="publishBtn" style="display: none;">保存并提交审核</button>
                </div>
            </div>
        </div>

        <!-- 算法详情弹窗 -->
        <div class="modal-overlay" id="algorithmDetailModal">
            <div class="modal modal-lg">
                <div class="modal-header">
                    <div>
                        <h2 class="modal-title">算法详情</h2>
                        <p class="modal-subtitle">查看算法的基本信息和配置</p>
                    </div>
                    <button class="modal-close" onclick="closeModal('algorithmDetailModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon"><i class="fas fa-info-circle"></i></div>
                            <span>基本信息</span>
                        </div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">算法名称</div>
                                <div class="info-value">电压稳定性指标计算算法</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">所属领域</div>
                                <div class="info-value">电力系统</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">算法版本</div>
                                <div class="info-value">v1.2.3</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">创建时间</div>
                                <div class="info-value">2025-03-15</div>
                            </div>
                            <div class="info-item wide">
                                <div class="info-label">算法描述</div>
                                <div class="info-value">基于电压裕度和暂态稳定性的综合指标计算，通过分析电压变化趋势和系统响应特性，提供电力系统稳定性的量化评估指标。</div>
                            </div>
                            <div class="info-item wide">
                                <div class="info-label">算法标签</div>
                                <div class="info-value tag-list">
                                    <span class="info-tag">电压稳定性</span>
                                    <span class="info-tag">电力系统</span>
                                    <span class="info-tag">裕度分析</span>
                                    <span class="info-tag">暂态稳定</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon"><i class="fas fa-sliders-h"></i></div>
                            <span>参数配置</span>
                        </div>
                        <table class="param-table">
                            <thead>
                                <tr>
                                    <th>参数名称</th>
                                    <th>数据类型</th>
                                    <th>默认值</th>
                                    <th>取值范围</th>
                                    <th>描述</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>threshold</td>
                                    <td>float</td>
                                    <td>0.75</td>
                                    <td>0.0 - 1.0</td>
                                    <td>稳定性评估阈值，低于此值视为不稳定</td>
                                </tr>
                                <tr>
                                    <td>window_size</td>
                                    <td>integer</td>
                                    <td>10</td>
                                    <td>5 - 20</td>
                                    <td>滑动窗口大小，用于计算移动平均</td>
                                </tr>
                                <tr>
                                    <td>weight_static</td>
                                    <td>float</td>
                                    <td>0.6</td>
                                    <td>0.0 - 1.0</td>
                                    <td>静态稳定性评估权重</td>
                                </tr>
                                <tr>
                                    <td>weight_dynamic</td>
                                    <td>float</td>
                                    <td>0.4</td>
                                    <td>0.0 - 1.0</td>
                                    <td>动态稳定性评估权重</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="form-section">
                        <div class="section-title">
                            <div class="section-icon"><i class="fas fa-server"></i></div>
                            <span>执行环境要求</span>
                        </div>
                        <div class="env-requirements">
                            <div class="requirement-item">
                                <div class="requirement-icon"><i class="fab fa-python"></i></div>
                                <div class="requirement-info">
                                    <div class="requirement-name">Python 版本</div>
                                    <div class="requirement-value">3.8 或更高版本</div>
                                </div>
                            </div>
                            <div class="requirement-item">
                                <div class="requirement-icon"><i class="fas fa-memory"></i></div>
                                <div class="requirement-info">
                                    <div class="requirement-name">内存需求</div>
                                    <div class="requirement-value">最低 4GB，推荐 8GB</div>
                                </div>
                            </div>
                            <div class="requirement-item">
                                <div class="requirement-icon"><i class="fas fa-microchip"></i></div>
                                <div class="requirement-info">
                                    <div class="requirement-name">CPU 需求</div>
                                    <div class="requirement-value">4核 2.0GHz 或更高</div>
                                </div>
                            </div>
                            <div class="requirement-item">
                                <div class="requirement-icon"><i class="fas fa-hdd"></i></div>
                                <div class="requirement-info">
                                    <div class="requirement-name">存储需求</div>
                                    <div class="requirement-value">至少 500MB 可用空间</div>
                                </div>
                            </div>
                            <div class="requirement-item">
                                <div class="requirement-icon"><i class="fas fa-cubes"></i></div>
                                <div class="requirement-info">
                                    <div class="requirement-name">依赖库</div>
                                    <div class="requirement-value">numpy, pandas, scipy, matplotlib</div>
                                </div>
                            </div>
                            <div class="requirement-item">
                                <div class="requirement-icon"><i class="fas fa-clock"></i></div>
                                <div class="requirement-info">
                                    <div class="requirement-name">平均运行时间</div>
                                    <div class="requirement-value">5-10秒（标准数据集）</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('algorithmDetailModal')">关闭</button>
                </div>
            </div>
        </div>

        <script>
            // 高级筛选切换
            const advancedFilterToggle = document.querySelector('.advanced-filter-toggle');
            const advancedFilterRow = document.querySelector('.advanced-filter-row');
            const filterActions = document.querySelector('.filter-actions');

            advancedFilterRow.style.display = 'none';
            filterActions.style.display = 'none';

            advancedFilterToggle.addEventListener('click', function () {
                if (advancedFilterRow.style.display === 'none') {
                    advancedFilterRow.style.display = 'grid';
                    filterActions.style.display = 'flex';
                    this.innerHTML = '<i class="fas fa-chevron-up"></i> 收起筛选';
                } else {
                    advancedFilterRow.style.display = 'none';
                    filterActions.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-sliders-h"></i> 高级筛选';
                }
            });

            // 全选功能
            const selectAllCheckbox = document.querySelector('#select-all');
            const checkboxes = document.querySelectorAll('.algorithm-table tbody .custom-checkbox input');

            selectAllCheckbox.addEventListener('change', function () {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // 收藏功能
            const favoriteButtons = document.querySelectorAll('.favorite-action');

            favoriteButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const starIcon = this.querySelector('.star-icon');
                    if (starIcon.classList.contains('star-active')) {
                        starIcon.classList.remove('star-active');
                        this.classList.remove('active');
                    } else {
                        starIcon.classList.add('star-active');
                        this.classList.add('active');
                    }
                });
            });

            // 表格操作按钮交互
            const actionButtons = document.querySelectorAll('.algorithm-action');

            actionButtons.forEach(button => {
                if (!button.hasAttribute('onclick')) {
                    button.addEventListener('click', function () {
                        const action = this.classList.contains('view') ? '查看' :
                            this.classList.contains('edit') ? '编辑' :
                                this.classList.contains('test') ? '测试' : '删除';

                        const algorithmName = this.closest('tr').querySelector('.algorithm-name').textContent;

                        if (action === '删除') {
                            if (confirm(`确定要删除算法"${algorithmName}"吗？此操作不可撤销。`)) {
                                alert(`算法"${algorithmName}"已删除`);
                            }
                        } else if (action !== '编辑' && action !== '测试') {
                            alert(`正在${action}算法: ${algorithmName}`);
                        }
                    });
                }
            });

            // 分页功能
            const pageButtons = document.querySelectorAll('.pagination .page-btn');

            pageButtons.forEach(button => {
                if (!button.classList.contains('disabled') && !button.classList.contains('active')) {
                    button.addEventListener('click', function () {
                        pageButtons.forEach(btn => btn.classList.remove('active'));
                        this.classList.add('active');

                        if (this.textContent.trim() !== '...') {
                            alert(`切换到第 ${this.textContent} 页`);
                        }
                    });
                }
            });

            // 打开/关闭模态窗口
            function openModal(modalId) {
                document.getElementById(modalId).style.display = 'block';
                document.body.style.overflow = 'hidden';
            }

            function closeModal(modalId) {
                document.getElementById(modalId).style.display = 'none';
                document.body.style.overflow = 'auto';
            }

            // 上传算法弹窗
            function openUploadAlgorithmModal() {
                openModal('uploadAlgorithmModal');
            }

            // 测试算法弹窗
            function openTestAlgorithmModal() {
                // 重置测试结果区域
                document.getElementById('testResultSection').style.display = 'none';
                // 移除对不存在元素的引用
                document.getElementById('retestBtn').style.display = 'none';
                document.getElementById('publishBtn').style.display = 'none'; // 确保初始状态时按钮不显示

                openModal('testAlgorithmModal');
            }

            // 导入算法弹窗
            function openImportModal() {
                alert('打开导入算法弹窗');
            }

            // 导出算法弹窗
            function openExportModal() {
                alert('打开导出算法弹窗');
            }

            // 算法详情弹窗
            function openAlgorithmDetailModal() {
                openModal('algorithmDetailModal');
            }

            // 测试结果标签页切换
            function switchResultTab(tabId) {
                const tabs = document.querySelectorAll('.result-tab');
                const contents = document.querySelectorAll('.result-content');

                tabs.forEach(tab => tab.classList.remove('active'));
                contents.forEach(content => content.classList.remove('active'));

                document.querySelector(`.result-tab[onclick="switchResultTab('${tabId}')"]`).classList.add('active');
                document.getElementById(`${tabId}Tab`).classList.add('active');

                // 如果切换到图表标签，初始化图表
                if (tabId === 'charts' && testResults.length > 0) {
                    initCharts();
                }
            }

            // 全局变量存储测试结果
            let testResults = [];

            // 运行算法测试
            function runAlgorithmTest() {
                // 显示测试结果区域
                const resultSection = document.getElementById('testResultSection');
                resultSection.style.display = 'block';
                
                // 显示底部按钮，但不包括发布按钮
                document.getElementById('retestBtn').style.display = 'inline-block';
                // 先隐藏发布按钮，在测试通过后才显示
                document.getElementById('publishBtn').style.display = 'none';

                // 获取测试参数
                const threshold = parseFloat(document.querySelector('input[value="0.75"]').value);
                const windowSize = parseInt(document.querySelector('input[value="10"]').value);
                const weightStatic = parseFloat(document.querySelector('input[value="0.6"]').value);
                const weightDynamic = parseFloat(document.querySelector('input[value="0.4"]').value);
                const targetPassRate = parseFloat(document.getElementById('passRateInput').value);

                // 隐藏测试结果状态
                document.getElementById('testResultStatus').style.display = 'none';
                document.getElementById('statusSuccess').style.display = 'none';
                document.getElementById('statusFail').style.display = 'none';

                // 模拟测试数据
                const testData = generateTestData(100); // 生成100条测试数据
                let processedCount = 0;
                let successCount = 0;
                let errorCount = 0;
                let totalDeviation = 0;
                let maxDeviation = 0;
                testResults = []; // 重置测试结果

                // 模拟测试过程
                const testInterval = setInterval(() => {
                    if (processedCount >= testData.length) {
                        clearInterval(testInterval);
                        finalizeTest();
                        return;
                    }

                    // 处理当前数据点
                    const data = testData[processedCount];
                    const result = processDataPoint(data, threshold, windowSize, weightStatic, weightDynamic);
                    
                    // 更新统计信息，只有成功和失败两种状态
                    if (result.status === 'success') successCount++;
                    else errorCount++;

                    totalDeviation += Math.abs(result.deviation);
                    maxDeviation = Math.max(maxDeviation, Math.abs(result.deviation));
                    
                    testResults.push(result);
                    
                    // 更新UI
                    updateProgress(processedCount + 1, testData.length);
                    updateStatusCounts(successCount, 0, errorCount); // 传递0作为warning数量
                    updateResultTable(testResults);
                    
                    processedCount++;
                }, 50);

                function finalizeTest() {
                    // 计算最终统计信息
                    const passRate = (successCount / testData.length * 100).toFixed(2);
                    const avgDeviation = (totalDeviation / testData.length).toFixed(4);
                    
                    // 更新概览信息
                    document.getElementById('totalDataCount').textContent = testData.length;
                    document.getElementById('passRate').textContent = passRate + '%';
                    document.getElementById('avgDeviation').textContent = avgDeviation;
                    document.getElementById('maxDeviation').textContent = maxDeviation.toFixed(4);
                    
                    // 初始化图表
                    initCharts();
                    
                    // 显示测试结果状态（通过/不通过）
                    const resultStatus = document.getElementById('testResultStatus');
                    const statusSuccess = document.getElementById('statusSuccess');
                    const statusFail = document.getElementById('statusFail');
                    const isPassed = parseFloat(passRate) >= targetPassRate;
                    
                    resultStatus.style.display = 'flex';
                    statusSuccess.style.display = isPassed ? 'flex' : 'none';
                    statusFail.style.display = isPassed ? 'none' : 'flex';
                    
                    // 设置背景色
                    resultStatus.style.backgroundColor = isPassed ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)';
                    resultStatus.style.border = isPassed ? '1px solid rgba(40, 167, 69, 0.2)' : '1px solid rgba(220, 53, 69, 0.2)';
                    
                    // 更新发布按钮状态
                    const publishBtn = document.getElementById('publishBtn');
                    if (isPassed) {
                        publishBtn.style.display = 'inline-block'; // 只有测试通过才显示按钮
                        publishBtn.disabled = false;
                        publishBtn.classList.add('btn-success');
                        publishBtn.classList.remove('btn-disabled');
                    } else {
                        publishBtn.style.display = 'none'; // 测试不通过不显示按钮
                    }
                }
            }

            // 生成测试数据
            function generateTestData(count) {
                const data = [];
                for (let i = 0; i < count; i++) {
                    // 生成更接近的数据，大幅提高通过率
                    const baseValue = Math.random() * 100;
                    // 减小输入值与期望值的差异范围，从±10减小到±5
                    const input = baseValue + (Math.random() - 0.5) * 10; // 输入值在基准值±5范围内
                    const expected = baseValue + (Math.random() - 0.5) * 10; // 期望值也在基准值±5范围内
                    data.push({
                        input,
                        expected,
                        timestamp: new Date().getTime() + i * 1000
                    });
                }
                return data;
            }

            // 处理单个数据点
            function processDataPoint(data, threshold, windowSize, weightStatic, weightDynamic) {
                // 修改算法计算，使输出更接近期望值
                // 调整权重比例，使输出值更偏向期望值
                const output = data.input * weightStatic * 0.8 + data.expected * weightDynamic * 1.2;
                // 进一步扩大偏差容忍度
                const deviation = output - data.expected;
                const confidenceInterval = [output - threshold * 3, output + threshold * 3]; // 扩大置信区间
                
                // 提高通过率的判断标准
                let status;
                if (Math.abs(deviation) <= threshold * 3) { // 扩大阈值，从2倍扩大到3倍
                    status = 'success';
                } else {
                    status = 'error';
                }
                
                // 强制性保证通过率，如果随机数大于0.2则强制判定为成功
                if (Math.random() > 0.2) {
                    status = 'success';
                }
                
                return {
                    input: data.input,
                    output,
                    confidenceInterval,
                    deviation,
                    status
                };
            }

            // 更新进度条
            function updateProgress(current, total) {
                const progress = (current / total * 100).toFixed(1);
                document.getElementById('testProgress').style.width = progress + '%';
                document.getElementById('progressText').textContent = progress + '%';
            }

            // 更新状态计数
            function updateStatusCounts(success, warning, error) {
                document.getElementById('successCount').textContent = success;
                // 隐藏warning计数
                document.getElementById('warningCount').textContent = '0';
                document.getElementById('errorCount').textContent = error;
            }

            // 更新结果表格
            function updateResultTable(results) {
                const tbody = document.getElementById('resultTableBody');
                tbody.innerHTML = '';
                
                results.forEach((result, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${result.input.toFixed(4)}</td>
                        <td>${result.output.toFixed(4)}</td>
                        <td>[${result.confidenceInterval[0].toFixed(4)}, ${result.confidenceInterval[1].toFixed(4)}]</td>
                        <td>${result.deviation.toFixed(4)}</td>
                        <td><span class="status-badge ${result.status}">${result.status === 'success' ? '通过' : '失败'}</span></td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // 全局变量存储图表实例
            let confidenceChart = null;
            let deviationChart = null;

            // 初始化图表
            function initCharts() {
                if (!testResults || testResults.length === 0) {
                    console.warn('没有可用的测试结果数据');
                    return;
                }

                // 销毁旧的图表实例
                if (confidenceChart) {
                    confidenceChart.destroy();
                }
                if (deviationChart) {
                    deviationChart.destroy();
                }

                // 置信区间图表
                const confidenceCtx = document.getElementById('confidenceChart').getContext('2d');
                confidenceChart = new Chart(confidenceCtx, {
                    type: 'line',
                    data: {
                        labels: testResults.map((_, i) => i + 1),
                        datasets: [{
                            label: '算法输出',
                            data: testResults.map(r => r.output),
                            borderColor: 'rgb(75, 192, 192)',
                            tension: 0.1
                        }, {
                            label: '置信区间上限',
                            data: testResults.map(r => r.confidenceInterval[1]),
                            borderColor: 'rgb(255, 99, 132)',
                            borderDash: [5, 5],
                            fill: false
                        }, {
                            label: '置信区间下限',
                            data: testResults.map(r => r.confidenceInterval[0]),
                            borderColor: 'rgb(255, 99, 132)',
                            borderDash: [5, 5],
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '算法输出与置信区间'
                            }
                        }
                    }
                });

                // 偏差图表
                const deviationCtx = document.getElementById('deviationChart').getContext('2d');
                deviationChart = new Chart(deviationCtx, {
                    type: 'bar',
                    data: {
                        labels: testResults.map((_, i) => i + 1),
                        datasets: [{
                            label: '偏差',
                            data: testResults.map(r => r.deviation),
                            backgroundColor: testResults.map(r => 
                                r.status === 'success' ? 'rgba(75, 192, 192, 0.5)' : 'rgba(255, 99, 132, 0.5)'
                            )
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '算法输出偏差'
                            }
                        }
                    }
                });
            }

            // 添加输入字段映射
            function addInputMapping() {
                const inputMappings = document.getElementById('inputMappings');
                const newMapping = document.createElement('div');
                newMapping.className = 'mapping-row';
                newMapping.innerHTML = `
                    <div class="mapping-col">
                        <select class="modal-select">
                            <option value="">选择算法参数</option>
                            <option value="threshold">threshold</option>
                            <option value="window_size">window_size</option>
                            <option value="weight_static">weight_static</option>
                            <option value="weight_dynamic">weight_dynamic</option>
                        </select>
                    </div>
                    <div class="mapping-col">
                        <select class="modal-select">
                            <option value="">选择测试数据字段</option>
                            <option value="voltage">电压数据</option>
                            <option value="current">电流数据</option>
                            <option value="power">功率数据</option>
                            <option value="frequency">频率数据</option>
                        </select>
                    </div>
                    <div class="mapping-col">
                        <button class="btn btn-icon" onclick="removeMapping(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                inputMappings.appendChild(newMapping);
            }

            // 移除字段映射
            function removeMapping(button) {
                const mappingRow = button.closest('.mapping-row');
                mappingRow.remove();
            }

            // 关闭窗口的事件监听
            window.addEventListener('click', function (event) {
                const modals = document.querySelectorAll('.modal-overlay');
                modals.forEach(modal => {
                    if (event.target === modal) {
                        modal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }
                });
            });

            // 阻止模态内容点击事件冒泡
            const modalContents = document.querySelectorAll('.modal');
            modalContents.forEach(content => {
                content.addEventListener('click', function (event) {
                    event.stopPropagation();
                });
            });

            // 主题切换功能初始化
            document.addEventListener('DOMContentLoaded', function() {
                // 主题切换功能初始化
                const themeSwitch = document.querySelector('.theme-switch');
                const htmlRoot = document.documentElement;
                
                // 检查本地存储中的主题设置，默认为暗色主题
                const savedTheme = localStorage.getItem('theme');
                // 只有当明确保存了"light"时才应用亮色主题
                if (savedTheme === 'light') {
                    htmlRoot.classList.add('light-theme');
                } else {
                    // 如果没有保存或者保存的不是"light"，则确保是暗色主题
                    localStorage.setItem('theme', 'dark');
                }
                
                // 切换主题的函数
                function toggleTheme() {
                    if (htmlRoot.classList.contains('light-theme')) {
                        htmlRoot.classList.remove('light-theme');
                        localStorage.setItem('theme', 'dark');
                    } else {
                        htmlRoot.classList.add('light-theme');
                        localStorage.setItem('theme', 'light');
                    }
                    
                    // 如果有图表，主题切换后需要重新渲染
                    if (typeof echarts !== 'undefined') {
                        // 延迟一点执行，等待CSS变化应用
                        setTimeout(() => {
                            // 找到所有echarts实例并重新调整大小
                            const chartElements = document.querySelectorAll('[id$="-chart"]');
                            chartElements.forEach(element => {
                                const chart = echarts.getInstanceByDom(element);
                                if (chart) {
                                    chart.resize();
                                }
                            });
                        }, 200);
                    }
                }
                
                // 为主题切换按钮添加点击事件
                themeSwitch.addEventListener('click', toggleTheme);
                
                // 模拟通知弹出
                document.querySelector('.notification-icon').addEventListener('click', function() {
                    alert('通知中心功能将在此处展开');
                });

                // 用户资料弹出
                document.querySelector('.user-profile').addEventListener('click', function() {
                    alert('用户个人资料功能将在此处展开');
                });

                // 分页功能
                const pageButtons = document.querySelectorAll('.pagination .page-btn');

                pageButtons.forEach(button => {
                    if (!button.classList.contains('disabled') && !button.classList.contains('active')) {
                        button.addEventListener('click', function () {
                            pageButtons.forEach(btn => btn.classList.remove('active'));
                            this.classList.add('active');

                            if (this.textContent.trim() !== '...') {
                                alert(`切换到第 ${this.textContent} 页`);
                            }
                        });
                    }
                });
            });
        </script>
</body>

</html>