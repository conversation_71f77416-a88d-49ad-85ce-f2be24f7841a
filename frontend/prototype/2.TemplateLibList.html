<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模版库列表 - 城市级关基级联的网络仿真度评估系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Echarts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入中国地图数据 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/map/js/china.js"></script>
    <style>
        /* 页面整体放大120% */
        html {
            zoom: 110%;
        }
        
        :root {
            --primary-color: #0096FF;
            --secondary-color: #00E0FF;
            --background-dark: #0F1520;
            --background-card: #1A202E;
            --text-color: #E0E6F0;
            --text-secondary: #A0A8B8;
            --border-color: #2A3142;
            --highlight-color: #3E9BFF;
            --success-color: #00C48C;
            --warning-color: #FFB946;
            --danger-color: #F25767;
            --info-color: #0095FF;
            --add-color: #27ae60;
            --remove-color: #e74c3c;
            --modify-color: #f39c12;
        }
        
        /* 亮色主题变量 */
        :root.light-theme {
            --primary-color: #0078D4;
            --secondary-color: #00A2E0;
            --background-dark: #FFFFFF;
            --background-card: #FFFFFF;
            --text-color: #333333;
            --text-secondary: #666666;
            --border-color: #E0E6ED;
            --highlight-color: #106EBE;
            --success-color: #0A9D5A;
            --warning-color: #F29D41;
            --danger-color: #E74C3C;
            --info-color: #0078D4;
            --add-color: #27ae60;
            --remove-color: #e74c3c;
            --modify-color: #f39c12;
        }
        
        /* 主题切换开关样式 */
        .theme-switch {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 60px;
            height: 30px;
            background: rgba(0, 149, 255, 0.15);
            border-radius: 30px;
            padding: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .theme-switch:hover {
            transform: scale(1.05);
        }

        .theme-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-size: 14px;
        }

        .theme-switch-slider {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            left: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        :root.light-theme .theme-switch-slider {
            transform: translateX(30px);
        }
        
        /* Logo样式 */
        .logo {
	zoom: 110%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 10px;
        }

        .logo-icon {
            font-size: 24px;
            color: var(--primary-color);
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 2px rgba(0, 224, 255, 0.4));
        }

        .logo span {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 0.5px;
            background: linear-gradient(90deg, var(--text-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: var(--background-dark);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            min-height: 100vh;
        }

        /* 顶部导航栏样式 */
        .header {
            background-color: rgba(16, 22, 36, 0.95);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 60px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .nav-item {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .nav-link {
            color: var(--text-color);
            text-decoration: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link.active {
            color: var(--primary-color);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 0 0 6px 6px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            border-bottom: 1px solid var(--border-color);
        }
		
		.dropdown-menu {
			list-style-type: none; /* 去掉前面的点 */
		}

        .dropdown-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px;
        }

        .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            padding-left: 20px;
        }

        .dropdown-icon {
            margin-right: 8px;
        }

        .nav-tools {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-icon, .user-profile {
            position: relative;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .notification-icon:hover, .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-profile img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 24px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
        }

        .page-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .template-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: var(--highlight-color);
        }

        /* 搜索和筛选区域 */
        .filter-section {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
        }

        .search-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .search-input-wrapper {
            position: relative;
            flex: 1;
        }

        .search-input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.08);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .advanced-filter-toggle {
            white-space: nowrap;
            color: var(--primary-color);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 0 10px;
        }

        .advanced-filter-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .filter-select, .filter-input {
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
        }
        
        /* 统计卡片区域 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 5px 0 0 5px;
        }

        .stat-card.success::before {
            background-color: var(--success-color);
        }

        .stat-card.warning::before {
            background-color: var(--warning-color);
        }

        .stat-card.danger::before {
            background-color: var(--danger-color);
        }

        .stat-card.info::before {
            background-color: var(--info-color);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
        }

        .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-description {
            font-size: 13px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-trend-up {
            color: var(--success-color);
        }

        .stat-trend-down {
            color: var(--danger-color);
        }

        /* 模版列表表格 */
        .templates-section {
            margin-bottom: 24px;
        }

        .table-responsive {
            background-color: var(--background-card);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .table-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .table-title {
            font-size: 16px;
            font-weight: 500;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .bulk-select {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-right: 15px;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
        }

        .bulk-action-btn {
            padding: 6px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .bulk-action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .bulk-action-btn.delete {
            color: var(--danger-color);
            border-color: rgba(242, 87, 103, 0.3);
        }

        .bulk-action-btn.delete:hover {
            background-color: rgba(242, 87, 103, 0.1);
            border-color: var(--danger-color);
        }

        .template-table {
            width: 100%;
            border-collapse: collapse;
        }

        .template-table th, .template-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .template-table th {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 13px;
            background-color: rgba(255, 255, 255, 0.02);
        }

        .template-table tbody tr {
            transition: all 0.2s ease;
        }

        .template-table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .template-table tbody tr:last-child td {
            border-bottom: none;
        }

        .checkbox-cell {
            width: 30px;
        }

        .template-id {
            font-family: monospace;
            color: var(--text-secondary);
        }

        .template-name {
            font-weight: 500;
        }

        .template-desc {
            color: var(--text-secondary);
            font-size: 13px;
            max-width: 250px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .template-status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .status-draft {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .status-review {
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--info-color);
        }

        .template-actions-cell {
            text-align: right;
            white-space: nowrap;
        }

        .template-action {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 5px;
        }

        .template-action:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .template-action.edit:hover {
            color: var(--info-color);
            border-color: var(--info-color);
            background-color: rgba(0, 149, 255, 0.1);
        }

        .template-action.copy:hover {
            color: var(--warning-color);
            border-color: var(--warning-color);
            background-color: rgba(255, 185, 70, 0.1);
        }

        .template-action.delete:hover {
            color: var(--danger-color);
            border-color: var(--danger-color);
            background-color: rgba(242, 87, 103, 0.1);
        }

        .favorite-action {
            color: var(--text-secondary);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .favorite-action:hover, .favorite-action.active {
            color: var(--warning-color);
        }

        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
        }

        .page-info {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .pagination {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .page-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .page-btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .page-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 标记/收藏样式 */
        .star-icon {
            transition: all 0.3s ease;
        }

        .star-active {
            color: var(--warning-color);
        }

        /* 自定义复选框 */
        .custom-checkbox {
            position: relative;
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 5px;
            vertical-align: middle;
        }

        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 18px;
            width: 18px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .custom-checkbox input:checked ~ .checkmark {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 2px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input:checked ~ .checkmark:after {
            display: block;
        }

        /* 最近活动卡片 */
        .recent-activity-card {
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .card-header {
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .card-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .card-action:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
        }

        .card-body {
            padding: 20px;
        }

        .activity-timeline {
            position: relative;
            padding-left: 24px;
            max-height: 350px;
            overflow-y: auto;
        }

        .activity-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 8px;
            height: 100%;
            width: 2px;
            background-color: var(--border-color);
        }

        .activity-item {
            position: relative;
            padding-bottom: 20px;
        }

        .activity-item:last-child {
            padding-bottom: 0;
        }

        .activity-dot {
            position: absolute;
            left: -24px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 3px solid var(--background-card);
            z-index: 1;
        }

        .activity-item.success .activity-dot {
            background-color: var(--success-color);
        }

        .activity-item.warning .activity-dot {
            background-color: var(--warning-color);
        }

        .activity-item.danger .activity-dot {
            background-color: var(--danger-color);
        }

        .activity-content {
            padding-bottom: 12px;
            border-bottom: 1px dashed var(--border-color);
        }

        .activity-item:last-child .activity-content {
            border-bottom: none;
        }

        .activity-title {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .activity-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 模态窗口通用样式 */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 2000;
            animation: fadeIn 0.3s ease;
        }

        .modal {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid var(--border-color);
            overflow: hidden;
            max-width: 90%;
            width: 900px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            animation: slideIn 0.3s ease;
        }

        .modal-lg {
            width: 1100px;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 500;
        }

        .modal-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 22px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            color: var(--text-color);
        }

        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .modal-form-group {
            margin-bottom: 20px;
        }

        .modal-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .modal-sublabel {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .modal-input, .modal-select, .modal-textarea {
            width: 100%;
            padding: 10px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .modal-input:focus, .modal-select:focus, .modal-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.08);
        }

        .modal-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .modal-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .modal-checkbox input {
            margin-right: 8px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: var(--background-card);
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--highlight-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: rgba(0, 196, 140, 0.8);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: rgba(242, 87, 103, 0.8);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: var(--border-color);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }

        /* 新建/编辑模版弹窗特定样式 */
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-col {
            flex: 1;
        }

        .form-section {
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px dashed var(--border-color);
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(62, 155, 255, 0.1);
            border-radius: 6px;
            color: var(--primary-color);
        }

        .tag-input-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            min-height: 42px;
        }

        .tag {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            background-color: rgba(62, 155, 255, 0.1);
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid rgba(62, 155, 255, 0.2);
        }

        .tag-close {
            cursor: pointer;
        }

        .tag-input {
            flex: 1;
            min-width: 100px;
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 14px;
            outline: none;
        }

        .tree-editor {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .tree-toolbar {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.02);
            border-bottom: 1px solid var(--border-color);
        }

        .tree-actions {
            display: flex;
            gap: 8px;
        }

        .tree-container {
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
        }

        .tree-node {
            margin-bottom: 8px;
        }

        .node-content {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 10px;
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            position: relative;
        }

        .node-content:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .node-collapse {
            cursor: pointer;
            width: 20px;
            display: flex;
            justify-content: center;
        }

        .node-icon {
            color: var(--primary-color);
            font-size: 14px;
        }

        .node-title {
            flex: 1;
        }

        .node-actions {
            display: flex;
            gap: 5px;
        }

        .node-action {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 12px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .node-action:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-color);
        }

        .node-weight {
            margin-left: 10px;
            font-size: 12px;
            color: var(--text-secondary);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 10px;
        }

        .node-children {
            margin-left: 25px;
            padding-left: 15px;
            border-left: 1px dashed var(--border-color);
            margin-top: 8px;
        }

        .file-upload-container {
            border: 2px dashed var(--border-color);
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .file-upload-container:hover {
            border-color: var(--primary-color);
            background-color: rgba(62, 155, 255, 0.05);
        }

        .upload-icon {
            font-size: 32px;
            color: var(--text-secondary);
            margin-bottom: 10px;
        }

        .upload-text {
            margin-bottom: 15px;
        }

        .upload-browse {
            color: var(--primary-color);
            cursor: pointer;
        }

        .upload-file-list {
            max-height: 150px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .upload-file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-icon {
            font-size: 18px;
            color: var(--primary-color);
        }

        .file-name {
            font-size: 14px;
        }

        .file-size {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .file-progress {
            width: 100px;
            height: 4px;
            background-color: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }

        .file-progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 2px;
        }

        .file-actions {
            display: flex;
            gap: 5px;
        }

        .file-action {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .file-action:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-color);
        }

        .file-action.delete:hover {
            color: var(--danger-color);
        }

        .nav-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .nav-tab {
            padding: 10px 16px;
            font-size: 14px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .nav-tab:hover {
            color: var(--text-color);
        }

        .nav-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .algorithm-select-container {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 6px;
        }

        .algorithm-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .algorithm-item:last-child {
            border-bottom: none;
        }

        .algorithm-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .algorithm-item.selected {
            background-color: rgba(62, 155, 255, 0.1);
            border-left: 3px solid var(--primary-color);
        }

        .algorithm-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(62, 155, 255, 0.1);
            border-radius: 6px;
            margin-right: 10px;
            color: var(--primary-color);
        }

        .algorithm-info {
            flex: 1;
        }

        .algorithm-name {
            font-size: 14px;
            font-weight: 500;
        }

        .algorithm-desc {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .weight-slider {
            width: 100%;
            height: 4px;
            background-color: var(--border-color);
            border-radius: 2px;
            position: relative;
            margin: 5px 0;
        }

        .weight-slider-thumb {
            width: 16px;
            height: 16px;
            background-color: var(--primary-color);
            border-radius: 50%;
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .weight-slider-thumb:hover {
            box-shadow: 0 0 0 5px rgba(62, 155, 255, 0.2);
        }

        .weight-slider-value {
            margin-top: 5px;
            font-size: 12px;
            color: var(--text-secondary);
            text-align: center;
        }

        /* 模版版本对比弹窗特定样式 */
        .version-selector {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .version-selector-col {
            flex: 1;
        }

        .version-col-title {
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .version-swap {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            margin-top: 24px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .version-swap:hover {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .diff-controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .view-mode {
            display: flex;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .view-mode-btn {
            padding: 6px 12px;
            font-size: 13px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .view-mode-btn.active {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
        }

        .filter-diff {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .diff-container {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .diff-split {
            display: flex;
        }

        .diff-panel {
            flex: 1;
            border-right: 1px solid var(--border-color);
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .diff-panel:last-child {
            border-right: none;
        }

        .diff-header {
            padding: 10px 15px;
            background-color: rgba(255, 255, 255, 0.02);
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
            font-size: 14px;
        }

        .diff-file {
            margin-bottom: 15px;
        }

        .diff-file-header {
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--border-color);
            border-radius: 4px 4px 0 0;
            font-size: 13px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .diff-file-body {
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 4px 4px;
        }

        .diff-line {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .diff-line:last-child {
            border-bottom: none;
        }

        .diff-line-num {
            width: 40px;
            text-align: right;
            padding: 2px 10px;
            color: var(--text-secondary);
            background-color: rgba(255, 255, 255, 0.02);
            border-right: 1px solid var(--border-color);
            user-select: none;
        }

        .diff-line-content {
            flex: 1;
            padding: 2px 10px;
            white-space: pre;
            overflow-x: auto;
        }

        .diff-added {
            background-color: rgba(39, 174, 96, 0.1);
            border-left: 3px solid var(--add-color);
        }

        .diff-removed {
            background-color: rgba(231, 76, 60, 0.1);
            border-left: 3px solid var(--remove-color);
        }

        .diff-modified {
            background-color: rgba(243, 156, 18, 0.1);
            border-left: 3px solid var(--modify-color);
        }

        .diff-add-text {
            color: var(--add-color);
            background-color: rgba(39, 174, 96, 0.1);
        }

        .diff-remove-text {
            color: var(--remove-color);
            background-color: rgba(231, 76, 60, 0.1);
            text-decoration: line-through;
        }

        .legend {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 3px;
        }

        .legend-add {
            background-color: var(--add-color);
        }

        .legend-remove {
            background-color: var(--remove-color);
        }

        .legend-modify {
            background-color: var(--modify-color);
        }

        /* 导入/导出模版弹窗特定样式 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .import-sources {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .import-source {
            flex: 1;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .import-source:hover {
            background-color: rgba(62, 155, 255, 0.05);
            border-color: var(--primary-color);
        }

        .import-source.active {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .import-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--text-secondary);
        }

        .import-source.active .import-icon {
            color: var(--primary-color);
        }

        .import-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .import-desc {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .import-options {
            margin-bottom: 20px;
        }

        .option-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .option-label {
            width: 150px;
            font-size: 14px;
        }

        .option-input {
            flex: 1;
        }

        .preview-container {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .preview-header {
            padding: 10px 15px;
            background-color: rgba(255, 255, 255, 0.02);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-body {
            max-height: 300px;
            overflow-y: auto;
            padding: 15px;
        }

        .preview-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: var(--text-secondary);
        }

        .preview-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .validation-progress {
            margin-bottom: 20px;
        }

        .progress-bar {
            height: 8px;
            background-color: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-status {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .export-template-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .export-template-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .export-template-item:last-child {
            border-bottom: none;
        }

        .export-template-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .export-checkbox {
            margin-right: 10px;
        }

        .export-template-info {
            flex: 1;
        }

        .export-template-name {
            font-size: 14px;
            font-weight: 500;
        }

        .export-template-meta {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .export-format-options {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .export-format {
            flex: 1;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .export-format:hover {
            background-color: rgba(62, 155, 255, 0.05);
            border-color: var(--primary-color);
        }

        .export-format.active {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .export-format-icon {
            font-size: 20px;
            margin-bottom: 8px;
            color: var(--text-secondary);
        }

        .export-format.active .export-format-icon {
            color: var(--primary-color);
        }

        .export-format-name {
            font-size: 14px;
            font-weight: 500;
        }

        .export-format-desc {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .alert {
            padding: 12px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .alert-warning {
            background-color: rgba(255, 185, 70, 0.1);
            border: 1px solid rgba(255, 185, 70, 0.3);
        }

        .alert-danger {
            background-color: rgba(242, 87, 103, 0.1);
            border: 1px solid rgba(242, 87, 103, 0.3);
        }

        .alert-success {
            background-color: rgba(0, 196, 140, 0.1);
            border: 1px solid rgba(0, 196, 140, 0.3);
        }

        .alert-icon {
            font-size: 18px;
        }

        .alert-warning .alert-icon {
            color: var(--warning-color);
        }

        .alert-danger .alert-icon {
            color: var(--danger-color);
        }

        .alert-success .alert-icon {
            color: var(--success-color);
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 500;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .alert-message {
            font-size: 13px;
            color: var(--text-color);
        }

        .error-list {
            max-height: 150px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .error-item {
            padding: 8px 10px;
            border-radius: 4px;
            background-color: rgba(242, 87, 103, 0.05);
            margin-bottom: 5px;
            font-size: 12px;
            border-left: 3px solid var(--danger-color);
        }

        .warning-item {
            padding: 8px 10px;
            border-radius: 4px;
            background-color: rgba(255, 185, 70, 0.05);
            margin-bottom: 5px;
            font-size: 12px;
            border-left: 3px solid var(--warning-color);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translate(-50%, -60%); opacity: 0; }
            to { transform: translate(-50%, -50%); opacity: 1; }
        }

        /* 响应式设计 */
        @media screen and (max-width: 1200px) {
            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .advanced-filter-row {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .form-row {
                flex-direction: column;
            }
            
            .modal {
                width: 95%;
            }
            
            .diff-split {
                flex-direction: column;
            }
            
            .diff-panel {
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }
            
            .diff-panel:last-child {
                border-bottom: none;
            }
            
            .import-sources {
                flex-direction: column;
            }
        }

        @media screen and (max-width: 768px) {
            .stats-cards {
                grid-template-columns: 1fr;
            }

            .template-table {
                min-width: 800px;
            }

            .table-responsive {
                overflow-x: auto;
            }

            .advanced-filter-row {
                grid-template-columns: 1fr;
            }

            .bulk-actions {
                display: none;
            }
            
            .version-selector {
                flex-direction: column;
                gap: 10px;
            }
            
            .version-swap {
                align-self: center;
                margin: 5px 0;
                transform: rotate(90deg);
            }
            
            .export-format-options {
                flex-direction: column;
            }
        }

        @media screen and (max-width: 576px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .template-actions {
                width: 100%;
            }

            .search-row {
                flex-direction: column;
            }
            
            .diff-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        :root.light-theme .theme-switch-slider {
            transform: translateX(30px);
        }
        
        /* 亮色主题的导航栏调整 */
        :root.light-theme .header {
            background-color: rgba(245, 247, 250, 0.95);
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .nav-link:hover {
            color: var(--primary-color);
        }
        
        :root.light-theme .dropdown-menu {
            background-color: var(--background-card);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        /* 亮色主题的卡片调整 */
        :root.light-theme .recent-activity-card,
        :root.light-theme .filter-section {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .card-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .card-action:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        /* 亮色主题的统计卡片 */
        :root.light-theme .stat-card {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .stat-card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .stat-icon {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        /* 亮色主题的表格样式 */
        :root.light-theme .table-responsive {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .template-table th {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .template-table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        :root.light-theme .template-status-badge.status-active {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .template-status-badge.status-draft {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .template-status-badge.status-review {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .template-action {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        :root.light-theme .template-action:hover {
            background-color: rgba(62, 155, 255, 0.1);
        }
        
        /* 亮色主题的按钮 */
        :root.light-theme .action-btn {
            background-color: #F5F7FA;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .action-btn.primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .action-btn.primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        :root.light-theme .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .btn-primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        :root.light-theme .btn-secondary {
            background-color: #F0F2F5;
        }
        
        :root.light-theme .btn-secondary:hover {
            background-color: #E8EBF0;
        }
        
        /* 亮色主题的搜索和筛选区域 */
        :root.light-theme .search-input {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .search-input:focus {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        :root.light-theme .filter-select, 
        :root.light-theme .filter-input {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        /* 亮色主题的时间线 */
        :root.light-theme .activity-timeline::before {
            background-color: #E0E6ED;
        }
        
        :root.light-theme .activity-dot {
            border: 3px solid #FFFFFF;
        }
        
        :root.light-theme .activity-content {
            border-bottom: 1px dashed #E0E6ED;
        }
        
        /* 亮色主题的模态窗口 */
        :root.light-theme .modal-overlay {
            background-color: rgba(0, 0, 0, 0.3);
        }
        
        :root.light-theme .modal {
            background-color: var(--background-card);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        :root.light-theme .modal-input, 
        :root.light-theme .modal-select, 
        :root.light-theme .modal-textarea {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .modal-input:focus, 
        :root.light-theme .modal-select:focus, 
        :root.light-theme .modal-textarea:focus {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        :root.light-theme .tag-input-container {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .tag {
            background-color: rgba(62, 155, 255, 0.08);
        }
        
        /* 亮色主题的树形编辑器 */
        :root.light-theme .tree-editor {
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .tree-toolbar {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .node-content {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        :root.light-theme .node-content:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        :root.light-theme .node-children {
            border-left: 1px dashed #E0E6ED;
        }
        
        /* 亮色主题的文件上传 */
        :root.light-theme .file-upload-container {
            border: 2px dashed #E0E6ED;
        }
        
        :root.light-theme .file-upload-container:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .upload-file-item {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        /* 亮色主题的滚动条 */
        :root.light-theme ::-webkit-scrollbar-track {
            background: #F5F7FA;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb {
            background: #D0D6E0;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
    </style>
    
<body>
    <div class="container">

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题和操作按钮 -->
            <div class="page-header">
                
                <div class="template-actions">
                    <button class="action-btn" id="importTemplateBtn" onclick="openImportExportModal()">
                        <i class="fas fa-file-import"></i> 导入模版
                    </button>
                    <button class="action-btn" onclick="openImportExportModal('export')">
                        <i class="fas fa-file-export"></i> 导出模版
                    </button>
                    <button class="action-btn primary" id="createTemplateBtn" onclick="openCreateTemplateModal()">
                        <i class="fas fa-plus"></i> 新建模版
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-cards">
                <div class="stat-card info">
                    <div class="stat-header">
                        <div class="stat-title">评估模版总数</div>
                        <div class="stat-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                    </div>
                    <div class="stat-value">30</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长 8 个</span>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-header">
                        <div class="stat-title">已发布</div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">15</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长 5 个</span>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-header">
                        <div class="stat-title">审核中</div>
                        <div class="stat-icon">
                            <i class="fas fa-hourglass-half"></i>
                        </div>
                    </div>
                    <div class="stat-value">10</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长 1 个</span>
                    </div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-header">
                        <div class="stat-title">草稿</div>
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">5</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-down stat-trend-down"></i>
                        <span>较上月减少 3 个</span>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="filter-section">
                <div class="search-row">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索模版名称、ID、创建人...">
                    </div>
                    <button class="advanced-filter-toggle">
                        <i class="fas fa-sliders-h"></i> 高级筛选
                    </button>
                </div>
                <div class="advanced-filter-row">
                    <div class="filter-group">
                        <label class="filter-label">模版状态</label>
                        <select class="filter-select">
                            <option value="">全部状态</option>
                            <option value="active">已发布</option>
                            <option value="draft">草稿</option>
                            <option value="review">审核中</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">所属领域</label>
                        <select class="filter-select">
                            <option value="">全部领域</option>
                            <option value="power">电力系统</option>
                            <option value="transportation">交通系统</option>
                            <option value="water">水利系统</option>
                            <option value="gas">燃气系统</option>
                            <option value="communication">通信系统</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">创建时间</label>
                        <select class="filter-select">
                            <option value="">全部时间</option>
                            <option value="today">今天</option>
                            <option value="yesterday">昨天</option>
                            <option value="last7days">最近7天</option>
                            <option value="last30days">最近30天</option>
                            <option value="custom">自定义范围</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">创建人</label>
                        <select class="filter-select">
                            <option value="">全部创建人</option>
                            <option value="self">我创建的</option>
                            <option value="team">我团队的</option>
                        </select>
                    </div>
                </div>
                <div class="filter-actions">
                    <button class="action-btn">重置筛选</button>
                    <button class="action-btn primary">应用筛选</button>
                </div>
            </div>

            <!-- 模版列表 -->
            <div class="templates-section">
                <div class="table-responsive">
                    <div class="table-header">
                        <div class="table-title">全部模版</div>
                        <div class="table-actions">
                            <div class="bulk-select">
                                <label class="custom-checkbox">
                                    <input type="checkbox" id="select-all">
                                    <span class="checkmark"></span>
                                </label>
                                <span>全选</span>
                            </div>
                            <div class="bulk-actions">
                                <button class="bulk-action-btn">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                                <button class="bulk-action-btn">
                                    <i class="fas fa-archive"></i> 归档
                                </button>
                                <button class="bulk-action-btn delete">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                    <table class="template-table">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </th>
                                <th>模版名称</th>
                                <th>ID</th>
                                <th>领域</th>
                                <th>状态</th>
                                <th>创建人</th>
                                <th>创建时间</th>
                                <th>使用次数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="template-name">电力系统仿真度标准评估模版</span>
                                    <div class="template-desc">用于电力系统稳态和暂态仿真评估的标准模版</div>
                                </td>
                                <td><span class="template-id">TPL-20250401-001</span></td>
                                <td>电力系统</td>
                                <td><span class="template-status-badge status-active">已发布</span></td>
                                <td>张三</td>
                                <td>2025-03-15</td>
                                <td>124</td>
                                <td class="template-actions-cell">
                                    <button class="template-action view" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="template-action edit" title="编辑" onclick="openEditTemplateModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="template-action copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="template-action compare" title="版本对比" onclick="openCompareModal()">
                                        <i class="fas fa-code-compare"></i>
                                    </button>
                                    <button class="template-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action active">
                                        <i class="fas fa-star star-icon star-active"></i>
                                    </button>
                                    <span class="template-name">城市交通流量仿真度评估模版</span>
                                    <div class="template-desc">适用于城市交通系统流量和拥堵分析</div>
                                </td>
                                <td><span class="template-id">TPL-20250402-002</span></td>
                                <td>交通系统</td>
                                <td><span class="template-status-badge status-active">已发布</span></td>
                                <td>李三</td>
                                <td>2025-03-20</td>
                                <td>98</td>
                                <td class="template-actions-cell">
                                    <button class="template-action view" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="template-action edit" title="编辑" onclick="openEditTemplateModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="template-action copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="template-action compare" title="版本对比" onclick="openCompareModal()">
                                        <i class="fas fa-code-compare"></i>
                                    </button>
                                    <button class="template-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="template-name">供水管网压力仿真度评估模版</span>
                                    <div class="template-desc">用于城市供水系统压力和流量仿真评估</div>
                                </td>
                                <td><span class="template-id">TPL-20250403-003</span></td>
                                <td>水利系统</td>
                                <td><span class="template-status-badge status-review">审核中</span></td>
                                <td>王三</td>
                                <td>2025-03-28</td>
                                <td>42</td>
                                <td class="template-actions-cell">
                                    <button class="template-action view" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="template-action edit" title="编辑" onclick="openEditTemplateModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="template-action copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="template-action compare" title="版本对比" onclick="openCompareModal()">
                                        <i class="fas fa-code-compare"></i>
                                    </button>
                                    <button class="template-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="template-name">燃气管网安全性能仿真评估模版</span>
                                    <div class="template-desc">城市燃气系统安全与泄漏仿真测评标准</div>
                                </td>
                                <td><span class="template-id">TPL-20250404-004</span></td>
                                <td>燃气系统</td>
                                <td><span class="template-status-badge status-draft">草稿</span></td>
                                <td>赵三</td>
                                <td>2025-04-01</td>
                                <td>5</td>
                                <td class="template-actions-cell">
                                    <button class="template-action view" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="template-action edit" title="编辑" onclick="openEditTemplateModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="template-action copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="template-action compare" title="版本对比" onclick="openCompareModal()">
                                        <i class="fas fa-code-compare"></i>
                                    </button>
                                    <button class="template-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="template-name">通信网络容量评估模版</span>
                                    <div class="template-desc">城市通信网络容量和时延性能评估</div>
                                </td>
                                <td><span class="template-id">TPL-20250405-005</span></td>
                                <td>通信系统</td>
                                <td><span class="template-status-badge status-draft">草稿</span></td>
                                <td>陈三</td>
                                <td>2025-02-10</td>
                                <td>76</td>
                                <td class="template-actions-cell">
                                    <button class="template-action view" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="template-action edit" title="编辑" onclick="openEditTemplateModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="template-action copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="template-action compare" title="版本对比" onclick="openCompareModal()">
                                        <i class="fas fa-code-compare"></i>
                                    </button>
                                    <button class="template-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="template-name">电力系统暂态仿真专项评估模版</span>
                                    <div class="template-desc">针对电力系统暂态事件的详细仿真度评估</div>
                                </td>
                                <td><span class="template-id">TPL-20250406-006</span></td>
                                <td>电力系统</td>
                                <td><span class="template-status-badge status-active">已发布</span></td>
                                <td>张三</td>
                                <td>2025-03-05</td>
                                <td>67</td>
                                <td class="template-actions-cell">
                                    <button class="template-action view" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="template-action edit" title="编辑" onclick="openEditTemplateModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="template-action copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="template-action compare" title="版本对比" onclick="openCompareModal()">
                                        <i class="fas fa-code-compare"></i>
                                    </button>
                                    <button class="template-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <label class="custom-checkbox">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <button class="favorite-action">
                                        <i class="fas fa-star star-icon"></i>
                                    </button>
                                    <span class="template-name">城市轨道交通运行仿真评估模版</span>
                                    <div class="template-desc">轨道交通系统运行效率和安全性评估</div>
                                </td>
                                <td><span class="template-id">TPL-20250407-007</span></td>
                                <td>交通系统</td>
                                <td><span class="template-status-badge status-review">审核中</span></td>
                                <td>李三</td>
                                <td>2025-03-30</td>
                                <td>12</td>
                                <td class="template-actions-cell">
                                    <button class="template-action view" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="template-action edit" title="编辑" onclick="openEditTemplateModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="template-action copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="template-action compare" title="版本对比" onclick="openCompareModal()">
                                        <i class="fas fa-code-compare"></i>
                                    </button>
                                    <button class="template-action delete" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-footer">
                        <div class="page-info">
                            显示 1 到 7 条，共 128 条记录
                        </div>
                        <div class="pagination">
                            <button class="page-btn disabled">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button class="page-btn disabled">
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">18</button>
                            <button class="page-btn">
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button class="page-btn">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近模版活动卡片 -->
            <div class="recent-activity-card">
                <div class="card-header">
                    <div class="card-title">模版活动日志</div>
                    <div class="card-actions">
                        <button class="card-action"><i class="fas fa-sync-alt"></i></button>
                        <button class="card-action"><i class="fas fa-ellipsis-v"></i></button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="activity-timeline">
                        <div class="activity-item success">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">张三发布了新模版"电力系统暂态仿真专项评估模版"</div>
                                <div class="activity-time">今天 09:32</div>
                            </div>
                        </div>
                        <div class="activity-item warning">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">王三提交了"供水管网压力仿真度评估模版"审核申请</div>
                                <div class="activity-time">昨天 16:45</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">李三编辑了"城市交通流量仿真度评估模版"</div>
                                <div class="activity-time">昨天 14:23</div>
                            </div>
                        </div>
                        <div class="activity-item success">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">您复制并创建了新模版"城市轨道交通运行仿真评估模版"</div>
                                <div class="activity-time">04-02 11:05</div>
                            </div>
                        </div>
                        <div class="activity-item danger">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">陈三归档了"通信网络容量评估模版"</div>
                                <div class="activity-time">04-01 09:17</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                                <div class="activity-title">系统管理员更新了模版库版本控制策略</div>
                                <div class="activity-time">04-01 08:30</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新建/编辑模版弹窗 -->
<div class="modal-overlay" id="templateModal">
    <div class="modal modal-lg">
        <div class="modal-header">
            <div>
                <h2 class="modal-title" id="templateModalTitle">新建仿真度测评模版</h2>
                <p class="modal-subtitle">创建或编辑仿真度测评模版及其指标体系</p>
            </div>
            <button class="modal-close" onclick="closeModal('templateModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-section">
                <div class="section-title">
                    <div class="section-icon"><i class="fas fa-info-circle"></i></div>
                    <span>基本信息</span>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <div class="modal-form-group">
                            <label class="modal-label">模版名称</label>
                            <input type="text" class="modal-input" placeholder="输入模版名称" value="电力系统仿真度标准评估模版">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="modal-form-group">
                            <label class="modal-label">所属领域</label>
                            <select class="modal-select">
                                <option value="power" selected>电力系统</option>
                                <option value="transportation">交通系统</option>
                                <option value="water">水利系统</option>
                                <option value="gas">燃气系统</option>
                                <option value="communication">通信系统</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-form-group">
                    <label class="modal-label">模版描述</label>
                    <textarea class="modal-textarea" placeholder="描述此模版的用途和适用场景">用于电力系统稳态和暂态仿真评估的标准模版，包含电网设备建模、负荷模型、电压和频率稳定性以及暂态响应等关键指标，适用于变电站、输电线路和配电网络的仿真度评估。</textarea>
                </div>
                <div class="modal-form-group">
                    <label class="modal-label">标签</label>
                    <div class="tag-input-container">
                        <div class="tag">
                            电力系统 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            仿真度评估 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            标准模版 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            电网稳定性 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <input type="text" class="tag-input" placeholder="输入标签后按回车添加">
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <div class="section-icon"><i class="fas fa-sitemap"></i></div>
                    <span>指标体系</span>
                </div>
                <div class="tree-editor">
                    <div class="tree-toolbar">
                        <div class="tree-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-plus"></i> 添加指标
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-file-import"></i> 导入指标
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-expand-alt"></i> 展开全部
                            </button>
                        </div>
                        <div class="tree-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-trash-alt"></i> 清空
                            </button>
                        </div>
                    </div>
                    <div class="tree-container">
                        <!-- 根指标：电网建模精度 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                <div class="node-icon"><i class="fas fa-layer-group"></i></div>
                                <div class="node-title">电网建模精度</div>
                                <div class="node-weight">权重: 30%</div>
                                <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                <div class="node-actions">
                                    <button class="node-action"><i class="fas fa-plus"></i></button>
                                    <button class="node-action"><i class="fas fa-edit"></i></button>
                                    <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                            <div class="node-children">
                                <!-- 叶子指标：变压器建模精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-cube"></i></div>
                                        <div class="node-title">变压器建模精度</div>
                                        <div class="node-weight">权重: 40%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 变压器参数拟合算法</div>
                                            <div class="node-dataset">数据集: 变压器实测数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：线路建模精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-cube"></i></div>
                                        <div class="node-title">线路建模精度</div>
                                        <div class="node-weight">权重: 35%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 线路参数识别算法</div>
                                            <div class="node-dataset">数据集: 输电线路监测数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：负荷建模精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-cube"></i></div>
                                        <div class="node-title">负荷建模精度</div>
                                        <div class="node-weight">权重: 25%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 负荷模型评估算法</div>
                                            <div class="node-dataset">数据集: 用电负荷历史数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 根指标：电网稳态特性 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                <div class="node-icon"><i class="fas fa-layer-group"></i></div>
                                <div class="node-title">电网稳态特性</div>
                                <div class="node-weight">权重: 25%</div>
                                <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                <div class="node-actions">
                                    <button class="node-action"><i class="fas fa-plus"></i></button>
                                    <button class="node-action"><i class="fas fa-edit"></i></button>
                                    <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                            <div class="node-children">
                                <!-- 叶子指标：潮流计算精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-cube"></i></div>
                                        <div class="node-title">潮流计算精度</div>
                                        <div class="node-weight">权重: 60%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 潮流计算评估算法</div>
                                            <div class="node-dataset">数据集: 电网运行数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：无功功率分布 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-cube"></i></div>
                                        <div class="node-title">无功功率分布</div>
                                        <div class="node-weight">权重: 40%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 无功优化评估算法</div>
                                            <div class="node-dataset">数据集: 电网无功分布数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 根指标：电网暂态特性 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                <div class="node-icon"><i class="fas fa-layer-group"></i></div>
                                <div class="node-title">电网暂态特性</div>
                                <div class="node-weight">权重: 35%</div>
                                <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                <div class="node-actions">
                                    <button class="node-action"><i class="fas fa-plus"></i></button>
                                    <button class="node-action"><i class="fas fa-edit"></i></button>
                                    <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                            <div class="node-children">
                                <!-- 叶子指标：故障响应特性 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-cube"></i></div>
                                        <div class="node-title">故障响应特性</div>
                                        <div class="node-weight">权重: 50%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 故障响应评估算法</div>
                                            <div class="node-dataset">数据集: 故障录波数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：暂态稳定性 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-cube"></i></div>
                                        <div class="node-title">暂态稳定性</div>
                                        <div class="node-weight">权重: 50%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 暂态稳定性评估算法</div>
                                            <div class="node-dataset">数据集: 系统暂态运行数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 根指标：控制系统仿真精度 (没有子节点但作为根指标) -->
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                <div class="node-icon"><i class="fas fa-layer-group"></i></div>
                                <div class="node-title">控制系统仿真精度</div>
                                <div class="node-weight">权重: 10%</div>
                                <div class="node-info leaf-node-info">
                                    <div class="node-algorithm">算法: 控制系统性能评估算法</div>
                                    <div class="node-dataset">数据集: 控制系统响应数据</div>
                                </div>
                                <div class="node-actions">
                                    <button class="node-action"><i class="fas fa-plus"></i></button>
                                    <button class="node-action"><i class="fas fa-edit"></i></button>
                                    <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <div class="section-icon"><i class="fas fa-file-alt"></i></div>
                    <span>附件上传</span>
                </div>
                <div class="file-upload-container">
                    <div class="upload-icon"><i class="fas fa-cloud-upload-alt"></i></div>
                    <div class="upload-text">拖拽文件到此处或 <span class="upload-browse">浏览文件</span></div>
                    <div class="upload-desc">支持 PDF, Word, Excel, ZIP 格式，单个文件不超过20MB</div>
                </div>
                <div class="upload-file-list">
                    <div class="upload-file-item">
                        <div class="file-info">
                            <div class="file-icon"><i class="fas fa-file-pdf"></i></div>
                            <div>
                                <div class="file-name">电力系统仿真度测评说明文档.pdf</div>
                                <div class="file-size">2.4 MB</div>
                            </div>
                        </div>
                        <div class="file-progress">
                            <div class="file-progress-bar" style="width: 100%;"></div>
                        </div>
                        <div class="file-actions">
                            <button class="file-action"><i class="fas fa-eye"></i></button>
                            <button class="file-action delete"><i class="fas fa-trash-alt"></i></button>
                        </div>
                    </div>
                    <div class="upload-file-item">
                        <div class="file-info">
                            <div class="file-icon"><i class="fas fa-file-excel"></i></div>
                            <div>
                                <div class="file-name">指标权重配置表.xlsx</div>
                                <div class="file-size">1.8 MB</div>
                            </div>
                        </div>
                        <div class="file-progress">
                            <div class="file-progress-bar" style="width: 100%;"></div>
                        </div>
                        <div class="file-actions">
                            <button class="file-action"><i class="fas fa-eye"></i></button>
                            <button class="file-action delete"><i class="fas fa-trash-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('templateModal')">取消</button>
            <button class="btn btn-secondary">保存为草稿</button>
            <button class="btn btn-primary">保存并发布</button>
        </div>
    </div>
</div>

<!-- 新建模版弹窗（跨电力-通信-交通行业） -->
<div class="modal-overlay" id="createNewTemplateModal">
    <div class="modal modal-lg">
        <div class="modal-header">
            <div>
                <h2 class="modal-title">新建仿真度测评模版</h2>
            </div>
            <button class="modal-close" onclick="closeModal('createNewTemplateModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-section">
                <div class="section-title">
                    <div class="section-icon"><i class="fas fa-info-circle"></i></div>
                    <span>基本信息</span>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <div class="modal-form-group">
                            <label class="modal-label">模版名称</label>
                            <input type="text" class="modal-input" placeholder="输入模版名称" value="城市级联关基行业综合仿真度评估模版">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="modal-form-group">
                            <label class="modal-label">所属领域</label>
                            <select class="modal-select">
                                <option value="multi" selected>跨行业综合</option>
                                <option value="power">电力系统</option>
                                <option value="transportation">交通系统</option>
                                <option value="communication">通信系统</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-form-group">
                    <label class="modal-label">模版描述</label>
                    <textarea class="modal-textarea" placeholder="描述此模版的用途和适用场景">用于城市级关键基础设施跨行业协同仿真度评估的综合模版，覆盖电力-通信-交通三大行业的关键性能指标和交互影响，适用于城市灾害应急响应、关键基础设施级联故障分析及韧性评估等场景。</textarea>
                </div>
                <div class="modal-form-group">
                    <label class="modal-label">标签</label>
                    <div class="tag-input-container">
                        <div class="tag">
                            跨行业仿真 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            电力-通信-交通 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            关键基础设施 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            级联故障分析 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            城市韧性评估 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <input type="text" class="tag-input" placeholder="输入标签后按回车添加">
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <div class="section-icon"><i class="fas fa-sitemap"></i></div>
                    <span>跨行业指标体系</span>
                </div>
                <div class="tree-editor">
                    <div class="tree-toolbar">
                        <div class="tree-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-plus"></i> 添加指标
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-file-import"></i> 导入指标
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-expand-alt"></i> 展开全部
                            </button>
                        </div>
                        <div class="tree-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-trash-alt"></i> 清空
                            </button>
                        </div>
                    </div>
                    <div class="tree-container">
                        <!-- 根指标：电力系统仿真精度 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                <div class="node-icon"><i class="fas fa-bolt"></i></div>
                                <div class="node-title">电力系统仿真精度</div>
                                <div class="node-weight">权重: 35%</div>
                                <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                <div class="node-actions">
                                    <button class="node-action"><i class="fas fa-plus"></i></button>
                                    <button class="node-action"><i class="fas fa-edit"></i></button>
                                    <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                            <div class="node-children">
                                <!-- 叶子指标：配电网拓扑结构精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-project-diagram"></i></div>
                                        <div class="node-title">配电网拓扑结构精度</div>
                                        <div class="node-weight">权重: 30%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 电网拓扑一致性评估算法</div>
                                            <div class="node-dataset">数据集: 配电网GIS数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：负荷预测精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-chart-line"></i></div>
                                        <div class="node-title">负荷预测精度</div>
                                        <div class="node-weight">权重: 35%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 负荷预测评估算法</div>
                                            <div class="node-dataset">数据集: 历史负荷数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：电网故障响应特性 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                        <div class="node-title">电网故障响应特性</div>
                                        <div class="node-weight">权重: 35%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 电网暂态响应评估算法</div>
                                            <div class="node-dataset">数据集: 故障录波数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 根指标：通信网络仿真精度 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                <div class="node-icon"><i class="fas fa-broadcast-tower"></i></div>
                                <div class="node-title">通信网络仿真精度</div>
                                <div class="node-weight">权重: 30%</div>
                                <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                <div class="node-actions">
                                    <button class="node-action"><i class="fas fa-plus"></i></button>
                                    <button class="node-action"><i class="fas fa-edit"></i></button>
                                    <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                            <div class="node-children">
                                <!-- 叶子指标：通信网络容量仿真精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-tachometer-alt"></i></div>
                                        <div class="node-title">通信网络容量仿真精度</div>
                                        <div class="node-weight">权重: 40%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 网络流量模型评估算法</div>
                                            <div class="node-dataset">数据集: 通信网络流量数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：通信时延仿真精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-hourglass-half"></i></div>
                                        <div class="node-title">通信时延仿真精度</div>
                                        <div class="node-weight">权重: 35%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 网络时延评估算法</div>
                                            <div class="node-dataset">数据集: 通信网络RTT数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：通信网络覆盖精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-wifi"></i></div>
                                        <div class="node-title">通信网络覆盖精度</div>
                                        <div class="node-weight">权重: 25%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 信号覆盖评估算法</div>
                                            <div class="node-dataset">数据集: 基站覆盖测试数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 根指标：交通系统仿真精度 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                <div class="node-icon"><i class="fas fa-road"></i></div>
                                <div class="node-title">交通系统仿真精度</div>
                                <div class="node-weight">权重: 25%</div>
                                <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                <div class="node-actions">
                                    <button class="node-action"><i class="fas fa-plus"></i></button>
                                    <button class="node-action"><i class="fas fa-edit"></i></button>
                                    <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                            <div class="node-children">
                                <!-- 叶子指标：交通流量仿真精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-car"></i></div>
                                        <div class="node-title">交通流量仿真精度</div>
                                        <div class="node-weight">权重: 40%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 交通流量模拟评估算法</div>
                                            <div class="node-dataset">数据集: 交通流量监测数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：交通信号控制精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-traffic-light"></i></div>
                                        <div class="node-title">交通信号控制精度</div>
                                        <div class="node-weight">权重: 30%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 信号控制仿真评估算法</div>
                                            <div class="node-dataset">数据集: 交通信号控制数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：道路拥堵状态精度 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-map-marked-alt"></i></div>
                                        <div class="node-title">道路拥堵状态精度</div>
                                        <div class="node-weight">权重: 30%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 拥堵状态评估算法</div>
                                            <div class="node-dataset">数据集: 城市路网实时状态数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 根指标：跨行业交互影响仿真精度 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                <div class="node-icon"><i class="fas fa-exchange-alt"></i></div>
                                <div class="node-title">跨行业交互影响仿真精度</div>
                                <div class="node-weight">权重: 10%</div>
                                <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                <div class="node-actions">
                                    <button class="node-action"><i class="fas fa-plus"></i></button>
                                    <button class="node-action"><i class="fas fa-edit"></i></button>
                                    <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                            <div class="node-children">
                                <!-- 叶子指标：电力-通信依赖关系 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-plug"></i></div>
                                        <div class="node-title">电力-通信依赖关系</div>
                                        <div class="node-weight">权重: 35%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 电力-通信级联影响评估算法</div>
                                            <div class="node-dataset">数据集: 基站供电依赖数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：通信-交通依赖关系 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-network-wired"></i></div>
                                        <div class="node-title">通信-交通依赖关系</div>
                                        <div class="node-weight">权重: 35%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 通信-交通协同评估算法</div>
                                            <div class="node-dataset">数据集: 交通信号通信数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 叶子指标：交通-电力依赖关系 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                        <div class="node-icon"><i class="fas fa-charging-station"></i></div>
                                        <div class="node-title">交通-电力依赖关系</div>
                                        <div class="node-weight">权重: 30%</div>
                                        <div class="node-info leaf-node-info">
                                            <div class="node-algorithm">算法: 电动车充电需求评估算法</div>
                                            <div class="node-dataset">数据集: 充电桩负荷数据</div>
                                        </div>
                                        <div class="node-actions">
                                            <button class="node-action"><i class="fas fa-edit"></i></button>
                                            <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <div class="section-icon"><i class="fas fa-file-alt"></i></div>
                    <span>附件上传</span>
                </div>
                <div class="file-upload-container">
                    <div class="upload-icon"><i class="fas fa-cloud-upload-alt"></i></div>
                    <div class="upload-text">拖拽文件到此处或 <span class="upload-browse">浏览文件</span></div>
                    <div class="upload-desc">支持 PDF, Word, Excel, ZIP 格式，单个文件不超过20MB</div>
                </div>
                <div class="upload-file-list">
                    <div class="upload-file-item">
                        <div class="file-info">
                            <div class="file-icon"><i class="fas fa-file-pdf"></i></div>
                            <div>
                                <div class="file-name">城市级联关基行业仿真度测评方法.pdf</div>
                                <div class="file-size">3.2 MB</div>
                            </div>
                        </div>
                        <div class="file-progress">
                            <div class="file-progress-bar" style="width: 100%;"></div>
                        </div>
                        <div class="file-actions">
                            <button class="file-action"><i class="fas fa-eye"></i></button>
                            <button class="file-action delete"><i class="fas fa-trash-alt"></i></button>
                        </div>
                    </div>
                    <div class="upload-file-item">
                        <div class="file-info">
                            <div class="file-icon"><i class="fas fa-file-excel"></i></div>
                            <div>
                                <div class="file-name">跨行业指标体系及权重.xlsx</div>
                                <div class="file-size">2.1 MB</div>
                            </div>
                        </div>
                        <div class="file-progress">
                            <div class="file-progress-bar" style="width: 100%;"></div>
                        </div>
                        <div class="file-actions">
                            <button class="file-action"><i class="fas fa-eye"></i></button>
                            <button class="file-action delete"><i class="fas fa-trash-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('createNewTemplateModal')">取消</button>
            <button class="btn btn-secondary">保存为草稿</button>
            <button class="btn btn-primary">保存并发布</button>
        </div>
    </div>
</div>

    <!-- 模版版本对比弹窗 -->
    <div class="modal-overlay" id="compareModal">
        <div class="modal modal-lg">
            <div class="modal-header">
                <div>
                    <h2 class="modal-title">模版版本对比</h2>
                    <p class="modal-subtitle">比较不同版本的模版内容和配置差异</p>
                </div>
                <button class="modal-close" onclick="closeModal('compareModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="version-selector">
                    <div class="version-selector-col">
                        <div class="version-col-title">基准版本</div>
                        <select class="modal-select">
                            <option>v1.0.0 (2025-01-15 初始版本)</option>
                            <option>v1.1.0 (2025-02-03 更新指标权重)</option>
                            <option selected>v1.2.0 (2025-03-10 新增暂态评估指标)</option>
                        </select>
                    </div>
                    <button class="version-swap">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                    <div class="version-selector-col">
                        <div class="version-col-title">比较版本</div>
                        <select class="modal-select">
                            <option>v1.0.0 (2025-01-15 初始版本)</option>
                            <option>v1.1.0 (2025-02-03 更新指标权重)</option>
                            <option>v1.2.0 (2025-03-10 新增暂态评估指标)</option>
                            <option selected>v1.3.0 (2025-03-28 最新版本)</option>
                        </select>
                    </div>
                </div>

                <div class="diff-controls">
                    <div class="view-mode">
                        <button class="view-mode-btn active">并排视图</button>
                        <button class="view-mode-btn">合并视图</button>
                        <button class="view-mode-btn">统计视图</button>
                    </div>
                    <div class="filter-diff">
                        <label class="modal-checkbox">
                            <input type="checkbox" checked> 仅显示差异项
                        </label>
                    </div>
                </div>

                <div class="diff-container">
                    <div class="diff-header">
                        模版对比: v1.2.0 → v1.3.0
                    </div>
                    <div class="diff-split">
                        <div class="diff-panel">
                            <div class="diff-file">
                                <div class="diff-file-header">
                                    基本信息
                                </div>
                                <div class="diff-file-body">
                                    <div class="diff-line">
                                        <div class="diff-line-num">1</div>
                                        <div class="diff-line-content">模版名称: 电力系统仿真度标准评估模版</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">2</div>
                                        <div class="diff-line-content">版本: <span class="diff-remove-text">v1.2.0</span></div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">3</div>
                                        <div class="diff-line-content">领域: 电力系统</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">4</div>
                                        <div class="diff-line-content">状态: 已发布</div>
                                    </div>
                                </div>
                            </div>
                            <div class="diff-file">
                                <div class="diff-file-header">
                                    指标体系
                                </div>
                                <div class="diff-file-body">
                                    <div class="diff-line">
                                        <div class="diff-line-num">1</div>
                                        <div class="diff-line-content">电网建模精度 (权重: 30%)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">2</div>
                                        <div class="diff-line-content">  ├─ 变压器建模精度 (权重: 40%)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">3</div>
                                        <div class="diff-line-content">  ├─ 线路建模精度 (权重: 35%)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">4</div>
                                        <div class="diff-line-content">  └─ 负荷建模精度 (权重: 25%)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">5</div>
                                        <div class="diff-line-content">电网稳态特性 (权重: 25%)</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">6</div>
                                        <div class="diff-line-content">  ├─ 潮流计算精度 (权重: <span class="diff-remove-text">60%</span>)</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">7</div>
                                        <div class="diff-line-content">  └─ 无功功率分布 (权重: <span class="diff-remove-text">40%</span>)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">8</div>
                                        <div class="diff-line-content">电网暂态特性 (权重: 35%)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="diff-panel">
                            <div class="diff-file">
                                <div class="diff-file-header">
                                    基本信息
                                </div>
                                <div class="diff-file-body">
                                    <div class="diff-line">
                                        <div class="diff-line-num">1</div>
                                        <div class="diff-line-content">模版名称: 电力系统仿真度标准评估模版</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">2</div>
                                        <div class="diff-line-content">版本: <span class="diff-add-text">v1.3.0</span></div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">3</div>
                                        <div class="diff-line-content">领域: 电力系统</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">4</div>
                                        <div class="diff-line-content">状态: 已发布</div>
                                    </div>
                                </div>
                            </div>
                            <div class="diff-file">
                                <div class="diff-file-header">
                                    指标体系
                                </div>
                                <div class="diff-file-body">
                                    <div class="diff-line">
                                        <div class="diff-line-num">1</div>
                                        <div class="diff-line-content">电网建模精度 (权重: 30%)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">2</div>
                                        <div class="diff-line-content">  ├─ 变压器建模精度 (权重: 40%)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">3</div>
                                        <div class="diff-line-content">  ├─ 线路建模精度 (权重: 35%)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">4</div>
                                        <div class="diff-line-content">  └─ 负荷建模精度 (权重: 25%)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">5</div>
                                        <div class="diff-line-content">电网稳态特性 (权重: 25%)</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">6</div>
                                        <div class="diff-line-content">  ├─ 潮流计算精度 (权重: <span class="diff-add-text">50%</span>)</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">7</div>
                                        <div class="diff-line-content">  └─ 无功功率分布 (权重: <span class="diff-add-text">50%</span>)</div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">8</div>
                                        <div class="diff-line-content">电网暂态特性 (权重: 35%)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color legend-add"></div>
                        <span>新增</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color legend-remove"></div>
                        <span>移除</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color legend-modify"></div>
                        <span>修改</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('compareModal')">关闭</button>
                <button class="btn">导出对比报告</button>
            </div>
        </div>
    </div>

    <!-- 导入/导出模版弹窗 -->
    <div class="modal-overlay" id="importExportModal">
        <div class="modal">
            <div class="modal-header">
                <div>
                    <h2 class="modal-title" id="importExportModalTitle">导入模版</h2>
                    <p class="modal-subtitle" id="importExportModalSubtitle">从外部来源导入测评模版</p>
                </div>
                <button class="modal-close" onclick="closeModal('importExportModal')">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 导入/导出选项卡 -->
                <div class="nav-tabs">
                    <div class="nav-tab active" id="importTab" onclick="switchTab('import')">导入模版</div>
                    <div class="nav-tab" id="exportTab" onclick="switchTab('export')">导出模版</div>
                </div>
                
                <!-- 导入内容 -->
                <div class="tab-content active" id="importContent">
                    <div class="import-sources">
                        <div class="import-source active">
                            <div class="import-icon"><i class="fas fa-file-upload"></i></div>
                            <div class="import-title">文件上传</div>
                            <div class="import-desc">从本地上传模版文件</div>
                        </div>
                        <div class="import-source">
                            <div class="import-icon"><i class="fas fa-link"></i></div>
                            <div class="import-title">URL导入</div>
                            <div class="import-desc">从远程URL导入模版</div>
                        </div>
                        <div class="import-source">
                            <div class="import-icon"><i class="fas fa-cloud-download-alt"></i></div>
                            <div class="import-title">模版市场</div>
                            <div class="import-desc">从共享模版市场导入</div>
                        </div>
                    </div>
                    
                    <div class="import-options">
                        <div class="option-row">
                            <div class="option-label">导入方式</div>
                            <div class="option-input">
                                <select class="modal-select">
                                    <option selected>创建新模版</option>
                                    <option>更新现有模版</option>
                                    <option>创建新版本</option>
                                </select>
                            </div>
                        </div>
                        <div class="option-row">
                            <div class="option-label">冲突处理</div>
                            <div class="option-input">
                                <select class="modal-select">
                                    <option selected>智能合并</option>
                                    <option>导入覆盖</option>
                                    <option>保留本地</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="file-upload-container">
                        <div class="upload-icon"><i class="fas fa-file-import"></i></div>
                        <div class="upload-text">拖拽模版文件到此处或 <span class="upload-browse">浏览文件</span></div>
                        <div class="upload-desc">支持 JSON, XML, Excel 格式，单个文件不超过10MB</div>
                    </div>
                    
                    <div class="preview-container">
                        <div class="preview-header">
                            <div>模版预览</div>
                            <button class="btn btn-secondary">刷新预览</button>
                        </div>
                        <div class="preview-body">
                            <div class="preview-placeholder">
                                <div class="preview-icon"><i class="fas fa-file-import"></i></div>
                                <div>上传文件后将在此处显示预览</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="validation-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%;"></div>
                        </div>
                        <div class="progress-status">
                            <div>验证进度: 0%</div>
                            <div>等待开始...</div>
                        </div>
                    </div>
                </div>
                
                <!-- 导出内容 -->
                <div class="tab-content" id="exportContent">
                    <div class="export-template-list">
                        <div class="export-template-item">
                            <label class="custom-checkbox export-checkbox">
                                <input type="checkbox" checked>
                                <span class="checkmark"></span>
                            </label>
                            <div class="export-template-info">
                                <div class="export-template-name">电力系统仿真度标准评估模版</div>
                                <div class="export-template-meta">版本: v1.3.0 | 创建于: 2025-03-15 | ID: TPL-20250401-001</div>
                            </div>
                        </div>
                        <div class="export-template-item">
                            <label class="custom-checkbox export-checkbox">
                                <input type="checkbox" checked>
                                <span class="checkmark"></span>
                            </label>
                            <div class="export-template-info">
                                <div class="export-template-name">城市交通流量仿真度评估模版</div>
                                <div class="export-template-meta">版本: v1.0.2 | 创建于: 2025-03-20 | ID: TPL-20250402-002</div>
                            </div>
                        </div>
                        <div class="export-template-item">
                            <label class="custom-checkbox export-checkbox">
                                <input type="checkbox">
                                <span class="checkmark"></span>
                            </label>
                            <div class="export-template-info">
                                <div class="export-template-name">供水管网压力仿真度评估模版</div>
                                <div class="export-template-meta">版本: v0.9.1 | 创建于: 2025-03-28 | ID: TPL-20250403-003</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="export-format-options">
                        <div class="export-format active">
                            <div class="export-format-icon"><i class="fas fa-file-code"></i></div>
                            <div class="export-format-name">JSON</div>
                            <div class="export-format-desc">标准JSON格式，便于系统间导入导出</div>
                        </div>
                        <div class="export-format">
                            <div class="export-format-icon"><i class="fas fa-file-excel"></i></div>
                            <div class="export-format-name">Excel</div>
                            <div class="export-format-desc">电子表格格式，便于查看和编辑</div>
                        </div>
                        <div class="export-format">
                            <div class="export-format-icon"><i class="fas fa-file-archive"></i></div>
                            <div class="export-format-name">完整归档包</div>
                            <div class="export-format-desc">包含模版定义和相关资源文件</div>
                        </div>
                    </div>
                    
                    <div class="option-row">
                        <div class="option-label">导出选项</div>
                        <div class="option-input">
                            <label class="modal-checkbox">
                                <input type="checkbox" checked> 包含指标体系完整定义
                            </label>
                            <label class="modal-checkbox">
                                <input type="checkbox" checked> 包含算法关联配置
                            </label>
                            <label class="modal-checkbox">
                                <input type="checkbox"> 包含版本历史记录
                            </label>
                            <label class="modal-checkbox">
                                <input type="checkbox"> 包含附件文件
                            </label>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <div class="alert-icon"><i class="fas fa-exclamation-triangle"></i></div>
                        <div class="alert-content">
                            <div class="alert-title">注意事项</div>
                            <div class="alert-message">导出的模版文件可能包含敏感配置信息，请妥善保管并遵循相关安全规定处理。</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('importExportModal')">取消</button>
                <button class="btn btn-primary" id="importExportButton">开始导入</button>
            </div>
        </div>
    </div>

    <script>
        // 高级筛选切换
        const advancedFilterToggle = document.querySelector('.advanced-filter-toggle');
        const advancedFilterRow = document.querySelector('.advanced-filter-row');
        const filterActions = document.querySelector('.filter-actions');
        
        advancedFilterRow.style.display = 'none';
        filterActions.style.display = 'none';
        
        advancedFilterToggle.addEventListener('click', function() {
            if (advancedFilterRow.style.display === 'none') {
                advancedFilterRow.style.display = 'grid';
                filterActions.style.display = 'flex';
                this.innerHTML = '<i class="fas fa-chevron-up"></i> 收起筛选';
            } else {
                advancedFilterRow.style.display = 'none';
                filterActions.style.display = 'none';
                this.innerHTML = '<i class="fas fa-sliders-h"></i> 高级筛选';
            }
        });

        // 全选功能
        const selectAllCheckbox = document.querySelector('#select-all');
        const checkboxes = document.querySelectorAll('.template-table tbody .custom-checkbox input');
        
        selectAllCheckbox.addEventListener('change', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
        
        // 收藏功能
        const favoriteButtons = document.querySelectorAll('.favorite-action');
        
        favoriteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const starIcon = this.querySelector('.star-icon');
                if (starIcon.classList.contains('star-active')) {
                    starIcon.classList.remove('star-active');
                    this.classList.remove('active');
                } else {
                    starIcon.classList.add('star-active');
                    this.classList.add('active');
                }
            });
        });
        
        // 表格操作按钮交互
        const actionButtons = document.querySelectorAll('.template-action');
        
        actionButtons.forEach(button => {
            if (!button.hasAttribute('onclick')) {
                button.addEventListener('click', function() {
                    const action = this.classList.contains('view') ? '查看' :
                                this.classList.contains('edit') ? '编辑' :
                                this.classList.contains('copy') ? '复制' :
                                this.classList.contains('compare') ? '版本对比' : '删除';
                                
                    const templateName = this.closest('tr').querySelector('.template-name').textContent;
                    
                    if (action === '删除') {
                        if (confirm(`确定要删除模版"${templateName}"吗？此操作不可撤销。`)) {
                            alert(`模版"${templateName}"已删除`);
                        }
                    } else if (action !== '编辑' && action !== '版本对比') {
                        alert(`正在${action}模版: ${templateName}`);
                    }
                });
            }
        });
        
        // 分页功能
        const pageButtons = document.querySelectorAll('.pagination .page-btn');
        
        pageButtons.forEach(button => {
            if (!button.classList.contains('disabled') && !button.classList.contains('active')) {
                button.addEventListener('click', function() {
                    pageButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    if (this.textContent.trim() !== '...') {
                        alert(`切换到第 ${this.textContent} 页`);
                    }
                });
            }
        });
        
        // 导入/导出模态窗口切换标签页
        function switchTab(tab) {
            const importTab = document.getElementById('importTab');
            const exportTab = document.getElementById('exportTab');
            const importContent = document.getElementById('importContent');
            const exportContent = document.getElementById('exportContent');
            const importExportButton = document.getElementById('importExportButton');
            const modalTitle = document.getElementById('importExportModalTitle');
            const modalSubtitle = document.getElementById('importExportModalSubtitle');
            
            if (tab === 'import') {
                importTab.classList.add('active');
                exportTab.classList.remove('active');
                importContent.classList.add('active');
                exportContent.classList.remove('active');
                importExportButton.textContent = '开始导入';
                modalTitle.textContent = '导入模版';
                modalSubtitle.textContent = '从外部来源导入测评模版';
            } else {
                importTab.classList.remove('active');
                exportTab.classList.add('active');
                importContent.classList.remove('active');
                exportContent.classList.add('active');
                importExportButton.textContent = '导出选中模版';
                modalTitle.textContent = '导出模版';
                modalSubtitle.textContent = '将选中的模版导出为文件';
            }
        }
        
        // 打开/关闭模态窗口
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        // 新建模版 - 现在打开新的跨行业模版弹窗
        function openCreateTemplateModal() {
            openModal('createNewTemplateModal');
        }
        
        // 编辑模版
        function openEditTemplateModal() {
            const modalTitle = document.getElementById('templateModalTitle');
            modalTitle.textContent = '编辑仿真度测评模版';
            openModal('templateModal');
        }
        
        // 版本对比
        function openCompareModal() {
            openModal('compareModal');
        }
        
        // 导入/导出模版
        function openImportExportModal(tab = 'import') {
            switchTab(tab);
            openModal('importExportModal');
        }
        
        // 关闭窗口的事件监听
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });
        });
        
        // 阻止模态内容点击事件冒泡
        const modalContents = document.querySelectorAll('.modal');
        modalContents.forEach(content => {
            content.addEventListener('click', function(event) {
                event.stopPropagation();
            });
        });

        // 导入模版来源切换
        const importSources = document.querySelectorAll('.import-source');
        importSources.forEach(source => {
            source.addEventListener('click', function() {
                importSources.forEach(s => s.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 导出格式切换
        const exportFormats = document.querySelectorAll('.export-format');
        exportFormats.forEach(format => {
            format.addEventListener('click', function() {
                exportFormats.forEach(f => f.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>

    <!-- 在文件末尾添加主题切换的JavaScript代码 -->
    <script>
        // 主题切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const themeSwitch = document.querySelector('.theme-switch');
            const htmlRoot = document.documentElement;
            
            // 检查本地存储中的主题设置，默认为暗色主题
            const savedTheme = localStorage.getItem('theme');
            // 只有当明确保存了"light"时才应用亮色主题
            if (savedTheme === 'light') {
                htmlRoot.classList.add('light-theme');
            } else {
                // 如果没有保存或者保存的不是"light"，则确保是暗色主题
                localStorage.setItem('theme', 'dark');
            }
            
            // 切换主题的函数
            function toggleTheme() {
                if (htmlRoot.classList.contains('light-theme')) {
                    htmlRoot.classList.remove('light-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    htmlRoot.classList.add('light-theme');
                    localStorage.setItem('theme', 'light');
                }
            }
            
            // 为主题切换按钮添加点击事件
            themeSwitch.addEventListener('click', toggleTheme);
        });
    </script>
</body>
</html>
