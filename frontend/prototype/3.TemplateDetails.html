<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模版详情 - 城市级关基级联的网络仿真度评估系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Echarts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入中国地图数据 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/map/js/china.js"></script>
    <style>
        /* 页面整体放大120% */
        html {
            zoom: 110%;
        }
        
        :root {
            --primary-color: #0096FF;
            --secondary-color: #00E0FF;
            --background-dark: #0F1520;
            --background-card: #1A202E;
            --text-color: #E0E6F0;
            --text-secondary: #A0A8B8;
            --border-color: #2A3142;
            --highlight-color: #3E9BFF;
            --success-color: #00C48C;
            --warning-color: #FFB946;
            --danger-color: #F25767;
            --info-color: #0095FF;
            --add-color: #27ae60;
            --remove-color: #e74c3c;
            --modify-color: #f39c12;
        }

        /* 亮色主题变量 */
        :root.light-theme {
            --primary-color: #0078D4;
            --secondary-color: #00A2E0;
            --background-dark: #FFFFFF;
            --background-card: #FFFFFF;
            --text-color: #333333;
            --text-secondary: #666666;
            --border-color: #E0E6ED;
            --highlight-color: #106EBE;
            --success-color: #0A9D5A;
            --warning-color: #F29D41;
            --danger-color: #E74C3C;
            --info-color: #0078D4;
            --add-color: #27ae60;
            --remove-color: #e74c3c;
            --modify-color: #f39c12;
        }

        /* Logo样式 */
        .logo {
	zoom: 110%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 10px;
        }

        .logo-icon {
            font-size: 24px;
            color: var(--primary-color);
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 2px rgba(0, 224, 255, 0.4));
        }

        .logo span {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 0.5px;
            background: linear-gradient(90deg, var(--text-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: var(--background-dark);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            min-height: 100vh;
        }

        /* 顶部导航栏样式 */
        .header {
            background-color: rgba(16, 22, 36, 0.95);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 60px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .nav-item {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .nav-link {
            color: var(--text-color);
            text-decoration: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link.active {
            color: var(--primary-color);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 0 0 6px 6px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            border-bottom: 1px solid var(--border-color);
        }
		
		.dropdown-menu {
			list-style-type: none; /* 去掉前面的点 */
		}

        .dropdown-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px;
        }

        .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            padding-left: 20px;
        }

        .dropdown-icon {
            margin-right: 8px;
        }

        .nav-tools {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-icon, .user-profile {
            position: relative;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .notification-icon:hover, .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-profile img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 24px;
        }

        /* 模版详情页返回导航 */
        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .breadcrumb a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .breadcrumb a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-secondary);
        }

        .breadcrumb-current {
            color: var(--text-color);
            font-weight: 500;
        }

        /* 模版详情页头部 */
        .template-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .template-detail-title-section {
            flex: 1;
        }

        .template-detail-title {
            font-size: 28px;
            font-weight: 500;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .template-status {
            font-size: 13px;
            padding: 4px 10px;
            border-radius: 4px;
            font-weight: 500;
        }

        .template-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 10px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .template-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: var(--highlight-color);
        }

        .action-btn.danger {
            color: var(--danger-color);
            border-color: rgba(242, 87, 103, 0.3);
        }

        .action-btn.danger:hover {
            background-color: rgba(242, 87, 103, 0.1);
            border-color: var(--danger-color);
        }

        .action-btn.success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .action-btn.success:hover {
            background-color: rgba(0, 196, 140, 0.8);
        }

        /* 标签样式 */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 16px;
        }

        .tag {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            background-color: rgba(62, 155, 255, 0.1);
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid rgba(62, 155, 255, 0.2);
        }

        .tag-close {
            cursor: pointer;
            display: none;
        }

        .tags-container.editable .tag-close {
            display: inline;
        }

        .add-tag-btn {
            padding: 4px 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px dashed var(--border-color);
            border-radius: 4px;
            color: var(--text-secondary);
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .add-tag-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 模版详情主内容 */
        .template-detail-content {
            display: flex;
            gap: 24px;
        }

        .template-detail-main {
            flex: 1;
        }

        .template-detail-sidebar {
            width: 320px;
        }

        /* 卡片和面板通用样式 */
        .card {
            background-color: var(--background-card);
            border-radius: 10px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .card-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .card-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .card-action:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
        }

        .card-body {
            padding: 20px;
        }

        /* 导航选项卡 */
        .detail-nav-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            overflow-x: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .detail-nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .detail-nav-tab {
            padding: 12px 20px;
            font-size: 14px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-nav-tab:hover {
            color: var(--text-color);
        }

        .detail-nav-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        /* 详情内容区域 */
        .detail-content-section {
            margin-bottom: 24px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-actions {
            display: flex;
            gap: 8px;
        }

        /* 描述区域 */
        .description-section {
            padding: 16px;
            background-color: rgba(255, 255, 255, 0.02);
            border-radius: 8px;
            border-left: 3px solid var(--primary-color);
            margin-bottom: 24px;
        }

        .description-text {
            color: var(--text-color);
            font-size: 14px;
            line-height: 1.6;
        }

        .description-edit {
            width: 100%;
            min-height: 100px;
            margin-top: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 12px;
            color: var(--text-color);
            font-size: 14px;
            resize: vertical;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .form-sublabel {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 10px 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.08);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-col {
            flex: 1;
        }

        /* 树形编辑器样式 */
        .tree-editor {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .tree-toolbar {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.02);
            border-bottom: 1px solid var(--border-color);
        }

        .tree-actions {
            display: flex;
            gap: 8px;
        }

        .tree-container {
            padding: 15px;
            overflow-y: auto;
        }

        .tree-node {
            margin-bottom: 8px;
        }

        .node-content {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 10px;
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            position: relative;
        }

        .node-content:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .node-collapse {
            cursor: pointer;
            width: 20px;
            display: flex;
            justify-content: center;
        }

        .node-icon {
            color: var(--primary-color);
            font-size: 14px;
        }

        .node-title {
            flex: 1;
        }

        .node-actions {
            display: flex;
            gap: 5px;
        }

        .node-action {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 12px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .node-action:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-color);
        }

        .node-weight {
            margin-left: 10px;
            font-size: 12px;
            color: var(--text-secondary);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 10px;
        }

        .node-children {
            margin-left: 25px;
            padding-left: 15px;
            border-left: 1px dashed var(--border-color);
            margin-top: 8px;
        }

        /* 版本历史 */
        .version-timeline {
            position: relative;
            padding-left: 24px;
        }

        .version-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 8px;
            height: 100%;
            width: 2px;
            background-color: var(--border-color);
        }

        .version-item {
            position: relative;
            padding-bottom: 20px;
        }

        .version-item:last-child {
            padding-bottom: 0;
        }

        .version-dot {
            position: absolute;
            left: -24px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 3px solid var(--background-card);
            z-index: 1;
        }

        .version-item.current .version-dot {
            background-color: var(--success-color);
        }

        .version-content {
            padding-bottom: 12px;
            border-bottom: 1px dashed var(--border-color);
        }

        .version-item:last-child .version-content {
            border-bottom: none;
        }

        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4px;
        }

        .version-title {
            font-size: 14px;
            font-weight: 500;
        }

        .version-date {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .version-author {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .version-desc {
            font-size: 13px;
            color: var(--text-color);
            margin-bottom: 10px;
        }

        .version-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .version-badge {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 3px;
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-secondary);
        }

        .version-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .version-btn {
            font-size: 12px;
            padding: 4px 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .version-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 关联任务列表 */
        .related-tasks-list {
            margin-top: 10px;
        }

        .task-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-item:hover {
            background-color: rgba(255, 255, 255, 0.02);
        }

        .task-icon {
            width: 36px;
            height: 36px;
            border-radius: 6px;
            background-color: rgba(62, 155, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            margin-right: 12px;
        }

        .task-info {
            flex: 1;
        }

        .task-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 2px;
        }

        .task-meta {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            gap: 12px;
        }

        .task-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: auto;
        }

        .status-running {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
        }

        .status-completed {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .status-scheduled {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        /* 权重配置工具 */
        .weight-config-container {
            margin-top: 16px;
        }

        .weight-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.02);
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid var(--border-color);
        }

        .weight-info {
            flex: 1;
        }

        .weight-name {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .weight-control {
            width: 180px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .weight-slider {
            flex: 1;
            height: 4px;
            background-color: var(--border-color);
            border-radius: 2px;
            position: relative;
        }

        .weight-slider-fill {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 2px;
        }

        .weight-value {
            width: 40px;
            text-align: right;
            font-size: 14px;
            font-weight: 500;
        }

        /* 算法设置 */
        .algorithm-item {
            display: flex;
            padding: 12px;
            background-color: rgba(255, 255, 255, 0.02);
            border-radius: 6px;
            border: 1px solid var(--border-color);
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }

        .algorithm-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .algorithm-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            background-color: rgba(62, 155, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            margin-right: 12px;
        }

        .algorithm-info {
            flex: 1;
        }

        .algorithm-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .algorithm-desc {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .algorithm-meta {
            margin-top: 6px;
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            gap: 12px;
        }

        .algorithm-actions {
            display: flex;
            flex-direction: column;
            gap: 6px;
            justify-content: center;
        }

        .algorithm-btn {
            font-size: 12px;
            padding: 4px 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .algorithm-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 权限设置 */
        .permissions-container {
            margin-top: 16px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-title {
            font-size: 14px;
        }

        .permission-select {
            width: 140px;
            padding: 6px 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 13px;
        }

        /* 图表容器 */
        .chart-container {
            height: 300px;
            margin-top: 16px;
        }

        /* 日志区域 */
        .log-container {
            background-color: rgba(16, 22, 36, 0.6);
            border-radius: 6px;
            border: 1px solid var(--border-color);
            height: 300px;
            overflow-y: auto;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .log-line {
            margin-bottom: 4px;
        }

        .log-timestamp {
            color: var(--text-secondary);
            margin-right: 8px;
        }

        .log-level-info {
            color: var(--info-color);
            margin-right: 8px;
        }

        .log-level-warning {
            color: var(--warning-color);
            margin-right: 8px;
        }

        .log-level-error {
            color: var(--danger-color);
            margin-right: 8px;
        }

        /* 模态窗口通用样式 */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 2000;
            animation: fadeIn 0.3s ease;
        }

        .modal {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid var(--border-color);
            overflow: hidden;
            max-width: 90%;
            width: 600px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            animation: slideIn 0.3s ease;
        }

        .modal-lg {
            width: 900px;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 500;
        }

        .modal-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 22px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            color: var(--text-color);
        }

        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: var(--background-card);
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--highlight-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: rgba(0, 196, 140, 0.8);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: rgba(242, 87, 103, 0.8);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: var(--border-color);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translate(-50%, -60%); opacity: 0; }
            to { transform: translate(-50%, -50%); opacity: 1; }
        }

        /* 响应式设计 */
        @media screen and (max-width: 1200px) {
            .template-detail-content {
                flex-direction: column;
            }
            
            .template-detail-sidebar {
                width: 100%;
            }
        }

        @media screen and (max-width: 768px) {
            .template-detail-header {
                flex-direction: column;
                gap: 16px;
            }
            
            .template-actions {
                width: 100%;
                overflow-x: auto;
                padding-bottom: 8px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 20px;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 主题切换开关样式 */
        .theme-switch {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 60px;
            height: 30px;
            background: rgba(0, 149, 255, 0.15);
            border-radius: 30px;
            padding: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .theme-switch:hover {
            transform: scale(1.05);
        }

        .theme-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-size: 14px;
        }

        .theme-switch-slider {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            left: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        :root.light-theme .theme-switch-slider {
            transform: translateX(30px);
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
        
        /* 亮色主题的导航栏调整 */
        :root.light-theme .header {
            background-color: rgba(245, 247, 250, 0.95);
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .nav-link:hover {
            color: var(--primary-color);
        }
        
        :root.light-theme .dropdown-menu {
            background-color: var(--background-card);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        /* 亮色主题的卡片调整 */
        :root.light-theme .card {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .card-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .card-action:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        /* 亮色主题的表单元素 */
        :root.light-theme .form-input, 
        :root.light-theme .form-select, 
        :root.light-theme .form-textarea {
            background-color: #F8FAFD;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .form-input:focus, 
        :root.light-theme .form-select:focus, 
        :root.light-theme .form-textarea:focus {
            background-color: #FFFFFF;
        }
        
        /* 亮色主题的树形结构 */
        :root.light-theme .node-content {
            background-color: rgba(0, 0, 0, 0.01);
        }
        
        :root.light-theme .node-content:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        /* 亮色主题的按钮 */
        :root.light-theme .action-btn {
            background-color: #F5F7FA;
        }
        
        :root.light-theme .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .btn-secondary {
            background-color: #F0F2F5;
        }
        
        :root.light-theme .btn-secondary:hover {
            background-color: #E8EBF0;
        }
        
        /* 亮色主题的滚动条 */
        :root.light-theme ::-webkit-scrollbar-track {
            background: #F5F7FA;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb {
            background: #D0D6E0;
        }
        
        /* 亮色主题的日志区域 */
        :root.light-theme .log-container {
            background-color: rgba(240, 245, 250, 0.8);
            border: 1px solid var(--border-color);
            color: #333333;
        }
        
        :root.light-theme .log-timestamp {
            color: #666666;
        }
        
        :root.light-theme .log-level-info {
            color: #0078D4;
        }
        
        :root.light-theme .log-level-warning {
            color: #F29D41;
        }
        
        :root.light-theme .log-level-error {
            color: #E74C3C;
        }
        
        /* 亮色主题的按钮 */
        :root.light-theme .action-btn {
            background-color: #F5F7FA;
        }
        
        :root.light-theme .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .action-btn.primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .action-btn.primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        :root.light-theme .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .btn-primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        :root.light-theme .btn-secondary {
            background-color: #F0F2F5;
        }
        
        :root.light-theme .btn-secondary:hover {
            background-color: #E8EBF0;
        }
    </style>
    
<body>
    <div class="container">

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="dashboard.html"><i class="fas fa-home"></i> 首页</a>
                <span class="breadcrumb-separator">/</span>
                <a href="template-list.html">模版库列表</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">城市级联关基行业综合仿真度评估模版</span>
            </div>

            <!-- 模版详情页头部 -->
            <div class="template-detail-header">
                <div class="template-detail-title-section">
                    <div class="template-detail-title">
                        <span>城市级联关基行业综合仿真度评估模版</span>
                        <span class="template-status status-active">已发布</span>
                    </div>
                    <div class="template-meta">
                        <div class="meta-item">
                            <i class="fas fa-layer-group"></i>
                            <span>跨行业综合</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-code-branch"></i>
                            <span>版本: v1.2.3</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>创建日期: 2025-03-15</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>最后更新: 2025-04-02</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-user"></i>
                            <span>创建人: 张三</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-tasks"></i>
                            <span>已用于: 12 个评估任务</span>
                        </div>
                    </div>
                    <div class="tags-container">
                        <div class="tag">
                            跨行业仿真 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            电力-通信-交通 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            关键基础设施 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            级联故障分析 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <div class="tag">
                            城市韧性评估 <span class="tag-close"><i class="fas fa-times"></i></span>
                        </div>
                        <button class="add-tag-btn" style="display: none;">
                            <i class="fas fa-plus"></i> 添加标签
                        </button>
                    </div>
                </div>
                <div class="template-actions">
                    <button class="action-btn" id="editModeBtn">
                        <i class="fas fa-edit"></i> 编辑模版
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-code-branch"></i> 新建版本
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-code-compare"></i> 版本对比
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-copy"></i> 复制模版
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-file-export"></i> 导出
                    </button>
                    <button class="action-btn danger">
                        <i class="fas fa-trash-alt"></i> 删除
                    </button>
                </div>
            </div>

            <!-- 模版详情主内容 -->
            <div class="template-detail-content">
                <!-- 左侧主要内容区域 -->
                <div class="template-detail-main">
                    <!-- 导航选项卡 -->
                    <div class="detail-nav-tabs">
                        <div class="detail-nav-tab active" data-tab="overview">
                            <i class="fas fa-home"></i> 概览
                        </div>
                        <div class="detail-nav-tab" data-tab="indicators">
                            <i class="fas fa-sitemap"></i> 指标体系
                        </div>
                        <div class="detail-nav-tab" data-tab="weights">
                            <i class="fas fa-balance-scale"></i> 权重配置
                        </div>
                        <div class="detail-nav-tab" data-tab="algorithms">
                            <i class="fas fa-microchip"></i> 算法设置
                        </div>
                        <div class="detail-nav-tab" data-tab="versions">
                            <i class="fas fa-code-branch"></i> 版本历史
                        </div>
                        <div class="detail-nav-tab" data-tab="usage">
                            <i class="fas fa-tasks"></i> 使用情况
                        </div>
                        <div class="detail-nav-tab" data-tab="settings">
                            <i class="fas fa-cog"></i> 高级设置
                        </div>
                        <div class="detail-nav-tab" data-tab="logs">
                            <i class="fas fa-history"></i> 操作日志
                        </div>
                    </div>

                    <!-- 概览内容 -->
                    <div class="tab-content active" id="overview-content">
                        <div class="description-section">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle"></i>
                                <span>模版描述</span>
                            </h3>
                            <div class="description-text">
                                用于城市级关键基础设施跨行业协同仿真度评估的综合模版，覆盖电力-通信-交通三大行业的关键性能指标和交互影响，适用于城市灾害应急响应、关键基础设施级联故障分析及韧性评估等场景。
                                <br><br>
                                该模版综合考虑了三大关键基础设施行业的独立运行性能与相互之间的依赖关系，通过多层次指标体系全面评估城市级仿真模型的准确性与可靠性。模版包含专门针对跨行业级联效应的评估指标，可用于预测和分析在极端情况下的系统韧性表现。
                            </div>
                            <textarea class="description-edit" style="display: none;">用于城市级关键基础设施跨行业协同仿真度评估的综合模版，覆盖电力-通信-交通三大行业的关键性能指标和交互影响，适用于城市灾害应急响应、关键基础设施级联故障分析及韧性评估等场景。

该模版综合考虑了三大关键基础设施行业的独立运行性能与相互之间的依赖关系，通过多层次指标体系全面评估城市级仿真模型的准确性与可靠性。模版包含专门针对跨行业级联效应的评估指标，可用于预测和分析在极端情况下的系统韧性表现。</textarea>
                        </div>

                        <!-- 指标体系概览 -->
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-sitemap"></i>
                                    <span>指标体系概览</span>
                                </h3>
                            </div>
                            <div class="card">
                                <div class="tree-container">
                                    <!-- 根指标：电力系统仿真精度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                            <div class="node-icon"><i class="fas fa-bolt"></i></div>
                                            <div class="node-title">电力系统仿真精度</div>
                                            <div class="node-weight">权重: 35%</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-project-diagram"></i></div>
                                                    <div class="node-title">配电网拓扑结构精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-chart-line"></i></div>
                                                    <div class="node-title">负荷预测精度</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                                    <div class="node-title">电网故障响应特性</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：通信网络仿真精度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                            <div class="node-icon"><i class="fas fa-broadcast-tower"></i></div>
                                            <div class="node-title">通信网络仿真精度</div>
                                            <div class="node-weight">权重: 30%</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-tachometer-alt"></i></div>
                                                    <div class="node-title">通信网络容量仿真精度</div>
                                                    <div class="node-weight">权重: 40%</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-hourglass-half"></i></div>
                                                    <div class="node-title">通信时延仿真精度</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-wifi"></i></div>
                                                    <div class="node-title">通信网络覆盖精度</div>
                                                    <div class="node-weight">权重: 25%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：交通系统仿真精度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                            <div class="node-icon"><i class="fas fa-road"></i></div>
                                            <div class="node-title">交通系统仿真精度</div>
                                            <div class="node-weight">权重: 25%</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-car"></i></div>
                                                    <div class="node-title">交通流量仿真精度</div>
                                                    <div class="node-weight">权重: 40%</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-traffic-light"></i></div>
                                                    <div class="node-title">交通信号控制精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-map-marked-alt"></i></div>
                                                    <div class="node-title">道路拥堵状态精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：跨行业交互影响仿真精度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                            <div class="node-icon"><i class="fas fa-exchange-alt"></i></div>
                                            <div class="node-title">跨行业交互影响仿真精度</div>
                                            <div class="node-weight">权重: 10%</div>
                                        </div>
                                        <div class="node-children">
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-plug"></i></div>
                                                    <div class="node-title">电力-通信依赖关系</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-network-wired"></i></div>
                                                    <div class="node-title">通信-交通依赖关系</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                </div>
                                            </div>
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-charging-station"></i></div>
                                                    <div class="node-title">交通-电力依赖关系</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 关联算法概览 -->
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-microchip"></i>
                                    <span>关联算法</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary">管理算法</button>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-bolt"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">电网拓扑一致性评估算法</div>
                                            <div class="algorithm-desc">评估电力系统拓扑模型与实际网络的一致性程度</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v2.1.0</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-03-10</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                            <button class="algorithm-btn">查看详情</button>
                                        </div>
                                    </div>
                                    
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-broadcast-tower"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">通信网络时延评估算法</div>
                                            <div class="algorithm-desc">通过对比仿真与实测数据评估通信时延模型准确性</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v1.5.3</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-03-22</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                            <button class="algorithm-btn">查看详情</button>
                                        </div>
                                    </div>
                                    
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-road"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">交通流量模拟评估算法</div>
                                            <div class="algorithm-desc">评估交通流量仿真模型与实际观测数据的偏差程度</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v2.0.1</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-02-15</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                            <button class="algorithm-btn">查看详情</button>
                                        </div>
                                    </div>
                                    
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-exchange-alt"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">跨行业级联影响评估算法</div>
                                            <div class="algorithm-desc">评估行业间依赖关系和级联故障传播的仿真准确性</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v1.2.0</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-03-28</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                            <button class="algorithm-btn">查看详情</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 使用统计 -->
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>使用统计</span>
                                </h3>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="chart-container" id="usage-chart"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 指标体系内容 -->
                    <div class="tab-content" id="indicators-content" style="display: none;">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-sitemap"></i>
                                    <span>完整指标体系</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary edit-mode-btn" style="display: none;">
                                        <i class="fas fa-plus"></i> 添加指标
                                    </button>
                                    <button class="btn btn-secondary edit-mode-btn" style="display: none;">
                                        <i class="fas fa-file-import"></i> 导入指标
                                    </button>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-expand-alt"></i> 展开全部
                                    </button>
                                </div>
                            </div>
                            <div class="tree-editor">
                                <div class="tree-toolbar" style="display: none;">
                                    <div class="tree-actions">
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-plus"></i> 添加指标
                                        </button>
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-file-import"></i> 导入指标
                                        </button>
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-expand-alt"></i> 展开全部
                                        </button>
                                    </div>
                                    <div class="tree-actions">
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-trash-alt"></i> 清空
                                        </button>
                                    </div>
                                </div>
                                <div class="tree-container">
                                    <!-- 根指标：电力系统仿真精度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                            <div class="node-icon"><i class="fas fa-bolt"></i></div>
                                            <div class="node-title">电力系统仿真精度</div>
                                            <div class="node-weight">权重: 35%</div>
                                            <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                            <div class="node-actions" style="display: none;">
                                                <button class="node-action"><i class="fas fa-plus"></i></button>
                                                <button class="node-action"><i class="fas fa-edit"></i></button>
                                                <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                        <div class="node-children">
                                            <!-- 叶子指标：配电网拓扑结构精度 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-project-diagram"></i></div>
                                                    <div class="node-title">配电网拓扑结构精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 电网拓扑一致性评估算法</div>
                                                        <div class="node-dataset">数据集: 配电网GIS数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 叶子指标：负荷预测精度 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-chart-line"></i></div>
                                                    <div class="node-title">负荷预测精度</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 负荷预测评估算法</div>
                                                        <div class="node-dataset">数据集: 历史负荷数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 叶子指标：电网故障响应特性 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                                    <div class="node-title">电网故障响应特性</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 电网暂态响应评估算法</div>
                                                        <div class="node-dataset">数据集: 故障录波数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：通信网络仿真精度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                            <div class="node-icon"><i class="fas fa-broadcast-tower"></i></div>
                                            <div class="node-title">通信网络仿真精度</div>
                                            <div class="node-weight">权重: 30%</div>
                                            <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                            <div class="node-actions" style="display: none;">
                                                <button class="node-action"><i class="fas fa-plus"></i></button>
                                                <button class="node-action"><i class="fas fa-edit"></i></button>
                                                <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                        <div class="node-children">
                                            <!-- 叶子指标：通信网络容量仿真精度 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-tachometer-alt"></i></div>
                                                    <div class="node-title">通信网络容量仿真精度</div>
                                                    <div class="node-weight">权重: 40%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 网络流量模型评估算法</div>
                                                        <div class="node-dataset">数据集: 通信网络流量数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 叶子指标：通信时延仿真精度 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-hourglass-half"></i></div>
                                                    <div class="node-title">通信时延仿真精度</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 网络时延评估算法</div>
                                                        <div class="node-dataset">数据集: 通信网络RTT数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 叶子指标：通信网络覆盖精度 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-wifi"></i></div>
                                                    <div class="node-title">通信网络覆盖精度</div>
                                                    <div class="node-weight">权重: 25%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 信号覆盖评估算法</div>
                                                        <div class="node-dataset">数据集: 基站覆盖测试数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：交通系统仿真精度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                            <div class="node-icon"><i class="fas fa-road"></i></div>
                                            <div class="node-title">交通系统仿真精度</div>
                                            <div class="node-weight">权重: 25%</div>
                                            <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                            <div class="node-actions" style="display: none;">
                                                <button class="node-action"><i class="fas fa-plus"></i></button>
                                                <button class="node-action"><i class="fas fa-edit"></i></button>
                                                <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                        <div class="node-children">
                                            <!-- 叶子指标：交通流量仿真精度 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-car"></i></div>
                                                    <div class="node-title">交通流量仿真精度</div>
                                                    <div class="node-weight">权重: 40%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 交通流量模拟评估算法</div>
                                                        <div class="node-dataset">数据集: 交通流量监测数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 叶子指标：交通信号控制精度 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-traffic-light"></i></div>
                                                    <div class="node-title">交通信号控制精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 信号控制仿真评估算法</div>
                                                        <div class="node-dataset">数据集: 交通信号控制数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 叶子指标：道路拥堵状态精度 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-map-marked-alt"></i></div>
                                                    <div class="node-title">道路拥堵状态精度</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 拥堵状态评估算法</div>
                                                        <div class="node-dataset">数据集: 城市路网实时状态数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 根指标：跨行业交互影响仿真精度 -->
                                    <div class="tree-node">
                                        <div class="node-content">
                                            <div class="node-collapse"><i class="fas fa-caret-down"></i></div>
                                            <div class="node-icon"><i class="fas fa-exchange-alt"></i></div>
                                            <div class="node-title">跨行业交互影响仿真精度</div>
                                            <div class="node-weight">权重: 10%</div>
                                            <div class="node-info root-node-info">根指标 - 仅设置权重</div>
                                            <div class="node-actions" style="display: none;">
                                                <button class="node-action"><i class="fas fa-plus"></i></button>
                                                <button class="node-action"><i class="fas fa-edit"></i></button>
                                                <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                        <div class="node-children">
                                            <!-- 叶子指标：电力-通信依赖关系 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-plug"></i></div>
                                                    <div class="node-title">电力-通信依赖关系</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 电力-通信级联影响评估算法</div>
                                                        <div class="node-dataset">数据集: 基站供电依赖数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 叶子指标：通信-交通依赖关系 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-network-wired"></i></div>
                                                    <div class="node-title">通信-交通依赖关系</div>
                                                    <div class="node-weight">权重: 35%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 通信-交通协同评估算法</div>
                                                        <div class="node-dataset">数据集: 交通信号通信数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 叶子指标：交通-电力依赖关系 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="node-collapse"><i class="fas fa-caret-right"></i></div>
                                                    <div class="node-icon"><i class="fas fa-charging-station"></i></div>
                                                    <div class="node-title">交通-电力依赖关系</div>
                                                    <div class="node-weight">权重: 30%</div>
                                                    <div class="node-info leaf-node-info">
                                                        <div class="node-algorithm">算法: 电动车充电需求评估算法</div>
                                                        <div class="node-dataset">数据集: 充电桩负荷数据</div>
                                                    </div>
                                                    <div class="node-actions" style="display: none;">
                                                        <button class="node-action"><i class="fas fa-edit"></i></button>
                                                        <button class="node-action"><i class="fas fa-trash-alt"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 权重配置内容 -->
                    <div class="tab-content" id="weights-content" style="display: none;">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-balance-scale"></i>
                                    <span>指标权重配置</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary edit-mode-btn" style="display: none;">
                                        <i class="fas fa-save"></i> 保存权重
                                    </button>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-chart-pie"></i> 权重视图
                                    </button>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <!-- 一级指标权重 -->
                                    <div class="weight-config-container">
                                        <h4>一级指标权重配置</h4>
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">电力系统仿真精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 35%;"></div>
                                                </div>
                                                <div class="weight-value">35%</div>
                                            </div>
                                        </div>
                                        
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">通信网络仿真精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 30%;"></div>
                                                </div>
                                                <div class="weight-value">30%</div>
                                            </div>
                                        </div>
                                        
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">交通系统仿真精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 25%;"></div>
                                                </div>
                                                <div class="weight-value">25%</div>
                                            </div>
                                        </div>
                                        
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">跨行业交互影响仿真精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 10%;"></div>
                                                </div>
                                                <div class="weight-value">10%</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 电力系统二级指标权重 -->
                                    <div class="weight-config-container">
                                        <h4>电力系统二级指标权重配置</h4>
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">配电网拓扑结构精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 30%;"></div>
                                                </div>
                                                <div class="weight-value">30%</div>
                                            </div>
                                        </div>
                                        
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">负荷预测精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 35%;"></div>
                                                </div>
                                                <div class="weight-value">35%</div>
                                            </div>
                                        </div>
                                        
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">电网故障响应特性</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 35%;"></div>
                                                </div>
                                                <div class="weight-value">35%</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 通信网络二级指标权重 -->
                                    <div class="weight-config-container">
                                        <h4>通信网络二级指标权重配置</h4>
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">通信网络容量仿真精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 40%;"></div>
                                                </div>
                                                <div class="weight-value">40%</div>
                                            </div>
                                        </div>
                                        
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">通信时延仿真精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 35%;"></div>
                                                </div>
                                                <div class="weight-value">35%</div>
                                            </div>
                                        </div>
                                        
                                        <div class="weight-item">
                                            <div class="weight-info">
                                                <div class="weight-name">通信网络覆盖精度</div>
                                            </div>
                                            <div class="weight-control">
                                                <div class="weight-slider">
                                                    <div class="weight-slider-fill" style="width: 25%;"></div>
                                                </div>
                                                <div class="weight-value">25%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 算法设置内容 -->
                    <div class="tab-content" id="algorithms-content" style="display: none;">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-microchip"></i>
                                    <span>算法配置</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary edit-mode-btn" style="display: none;">
                                        <i class="fas fa-plus"></i> 关联新算法
                                    </button>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-bolt"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">电网拓扑一致性评估算法</div>
                                            <div class="algorithm-desc">评估电力系统拓扑模型与实际网络的一致性程度</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v2.1.0</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-03-10</span>
                                                <span><i class="fas fa-cube"></i> 关联指标: 配电网拓扑结构精度</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                            <button class="algorithm-btn">查看详情</button>
                                            <button class="algorithm-btn edit-mode-btn" style="display: none;">取消关联</button>
                                        </div>
                                    </div>
                                    
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">负荷预测评估算法</div>
                                            <div class="algorithm-desc">评估负荷预测模型准确性与实际负荷变化的拟合度</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v1.3.5</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-02-28</span>
                                                <span><i class="fas fa-cube"></i> 关联指标: 负荷预测精度</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                                                                        <button class="algorithm-btn">查看详情</button>
                                            <button class="algorithm-btn edit-mode-btn" style="display: none;">取消关联</button>
                                        </div>
                                    </div>
                                    
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">电网暂态响应评估算法</div>
                                            <div class="algorithm-desc">评估电网故障响应仿真结果与实际录波数据的匹配程度</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v2.0.1</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-03-18</span>
                                                <span><i class="fas fa-cube"></i> 关联指标: 电网故障响应特性</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                            <button class="algorithm-btn">查看详情</button>
                                            <button class="algorithm-btn edit-mode-btn" style="display: none;">取消关联</button>
                                        </div>
                                    </div>
                                    
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-broadcast-tower"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">交通流量模拟评估算法</div>
                                            <div class="algorithm-desc">评估交通流量模拟与实际网络负载的匹配度</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v1.7.2</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-03-05</span>
                                                <span><i class="fas fa-cube"></i> 关联指标: 交通流量仿真精度</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                            <button class="algorithm-btn">查看详情</button>
                                            <button class="algorithm-btn edit-mode-btn" style="display: none;">取消关联</button>
                                        </div>
                                    </div>
                                    
                                    <div class="algorithm-item">
                                        <div class="algorithm-icon">
                                            <i class="fas fa-exchange-alt"></i>
                                        </div>
                                        <div class="algorithm-info">
                                            <div class="algorithm-name">电力-通信级联影响评估算法</div>
                                            <div class="algorithm-desc">评估行业间依赖关系和级联故障传播的仿真准确性</div>
                                            <div class="algorithm-meta">
                                                <span><i class="fas fa-code-branch"></i> 版本: v1.2.0</span>
                                                <span><i class="fas fa-calendar-alt"></i> 更新: 2025-03-28</span>
                                                <span><i class="fas fa-cube"></i> 关联指标: 跨行业交互影响仿真精度</span>
                                            </div>
                                        </div>
                                        <div class="algorithm-actions">
                                            <button class="algorithm-btn">查看详情</button>
                                            <button class="algorithm-btn edit-mode-btn" style="display: none;">取消关联</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-cogs"></i>
                                    <span>算法参数配置</span>
                                </h3>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label class="form-label">电力-通信级联影响评估算法参数</label>
                                        <div class="form-row">
                                            <div class="form-col">
                                                <label class="form-sublabel">故障传播阈值</label>
                                                <input type="text" class="form-input" value="0.75" disabled>
                                            </div>
                                            <div class="form-col">
                                                <label class="form-sublabel">最大传播深度</label>
                                                <input type="text" class="form-input" value="5" disabled>
                                            </div>
                                            <div class="form-col">
                                                <label class="form-sublabel">时间窗口(小时)</label>
                                                <input type="text" class="form-input" value="24" disabled>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">交通流量模拟评估算法参数</label>
                                        <div class="form-row">
                                            <div class="form-col">
                                                <label class="form-sublabel">对比采样频率(秒)</label>
                                                <input type="text" class="form-input" value="300" disabled>
                                            </div>
                                            <div class="form-col">
                                                <label class="form-sublabel">允许误差阈值(%)</label>
                                                <input type="text" class="form-input" value="15" disabled>
                                            </div>
                                            <div class="form-col">
                                                <label class="form-sublabel">峰值权重系数</label>
                                                <input type="text" class="form-input" value="1.5" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 版本历史内容 -->
                    <div class="tab-content" id="versions-content" style="display: none;">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-code-branch"></i>
                                    <span>版本历史记录</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-code-branch"></i> 新建版本
                                    </button>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="version-timeline">
                                        <div class="version-item current">
                                            <div class="version-dot"></div>
                                            <div class="version-content">
                                                <div class="version-header">
                                                    <div class="version-title">v1.2.3 (当前版本)</div>
                                                    <div class="version-date">2025-04-02</div>
                                                </div>
                                                <div class="version-author">张三 · 管理员</div>
                                                <div class="version-desc">优化了跨行业交互影响评估算法参数，提高了电力-通信依赖关系的评估准确性。</div>
                                                <div class="version-badges">
                                                    <span class="version-badge">参数调整</span>
                                                    <span class="version-badge">性能优化</span>
                                                </div>
                                                <div class="version-actions">
                                                    <button class="version-btn">查看详情</button>
                                                    <button class="version-btn">恢复此版本</button>
                                                    <button class="version-btn">对比</button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="version-item">
                                            <div class="version-dot"></div>
                                            <div class="version-content">
                                                <div class="version-header">
                                                    <div class="version-title">v1.2.0</div>
                                                    <div class="version-date">2025-03-28</div>
                                                </div>
                                                <div class="version-author">张三 · 管理员</div>
                                                <div class="version-desc">新增了交通-电力依赖关系指标，完善了跨行业交互影响评估体系。</div>
                                                <div class="version-badges">
                                                    <span class="version-badge">指标新增</span>
                                                    <span class="version-badge">功能增强</span>
                                                </div>
                                                <div class="version-actions">
                                                    <button class="version-btn">查看详情</button>
                                                    <button class="version-btn">恢复此版本</button>
                                                    <button class="version-btn">对比</button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="version-item">
                                            <div class="version-dot"></div>
                                            <div class="version-content">
                                                <div class="version-header">
                                                    <div class="version-title">v1.1.0</div>
                                                    <div class="version-date">2025-03-20</div>
                                                </div>
                                                <div class="version-author">李四 · 模版设计师</div>
                                                <div class="version-desc">调整了电力系统与通信网络的权重比例，优化了指标体系结构。</div>
                                                <div class="version-badges">
                                                    <span class="version-badge">权重调整</span>
                                                    <span class="version-badge">结构优化</span>
                                                </div>
                                                <div class="version-actions">
                                                    <button class="version-btn">查看详情</button>
                                                    <button class="version-btn">恢复此版本</button>
                                                    <button class="version-btn">对比</button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="version-item">
                                            <div class="version-dot"></div>
                                            <div class="version-content">
                                                <div class="version-header">
                                                    <div class="version-title">v1.0.0</div>
                                                    <div class="version-date">2025-03-15</div>
                                                </div>
                                                <div class="version-author">张三 · 管理员</div>
                                                <div class="version-desc">创建城市级联关基行业综合仿真度评估模版的初始版本。</div>
                                                <div class="version-badges">
                                                    <span class="version-badge">初始版本</span>
                                                </div>
                                                <div class="version-actions">
                                                    <button class="version-btn">查看详情</button>
                                                    <button class="version-btn">恢复此版本</button>
                                                    <button class="version-btn">对比</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 使用情况内容 -->
                    <div class="tab-content" id="usage-content" style="display: none;">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-tasks"></i>
                                    <span>关联评估任务</span>
                                </h3>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="related-tasks-list">
                                        <div class="task-item">
                                            <div class="task-icon">
                                                <i class="fas fa-tasks"></i>
                                            </div>
                                            <div class="task-info">
                                                <div class="task-name">通信-交通行业级联风险仿真度评估</div>
                                                <div class="task-meta">
                                                    <span><i class="fas fa-calendar-alt"></i> 2025-04-05</span>
                                                    <span><i class="fas fa-user"></i> 王五</span>
                                                    <span><i class="fas fa-chart-bar"></i> 综合评分: 87.5</span>
                                                </div>
                                            </div>
                                            <div class="task-status status-completed">已完成</div>
                                        </div>
                                        
                                        <div class="task-item">
                                            <div class="task-icon">
                                                <i class="fas fa-tasks"></i>
                                            </div>
                                            <div class="task-info">
                                                <div class="task-name">城市Y级联故障应急响应系统测评</div>
                                                <div class="task-meta">
                                                    <span><i class="fas fa-calendar-alt"></i> 2025-04-03</span>
                                                    <span><i class="fas fa-user"></i> 张三</span>
                                                    <span><i class="fas fa-chart-bar"></i> 综合评分: 82.3</span>
                                                </div>
                                            </div>
                                            <div class="task-status status-completed">已完成</div>
                                        </div>
                                        
                                        <div class="task-item">
                                            <div class="task-icon">
                                                <i class="fas fa-tasks"></i>
                                            </div>
                                            <div class="task-info">
                                                <div class="task-name">城市Z关键基础设施韧性测评</div>
                                                <div class="task-meta">
                                                    <span><i class="fas fa-calendar-alt"></i> 2025-04-10</span>
                                                    <span><i class="fas fa-user"></i> 李四</span>
                                                </div>
                                            </div>
                                            <div class="task-status status-running">进行中</div>
                                        </div>
                                        
                                        <div class="task-item">
                                            <div class="task-icon">
                                                <i class="fas fa-tasks"></i>
                                            </div>
                                            <div class="task-info">
                                                <div class="task-name">省级电力-通信协同仿真度评估</div>
                                                <div class="task-meta">
                                                    <span><i class="fas fa-calendar-alt"></i> 2025-04-15</span>
                                                    <span><i class="fas fa-user"></i> 王五</span>
                                                </div>
                                            </div>
                                            <div class="task-status status-scheduled">计划中</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 高级设置内容 -->
                    <div class="tab-content" id="settings-content" style="display: none;">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-cog"></i>
                                    <span>模版设置</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary edit-mode-btn" style="display: none;">
                                        <i class="fas fa-save"></i> 保存设置
                                    </button>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label class="form-label">模版状态</label>
                                        <select class="form-select" disabled>
                                            <option value="active" selected>已发布</option>
                                            <option value="draft">草稿</option>
                                            <option value="review">审核中</option>
                                            <option value="archived">已归档</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">适用场景</label>
                                        <div class="form-row">
                                            <div class="form-col">
                                                <label class="form-sublabel">主要场景</label>
                                                <select class="form-select" disabled>
                                                    <option value="emergency" selected>应急响应</option>
                                                    <option value="planning">规划设计</option>
                                                    <option value="operation">日常运行</option>
                                                    <option value="training">培训演练</option>
                                                </select>
                                            </div>
                                            <div class="form-col">
                                                <label class="form-sublabel">行业范围</label>
                                                <select class="form-select" disabled>
                                                    <option value="multi" selected>跨行业综合</option>
                                                    <option value="single">单一行业</option>
                                                </select>
                                            </div>
                                            <div class="form-col">
                                                <label class="form-sublabel">评估模式</label>
                                                <select class="form-select" disabled>
                                                    <option value="comprehensive" selected>综合评估</option>
                                                    <option value="specific">专项评估</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">计算设置</label>
                                        <div class="form-row">
                                            <div class="form-col">
                                                <label class="form-sublabel">默认计算引擎</label>
                                                <select class="form-select" disabled>
                                                    <option value="standard" selected>标准评估引擎</option>
                                                    <option value="fast">快速评估引擎</option>
                                                    <option value="expert">专家评估引擎</option>
                                                </select>
                                            </div>
                                            <div class="form-col">
                                                <label class="form-sublabel">数据采样频率</label>
                                                <input type="text" class="form-input" value="300" disabled>
                                            </div>
                                            <div class="form-col">
                                                <label class="form-sublabel">计算超时(秒)</label>
                                                <input type="text" class="form-input" value="3600" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-lock"></i>
                                    <span>权限设置</span>
                                </h3>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="permissions-container">
                                        <div class="permission-item">
                                            <div class="permission-title">查看模版</div>
                                            <select class="permission-select" disabled>
                                                <option value="all" selected>所有用户</option>
                                                <option value="registered">注册用户</option>
                                                <option value="team">团队成员</option>
                                                <option value="creator">仅创建者</option>
                                            </select>
                                        </div>
                                        
                                        <div class="permission-item">
                                            <div class="permission-title">编辑模版</div>
                                            <select class="permission-select" disabled>
                                                <option value="all">所有用户</option>
                                                <option value="registered">注册用户</option>
                                                <option value="team" selected>团队成员</option>
                                                <option value="creator">仅创建者</option>
                                            </select>
                                        </div>
                                        
                                        <div class="permission-item">
                                            <div class="permission-title">删除模版</div>
                                            <select class="permission-select" disabled>
                                                <option value="all">所有用户</option>
                                                <option value="registered">注册用户</option>
                                                <option value="team">团队成员</option>
                                                <option value="creator" selected>仅创建者</option>
                                            </select>
                                        </div>
                                        
                                        <div class="permission-item">
                                            <div class="permission-title">复制模版</div>
                                            <select class="permission-select" disabled>
                                                <option value="all">所有用户</option>
                                                <option value="registered" selected>注册用户</option>
                                                <option value="team">团队成员</option>
                                                <option value="creator">仅创建者</option>
                                            </select>
                                        </div>
                                        
                                        <div class="permission-item">
                                            <div class="permission-title">使用模版</div>
                                            <select class="permission-select" disabled>
                                                <option value="all">所有用户</option>
                                                <option value="registered" selected>注册用户</option>
                                                <option value="team">团队成员</option>
                                                <option value="creator">仅创建者</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作日志内容 -->
                    <div class="tab-content" id="logs-content" style="display: none;">
                        <div class="detail-content-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-history"></i>
                                    <span>操作日志</span>
                                </h3>
                                <div class="section-actions">
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-file-export"></i> 导出日志
                                    </button>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <div class="log-container">
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-02 14:32:15</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>用户 "张三" 更新了模版参数配置</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-04-02 14:30:42</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>用户 "张三" 调整了跨行业级联影响评估算法参数</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-03-28 10:15:38</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>用户 "张三" 创建了模版新版本 v1.2.0</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-03-28 10:14:21</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>用户 "张三" 添加了新指标 "交通-电力依赖关系"</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-03-20 16:42:19</span>
                                            <span class="log-level-warning">[WARNING]</span>
                                            <span>用户 "李四" 尝试删除核心指标 "电力系统仿真精度" 但操作被拒绝</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-03-20 16:38:54</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>用户 "李四" 修改了指标权重配置</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-03-20 16:35:27</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>用户 "李四" 创建了模版新版本 v1.1.0</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-03-15 11:20:03</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>用户 "张三" 首次发布模版 v1.0.0</span>
                                        </div>
                                        <div class="log-line">
                                            <span class="log-timestamp">2025-03-15 10:58:42</span>
                                            <span class="log-level-info">[INFO]</span>
                                            <span>用户 "张三" 创建了模版初始版本</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧侧边栏 -->
                <div class="template-detail-sidebar">
                    
                    <!-- 主要指标卡片 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">主要指标</div>
                        </div>
                        <div class="card-body">
                            <div class="algorithm-item">
                                <div class="algorithm-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="algorithm-info">
                                    <div class="algorithm-name">电力系统仿真精度</div>
                                    <div class="algorithm-meta">
                                        <span>权重: 35%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="algorithm-item">
                                <div class="algorithm-icon">
                                    <i class="fas fa-broadcast-tower"></i>
                                </div>
                                <div class="algorithm-info">
                                    <div class="algorithm-name">通信网络仿真精度</div>
                                    <div class="algorithm-meta">
                                        <span>权重: 30%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="algorithm-item">
                                <div class="algorithm-icon">
                                    <i class="fas fa-road"></i>
                                </div>
                                <div class="algorithm-info">
                                    <div class="algorithm-name">交通系统仿真精度</div>
                                    <div class="algorithm-meta">
                                        <span>权重: 25%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="algorithm-item">
                                <div class="algorithm-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <div class="algorithm-info">
                                    <div class="algorithm-name">跨行业交互影响仿真精度</div>
                                    <div class="algorithm-meta">
                                        <span>权重: 10%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 附件卡片 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">附件文档</div>
                            <div class="card-actions">
                                <button class="card-action edit-mode-btn" style="display: none;">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="algorithm-item">
                                <div class="algorithm-icon">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <div class="algorithm-info">
                                    <div class="algorithm-name">城市级联关基行业仿真度测评方法.pdf</div>
                                    <div class="algorithm-meta">
                                        <span>3.2 MB</span>
                                        <span>2025-03-15</span>
                                    </div>
                                </div>
                                <div class="algorithm-actions">
                                    <button class="algorithm-btn">查看</button>
                                </div>
                            </div>
                            
                            <div class="algorithm-item">
                                <div class="algorithm-icon">
                                    <i class="fas fa-file-excel"></i>
                                </div>
                                <div class="algorithm-info">
                                    <div class="algorithm-name">跨行业指标体系及权重.xlsx</div>
                                    <div class="algorithm-meta">
                                        <span>2.1 MB</span>
                                        <span>2025-03-15</span>
                                    </div>
                                </div>
                                <div class="algorithm-actions">
                                    <button class="algorithm-btn">查看</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 编辑指标弹窗 -->
    <div class="modal-overlay" id="editIndicatorModal">
        <div class="modal">
            <div class="modal-header">
                <div>
                    <h2 class="modal-title">编辑指标</h2>
                </div>
                <button class="modal-close" onclick="closeModal('editIndicatorModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">指标名称</label>
                    <input type="text" class="form-input" value="电力-通信依赖关系">
                </div>
                
                <div class="form-group">
                    <label class="form-label">指标描述</label>
                    <textarea class="form-textarea">评估电力系统对通信网络的供电可靠性与通信网络对电力系统的监控数据传输等依赖关系的仿真精度。</textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">指标权重</label>
                            <input type="text" class="form-input" value="35%">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">指标类型</label>
                            <select class="form-select">
                                <option value="leaf" selected>叶子指标</option>
                                <option value="branch">分支指标</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">关联算法</label>
                    <select class="form-select">
                        <option value="1" selected>电力-通信级联影响评估算法</option>
                        <option value="2">网络互联可靠性评估算法</option>
                        <option value="3">跨行业依赖图分析算法</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">关联数据集</label>
                    <select class="form-select">
                        <option value="1" selected>基站供电依赖数据</option>
                        <option value="2">电力监控通信数据</option>
                        <option value="3">电力-通信联动实测数据</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editIndicatorModal')">取消</button>
                <button class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <!-- 版本对比弹窗 -->
    <div class="modal-overlay" id="compareModal">
        <div class="modal modal-lg">
            <div class="modal-header">
                <div>
                    <h2 class="modal-title">模版版本对比</h2>
                    <p class="modal-subtitle">比较不同版本的模版内容和配置差异</p>
                </div>
                <button class="modal-close" onclick="closeModal('compareModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="version-selector">
                    <div class="version-selector-col">
                        <div class="version-col-title">基准版本</div>
                        <select class="form-select">
                            <option value="1.0.0">v1.0.0 (2025-03-15 初始版本)</option>
                            <option value="1.1.0">v1.1.0 (2025-03-20 权重调整)</option>
                            <option value="1.2.0" selected>v1.2.0 (2025-03-28 新增指标)</option>
                        </select>
                    </div>
                    <button class="version-swap">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                    <div class="version-selector-col">
                        <div class="version-col-title">比较版本</div>
                        <select class="form-select">
                            <option value="1.0.0">v1.0.0 (2025-03-15 初始版本)</option>
                            <option value="1.1.0">v1.1.0 (2025-03-20 权重调整)</option>
                            <option value="1.2.0">v1.2.0 (2025-03-28 新增指标)</option>
                            <option value="1.2.3" selected>v1.2.3 (2025-04-02 参数优化)</option>
                        </select>
                    </div>
                </div>

                <div class="diff-controls">
                    <div class="view-mode">
                        <button class="view-mode-btn active">并排视图</button>
                        <button class="view-mode-btn">合并视图</button>
                        <button class="view-mode-btn">统计视图</button>
                    </div>
                    <div class="filter-diff">
                        <label class="form-label">
                            <input type="checkbox" checked> 仅显示差异项
                        </label>
                    </div>
                </div>

                <div class="card">
                    <div class="diff-header">
                        模版对比: v1.2.0 → v1.2.3
                    </div>
                    <div class="diff-split">
                        <div class="diff-panel">
                            <div class="diff-file">
                                <div class="diff-file-header">
                                    算法参数设置
                                </div>
                                <div class="diff-file-body">
                                    <div class="diff-line">
                                        <div class="diff-line-num">1</div>
                                        <div class="diff-line-content">电力-通信级联影响评估算法:</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">2</div>
                                        <div class="diff-line-content">- 故障传播阈值: <span class="diff-remove-text">0.65</span></div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">3</div>
                                        <div class="diff-line-content">- 最大传播深度: 5</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">4</div>
                                        <div class="diff-line-content">- 时间窗口: <span class="diff-remove-text">12</span> 小时</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="diff-panel">
                            <div class="diff-file">
                                <div class="diff-file-header">
                                    算法参数设置
                                </div>
                                <div class="diff-file-body">
                                    <div class="diff-line">
                                        <div class="diff-line-num">1</div>
                                        <div class="diff-line-content">电力-通信级联影响评估算法:</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">2</div>
                                        <div class="diff-line-content">- 故障传播阈值: <span class="diff-add-text">0.75</span></div>
                                    </div>
                                    <div class="diff-line">
                                        <div class="diff-line-num">3</div>
                                        <div class="diff-line-content">- 最大传播深度: 5</div>
                                    </div>
                                    <div class="diff-line diff-modified">
                                        <div class="diff-line-num">4</div>
                                        <div class="diff-line-content">- 时间窗口: <span class="diff-add-text">24</span> 小时</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('compareModal')">关闭</button>
                <button class="btn btn-secondary">导出对比报告</button>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化使用统计图表
            const usageChart = echarts.init(document.getElementById('usage-chart'));
            const usageOption = {
                title: {
                    text: '模版使用次数统计',
                    left: 'center',
                    textStyle: {
                        color: '#E0E6F0'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月'],
                    axisLine: {
                        lineStyle: {
                            color: '#2A3142'
                        }
                    },
                    axisLabel: {
                        color: '#A0A8B8'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#2A3142'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#2A3142'
                        }
                    },
                    axisLabel: {
                        color: '#A0A8B8'
                    }
                },
                series: [{
                    name: '使用次数',
                    type: 'bar',
                    data: [2, 3, 5, 2],
                    itemStyle: {
                        color: '#0096FF'
                    }
                }]
            };
            usageChart.setOption(usageOption);
            
            // 初始化权重图表
            const weightChart = echarts.init(document.getElementById('weight-chart'));
            const weightOption = {
                title: {
                    text: '指标权重分布',
                    left: 'center',
                    textStyle: {
                        color: '#E0E6F0'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 10,
                    data: ['电力系统仿真精度', '通信网络仿真精度', '交通系统仿真精度', '跨行业交互影响仿真精度'],
                    textStyle: {
                        color: '#A0A8B8'
                    }
                },
                series: [
                    {
                        name: '指标权重',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '16',
                                fontWeight: 'bold',
                                color: '#E0E6F0'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 35, name: '电力系统仿真精度', itemStyle: { color: '#0096FF' } },
                            { value: 30, name: '通信网络仿真精度', itemStyle: { color: '#00C48C' } },
                            { value: 25, name: '交通系统仿真精度', itemStyle: { color: '#FFB946' } },
                            { value: 10, name: '跨行业交互影响仿真精度', itemStyle: { color: '#F25767' } }
                        ]
                    }
                ]
            };
            weightChart.setOption(weightOption);
            
            // 初始化预览图表
            const previewChart = echarts.init(document.getElementById('preview-chart'));
            const previewOption = {
                radar: {
                    indicator: [
                        { name: '电力系统', max: 100 },
                        { name: '通信网络', max: 100 },
                        { name: '交通系统', max: 100 },
                        { name: '跨行业交互', max: 100 }
                    ],
                    splitArea: {
                        areaStyle: {
                            color: ['rgba(255, 255, 255, 0.03)']
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(211, 211, 211, 0.3)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(211, 211, 211, 0.3)'
                        }
                    },
                    name: {
                        textStyle: {
                            color: '#A0A8B8'
                        }
                    }
                },
                series: [{
                    name: '模版覆盖分析',
                    type: 'radar',
                    data: [
                        {
                            value: [90, 85, 80, 70],
                            name: '覆盖度',
                            areaStyle: {
                                color: 'rgba(0, 150, 255, 0.2)'
                            },
                            lineStyle: {
                                color: '#0096FF'
                            },
                            itemStyle: {
                                color: '#0096FF'
                            }
                        }
                    ]
                }]
            };
            previewChart.setOption(previewOption);
            
            // 初始化使用趋势图
            if (document.getElementById('usage-trend-chart')) {
                const usageTrendChart = echarts.init(document.getElementById('usage-trend-chart'));
                const usageTrendOption = {
                    title: {
                        text: '模版使用趋势',
                        left: 'center',
                        textStyle: {
                            color: '#E0E6F0'
                        }
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: ['使用次数', '评分平均值'],
                        textStyle: {
                            color: '#A0A8B8'
                        },
                        bottom: 0
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: ['2025-03-15', '2025-03-20', '2025-03-25', '2025-03-30', '2025-04-05'],
                        axisLine: {
                            lineStyle: {
                                color: '#2A3142'
                            }
                        },
                        axisLabel: {
                            color: '#A0A8B8'
                        }
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: '使用次数',
                            axisLine: {
                                lineStyle: {
                                    color: '#2A3142'
                                }
                            },
                            splitLine: {
                                lineStyle: {
                                    color: '#2A3142'
                                }
                            },
                            axisLabel: {
                                color: '#A0A8B8'
                            }
                        },
                        {
                            type: 'value',
                            name: '评分',
                            min: 0,
                            max: 100,
                            axisLine: {
                                lineStyle: {
                                    color: '#2A3142'
                                }
                            },
                            splitLine: {
                                show: false
                            },
                            axisLabel: {
                                color: '#A0A8B8'
                            }
                        }
                    ],
                    series: [
                        {
                            name: '使用次数',
                            type: 'line',
                            data: [2, 5, 3, 1, 1],
                            itemStyle: {
                                color: '#0096FF'
                            }
                        },
                        {
                            name: '评分平均值',
                            type: 'line',
                            yAxisIndex: 1,
                            data: [80, 82, 81, 85, 84],
                            itemStyle: {
                                color: '#00C48C'
                            }
                        }
                    ]
                };
                usageTrendChart.setOption(usageTrendOption);
            }
            
            // 监听窗口大小变化，调整图表尺寸
            window.addEventListener('resize', function() {
                usageChart.resize();
                weightChart.resize();
                previewChart.resize();
                if (document.getElementById('usage-trend-chart')) {
                    echarts.getInstanceByDom(document.getElementById('usage-trend-chart')).resize();
                }
            });
        });

        // 选项卡切换功能
        const tabButtons = document.querySelectorAll('.detail-nav-tab');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');
                
                // 移除所有标签页的激活状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.style.display = 'none');
                
                // 激活当前选中的标签页
                this.classList.add('active');
                document.getElementById(`${tabId}-content`).style.display = 'block';
            });
        });

        // 编辑模式切换功能
        const editModeBtn = document.getElementById('editModeBtn');
        const editModeBtns = document.querySelectorAll('.edit-mode-btn');
        const nodeActions = document.querySelectorAll('.node-actions');
        const tagCloseIcons = document.querySelectorAll('.tag-close');
        const addTagBtn = document.querySelector('.add-tag-btn');
        const descriptionText = document.querySelector('.description-text');
        const descriptionEdit = document.querySelector('.description-edit');
        const treeToolbar = document.querySelector('.tree-toolbar');
        
        let editModeActive = false;
        
        editModeBtn.addEventListener('click', function() {
            editModeActive = !editModeActive;
            
            if (editModeActive) {
                this.innerHTML = '<i class="fas fa-save"></i> 保存更改';
                this.classList.add('primary');
                
                // 显示所有编辑按钮
                editModeBtns.forEach(btn => btn.style.display = 'inline-flex');
                nodeActions.forEach(action => action.style.display = 'flex');
                addTagBtn.style.display = 'flex';
                
                // 切换到编辑文本框
                descriptionText.style.display = 'none';
                descriptionEdit.style.display = 'block';
                
                // 显示树编辑器工具栏
                if (treeToolbar) treeToolbar.style.display = 'flex';
                
                // 启用所有禁用的输入控件
                document.querySelectorAll('input[disabled], select[disabled], textarea[disabled]').forEach(input => {
                    input.disabled = false;
                });
                
                document.querySelector('.tags-container').classList.add('editable');
            } else {
                this.innerHTML = '<i class="fas fa-edit"></i> 编辑模版';
                this.classList.remove('primary');
                
                // 隐藏所有编辑按钮
                editModeBtns.forEach(btn => btn.style.display = 'none');
                nodeActions.forEach(action => action.style.display = 'none');
                addTagBtn.style.display = 'none';
                
                // 切换回显示文本
                descriptionText.style.display = 'block';
                descriptionEdit.style.display = 'none';
                
                // 隐藏树编辑器工具栏
                if (treeToolbar) treeToolbar.style.display = 'none';
                
                // 禁用所有输入控件
                document.querySelectorAll('input:not([disabled]), select:not([disabled]), textarea:not([disabled])').forEach(input => {
                    if (!input.classList.contains('search-input')) {
                        input.disabled = true;
                    }
                });
                
                document.querySelector('.tags-container').classList.remove('editable');
            }
        });

        // 树形结构折叠/展开功能
        const nodeCollapseIcons = document.querySelectorAll('.node-collapse');
        
        nodeCollapseIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                const nodeContent = this.closest('.node-content');
                const nodeChildren = nodeContent.nextElementSibling;
                
                if (nodeChildren && nodeChildren.classList.contains('node-children')) {
                    if (nodeChildren.style.display === 'none') {
                        nodeChildren.style.display = 'block';
                        this.innerHTML = '<i class="fas fa-caret-down"></i>';
                    } else {
                        nodeChildren.style.display = 'none';
                        this.innerHTML = '<i class="fas fa-caret-right"></i>';
                    }
                }
            });
        });

        // 打开/关闭模态窗口
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        // 全局点击事件监听，关闭模态窗口
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });
        });
        
        // 阻止模态内容点击事件冒泡
        const modalContents = document.querySelectorAll('.modal');
        modalContents.forEach(content => {
            content.addEventListener('click', function(event) {
                event.stopPropagation();
            });
        });
    </script>

    <!-- 编辑模式切换脚本 -->
    <script>
        // 主题切换功能初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 主题切换功能初始化
            const themeSwitch = document.querySelector('.theme-switch');
            const htmlRoot = document.documentElement;
            
            // 检查本地存储中的主题设置，默认为暗色主题
            const savedTheme = localStorage.getItem('theme');
            // 只有当明确保存了"light"时才应用亮色主题
            if (savedTheme === 'light') {
                htmlRoot.classList.add('light-theme');
            } else {
                // 如果没有保存或者保存的不是"light"，则确保是暗色主题
                localStorage.setItem('theme', 'dark');
            }
            
            // 切换主题的函数
            function toggleTheme() {
                if (htmlRoot.classList.contains('light-theme')) {
                    htmlRoot.classList.remove('light-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    htmlRoot.classList.add('light-theme');
                    localStorage.setItem('theme', 'light');
                }
            }
            
            // 为主题切换按钮添加点击事件
            themeSwitch.addEventListener('click', toggleTheme);
        });
        
        // 编辑模式切换脚本
        // ... existing code ...
    </script>
</body>
</html>
