<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘 - 城市级关基级联的网络仿真度评估系统</title>
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Echarts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入中国地图数据 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/map/js/china.js"></script>
    <style>
        /* 页面整体放大120% */
        html {
            zoom: 110%;
        }
        
        :root {
            --primary-color: #0096FF;
            --secondary-color: #00E0FF;
            --background-dark: #0F1520;
            --background-card: #1A202E;
            --text-color: #E0E6F0;
            --text-secondary: #A0A8B8;
            --border-color: #2A3142;
            --highlight-color: #3E9BFF;
            --success-color: #00C48C;
            --warning-color: #FFB946;
            --danger-color: #F25767;
            --info-color: #0095FF;
        }
        
        /* 亮色主题变量 */
        :root.light-theme {
            --primary-color: #0078D4;
            --secondary-color: #00A2E0;
            --background-dark: #FFFFFF;
            --background-card: #FFFFFF;
            --text-color: #333333;
            --text-secondary: #666666;
            --border-color: #E0E6ED;
            --highlight-color: #106EBE;
            --success-color: #0A9D5A;
            --warning-color: #F29D41;
            --danger-color: #E74C3C;
            --info-color: #0078D4;
        }
        
        /* Logo样式 */
        .logo {
	zoom: 110%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 10px;
        }

        .logo-icon {
            font-size: 24px;
            color: var(--primary-color);
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 2px rgba(0, 224, 255, 0.4));
        }

        .logo span {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 0.5px;
            background: linear-gradient(90deg, var(--text-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: var(--background-dark);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            min-height: 100vh;
        }

        /* 顶部导航栏样式 */
        .header {
            background-color: rgba(16, 22, 36, 0.95);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 60px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .nav-item {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .nav-link {
            color: var(--text-color);
            text-decoration: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link.active {
            color: var(--primary-color);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 0 0 6px 6px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            border-bottom: 1px solid var(--border-color);
        }
		
		.dropdown-menu {
			list-style-type: none; /* 去掉前面的点 */
		}

        .dropdown-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px;
        }

        .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.1);
            color: var(--primary-color);
            padding-left: 20px;
        }

        .dropdown-icon {
            margin-right: 8px;
        }

        .nav-tools {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        /* 主题切换开关样式 */
        .theme-switch {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 60px;
            height: 30px;
            background: rgba(0, 149, 255, 0.15);
            border-radius: 30px;
            padding: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .theme-switch:hover {
            transform: scale(1.05);
        }
        
        .theme-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-size: 14px;
        }
        
        .theme-switch-slider {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            left: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        :root.light-theme .theme-switch-slider {
            transform: translateX(30px);
        }

        .notification-icon, .user-profile {
            position: relative;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .notification-icon:hover, .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-profile img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 24px;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .welcome-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .welcome-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .welcome-text h2 {
            font-size: 24px;
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 4px;
        }

        .welcome-text p {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .dashboard-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.1);
            border-color: var(--primary-color);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: var(--highlight-color);
        }

        /* 统计卡片区域 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background-color: var(--background-card);
            border-radius: 10px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 5px 0 0 5px;
        }

        .stat-card.success::before {
            background-color: var(--success-color);
        }

        .stat-card.warning::before {
            background-color: var(--warning-color);
        }

        .stat-card.danger::before {
            background-color: var(--danger-color);
        }

        .stat-card.info::before {
            background-color: var(--info-color);
        }

        .stat-card.primary::before {
            background-color: var(--primary-color);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
        }

        .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-description {
            font-size: 13px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-trend-up {
            color: var(--success-color);
        }

        .stat-trend-down {
            color: var(--danger-color);
        }

        /* 可点击的统计卡片样式 */
        .clickable-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clickable-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 12px 32px rgba(0, 149, 255, 0.2);
            border-color: var(--primary-color);
        }

        .clickable-card:active {
            transform: translateY(-4px) scale(1.01);
        }

        /* 仪表盘网格布局 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }

        .dashboard-left-column {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .dashboard-right-column {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        /* 卡片通用样式 */
        .dashboard-card {
            background-color: var(--background-card);
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .card-header {
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .card-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .card-action:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
        }

        .card-body {
            padding: 20px;
        }

        /* 仿真趋势图卡片 */
        .simulation-trend-card {
            height: 360px;
        }

        .simulation-chart-container {
            width: 100%;
            height: 280px;
        }

        /* 任务状态卡片 */
        .task-status-card {
            height: 360px;
        }

        .task-chart-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: 280px;
        }

        .task-donut-chart, .task-bar-chart {
            height: 100%;
            width: 100%;
        }

        /* 快捷入口卡片 */
        .quick-access-card {
            height: 360px;
        }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .quick-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16px;
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: var(--text-color);
        }

        .quick-link:hover {
            transform: translateY(-3px);
            border-color: var(--primary-color);
            background-color: rgba(0, 149, 255, 0.05);
        }

        .quick-link-icon {
            width: 42px;
            height: 42px;
            border-radius: 8px;
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 12px;
        }

        .quick-link-title {
            font-size: 13px;
            font-weight: 500;
            text-align: center;
        }

        /* 最近活动卡片 */
        .recent-activity-card {
            height: 360px;
        }

        .activity-timeline {
            position: relative;
            padding-left: 24px;
            height: calc(100% - 40px);
            overflow-y: auto;
        }

        .activity-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 8px;
            height: 100%;
            width: 2px;
            background-color: var(--border-color);
        }

        .activity-item {
            position: relative;
            padding-bottom: 20px;
        }

        .activity-item:last-child {
            padding-bottom: 0;
        }

        .activity-dot {
            position: absolute;
            left: -24px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 3px solid var(--background-card);
            z-index: 1;
        }

        .activity-item.success .activity-dot {
            background-color: var(--success-color);
        }

        .activity-item.warning .activity-dot {
            background-color: var(--warning-color);
        }

        .activity-item.danger .activity-dot {
            background-color: var(--danger-color);
        }

        .activity-content {
            padding-bottom: 12px;
            border-bottom: 1px dashed var(--border-color);
        }

        .activity-item:last-child .activity-content {
            border-bottom: none;
        }

        .activity-title {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .activity-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 通知卡片 */
        .notifications-card {
            height: 360px;
        }

        .notification-list {
            list-style: none;
            margin: 0;
            padding: 0;
            height: calc(100% - 40px);
            overflow-y: auto;
        }

        .notification-item {
            display: flex;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-icon-wrapper {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(0, 149, 255, 0.1);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .notification-item.success .notification-icon-wrapper {
            background-color: rgba(0, 196, 140, 0.1);
            color: var(--success-color);
        }

        .notification-item.warning .notification-icon-wrapper {
            background-color: rgba(255, 185, 70, 0.1);
            color: var(--warning-color);
        }

        .notification-item.danger .notification-icon-wrapper {
            background-color: rgba(242, 87, 103, 0.1);
            color: var(--danger-color);
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .notification-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 响应式设计 */
        @media screen and (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 768px) {
            .stats-cards {
                grid-template-columns: 1fr;
            }

            .task-chart-container {
                grid-template-columns: 1fr;
            }

            .quick-links {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 576px) {
            .welcome-section {
                flex-direction: column;
                align-items: flex-start;
            }

            .dashboard-actions {
                flex-direction: column;
                width: 100%;
            }

            .action-btn {
                width: 100%;
            }
        }

        /* 动画与过渡效果 */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 149, 255, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(0, 149, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 149, 255, 0);
            }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: var(--background-card);
            border-radius: 8px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
            font-size: 14px;
        }

        .radio-group {
            display: flex;
            gap: 20px;
        }

        .radio-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .radio-label input[type="radio"] {
            margin: 0;
        }

        .form-select {
            width: 100%;
            padding: 8px 12px;
            background-color: var(--background-dark);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            font-size: 14px;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: none;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--highlight-color);
        }

        .btn-secondary {
            background-color: var(--background-dark);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        /* 亮色主题的导航栏调整 */
        :root.light-theme .header {
            background-color: rgba(245, 247, 250, 0.95);
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .nav-link:hover {
            color: var(--primary-color);
        }
        
        :root.light-theme .dropdown-menu {
            background-color: var(--background-card);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .dropdown-item:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        /* 亮色主题的卡片调整 */
        :root.light-theme .dashboard-card {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .card-header {
            border-bottom: 1px solid var(--border-color);
        }
        
        :root.light-theme .card-action:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        /* 亮色主题的统计卡片 */
        :root.light-theme .stat-card {
            background-color: var(--background-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        :root.light-theme .stat-card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .stat-icon {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .stat-card.success .stat-icon {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .stat-card.warning .stat-icon {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .stat-card.danger .stat-icon {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        /* 亮色主题的按钮 */
        :root.light-theme .action-btn {
            background-color: #F5F7FA;
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .action-btn:hover {
            background-color: rgba(62, 155, 255, 0.05);
        }
        
        :root.light-theme .action-btn.primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .action-btn.primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        :root.light-theme .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        :root.light-theme .btn-primary:hover {
            background-color: var(--highlight-color);
            border-color: var(--highlight-color);
        }
        
        :root.light-theme .btn-secondary {
            background-color: #F0F2F5;
        }
        
        :root.light-theme .btn-secondary:hover {
            background-color: #E8EBF0;
        }
        
        /* 亮色主题的时间线 */
        :root.light-theme .activity-timeline::before {
            background-color: #E0E6ED;
        }
        
        :root.light-theme .activity-dot {
            border: 3px solid #FFFFFF;
        }
        
        :root.light-theme .activity-content {
            border-bottom: 1px dashed #E0E6ED;
        }
        
        /* 亮色主题的通知列表 */
        :root.light-theme .notification-item {
            border-bottom: 1px solid #E0E6ED;
        }
        
        :root.light-theme .notification-icon-wrapper {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        :root.light-theme .notification-item.success .notification-icon-wrapper {
            background-color: rgba(0, 196, 140, 0.08);
        }
        
        :root.light-theme .notification-item.warning .notification-icon-wrapper {
            background-color: rgba(255, 185, 70, 0.08);
        }
        
        :root.light-theme .notification-item.danger .notification-icon-wrapper {
            background-color: rgba(242, 87, 103, 0.08);
        }
        
        /* 亮色主题的快捷链接 */
        :root.light-theme .quick-link {
            background-color: rgba(0, 0, 0, 0.01);
            border: 1px solid var(--border-color);
        }
        
        :root.light-theme .quick-link:hover {
            background-color: rgba(0, 149, 255, 0.05);
        }
        
        :root.light-theme .quick-link-icon {
            background-color: rgba(0, 149, 255, 0.08);
        }
        
        /* 亮色主题的弹窗 */
        :root.light-theme .modal-content {
            background-color: var(--background-card);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme .form-select {
            background-color: #F8FAFD;
            border: 1px solid var(--border-color);
        }
        
        /* 亮色主题的滚动条 */
        :root.light-theme ::-webkit-scrollbar-track {
            background: #F5F7FA;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb {
            background: #D0D6E0;
        }
        
        :root.light-theme ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 亮色主题下的图表样式调整 */
        :root.light-theme #task-donut-chart canvas,
        :root.light-theme #task-bar-chart canvas,
        :root.light-theme #simulation-trend-chart canvas,
        :root.light-theme #scene-distribution-chart canvas {
            background-color: var(--background-card);
        }
        
        /* 确保在亮色主题下图表的文字颜色适应背景 */
        :root.light-theme .echarts-tooltip,
        :root.light-theme .echarts-label {
            color: var(--text-color) !important;
        }

        /* 评估场景分布卡片样式 */
        .scene-distribution-card {
            height: 360px;
        }

        .scene-chart {
            width: 100%;
            height: 280px;
        }
    </style>
    
<body>
    <div class="container">

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 仪表盘头部 -->
            <div class="dashboard-header">
                <div class="dashboard-actions">
                    <button class="action-btn">
                        <i class="fas fa-sync-alt"></i> 刷新数据
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-cog"></i> 仪表盘设置
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-cards">
                <div class="stat-card info clickable-card" onclick="window.location.href='0.Home.html'">
                    <div class="stat-header">
                        <div class="stat-title">评估任务总数</div>
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                    </div>
                    <div class="stat-value">50</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长12个</span>
                    </div>
                </div>
                <div class="stat-card primary">
                    <div class="stat-header">
                        <div class="stat-title">进行中</div>
                        <div class="stat-icon">
                            <i class="fas fa-spinner"></i>
                        </div>
                    </div>
                    <div class="stat-value">15</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长8个</span>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-header">
                        <div class="stat-title">已评估</div>
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="stat-value">20</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增长5个</span>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-header">
                        <div class="stat-title">待评估</div>
                        <div class="stat-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                    </div>
                    <div class="stat-value">15</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-down stat-trend-down"></i>
                        <span>较上月减少5个</span>
                    </div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-header">
                        <div class="stat-title">测评模版总数</div>
                        <div class="stat-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                    </div>
                    <div class="stat-value">42</div>
                    <div class="stat-description">
                        <i class="fas fa-arrow-up stat-trend-up"></i>
                        <span>较上月增加3个</span>
                    </div>
                </div>
            </div>

            <!-- 仪表盘网格内容 -->
            <div class="dashboard-grid">
                <!-- 左侧内容 -->
                <div class="dashboard-left-column">
                    <!-- 评估任务状态分布 -->
                    <div class="dashboard-card task-status-card">
                        <div class="card-header">
                            <div class="card-title">评估任务状态分布</div>
                            <div class="card-actions">
                                <button class="card-action"><i class="fas fa-sync-alt"></i></button>
                                <button class="card-action"><i class="fas fa-expand"></i></button>
                                <button class="card-action"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="task-chart-container">
                                <div id="task-donut-chart" class="task-donut-chart"></div>
                                <div id="task-bar-chart" class="task-bar-chart"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 单行业仿真度评估次数趋势分析 -->
                    <div class="dashboard-card simulation-trend-card">
                        <div class="card-header">
                            <div class="card-title">单行业仿真度评估次数趋势分析</div>
                            <div class="card-actions">
                                <button class="card-action"><i class="fas fa-sync-alt"></i></button>
                                <button class="card-action"><i class="fas fa-expand"></i></button>
                                <button class="card-action"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="simulation-trend-chart" class="simulation-chart-container"></div>
                        </div>
                    </div>
                </div>

                <!-- 右侧内容 -->
                <div class="dashboard-right-column">
                    <!-- 评估场景分布 -->
                    <div class="dashboard-card scene-distribution-card">
                        <div class="card-header">
                            <div class="card-title">评估场景分布</div>
                            <div class="card-actions">
                                <button class="card-action"><i class="fas fa-sync-alt"></i></button>
                                <button class="card-action"><i class="fas fa-expand"></i></button>
                                <button class="card-action"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="scene-distribution-chart" class="scene-chart"></div>
                        </div>
                    </div>
                    
                    <!-- 最近活动 -->
                    <div class="dashboard-card recent-activity-card">
                        <div class="card-header">
                            <div class="card-title">最近活动</div>
                            <div class="card-actions">
                                <button class="card-action"><i class="fas fa-sync-alt"></i></button>
                                <button class="card-action"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="activity-timeline">
                                <div class="activity-item">
                                    <div class="activity-dot"></div>
                                    <div class="activity-content">
                                        <div class="activity-title">您创建了新的评估任务"供水系统压力仿真评估-2025年Q1"</div>
                                        <div class="activity-time">今天 09:32</div>
                                    </div>
                                </div>
                                <div class="activity-item warning">
                                    <div class="activity-dot"></div>
                                    <div class="activity-content">
                                        <div class="activity-title">小刘更新了"交通流量模拟"算法参数</div>
                                        <div class="activity-time">昨天 14:23</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-dot"></div>
                                    <div class="activity-content">
                                        <div class="activity-title">您审核通过了"燃气管网压力监测"数据采集任务</div>
                                        <div class="activity-time">04-02 11:05</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-dot"></div>
                                    <div class="activity-content">
                                        <div class="activity-title">系统管理员发布了新版测评模版</div>
                                        <div class="activity-time">04-01 08:30</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新建评估任务弹窗 -->
    <div class="modal" id="newEvalTaskModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新建评估任务</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 新增评估对象选择 -->
                <div class="form-group">
                    <label>选择评估场景</label>
                    <select class="form-select" id="assessmentSceneSelect">
                        <option value="">请选评估场景</option>
                        <option value="power">中小企业网场景</option>
                        <option value="transport">轨道交通场景</option>
                        <option value="communication">危险气体缓冲工艺场景</option>
                        <option value="water">通信-电力级联风险场景</option>
                        <option value="gas">通信-交通级联风险场景</option>
                        <option value="power-transport">电力-交通级联风险场景</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>选择创建方式</label>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="createType" value="template" checked>
                            <span>从模版创建</span>
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="createType" value="empty">
                            <span>创建空的评估任务</span>
                        </label>
                    </div>
                </div>
                <div class="form-group template-select" id="templateSelectGroup">
                    <label>选择模版</label>
                    <select class="form-select">
                        <option value="">请选择模版</option>
                        <option value="template1">通信-交通级联风险仿真评估模版</option>
                        <option value="template1">电力系统稳态仿真评估模版</option>
                        <option value="template2">交通流量模拟评估模版</option>
                        <option value="template3">供水系统压力仿真评估模版</option>
                        <option value="template4">燃气管网压力监测模版</option>
                    </select>
                </div>
                
                <!-- 新增仿真基本信息表单字段 -->
                <div class="form-group simulation-info" id="simulationInfoGroup" style="display: none;">
                    <label for="simName">评估任务名称</label>
                    <input type="text" id="simName" class="form-select" placeholder="请输入仿真名称">
                </div>
                
                <div class="form-group simulation-info" id="simulationTypeGroup" style="display: none;">
                    <label for="simType">评估任务类型</label>
                    <select id="simType" class="form-select">
                        <option value="">请选择评估任务类型</option>
                        <option value="steady">跨行业级联风险仿真度评估</option>
                        <option value="dynamic">电力行业仿真度评估</option>
                        <option value="monte-carlo">通信行业仿真度评估</option>
                        <option value="scenario">交通行业仿真度评估</option>
                        <option value="cascade">燃气行业仿真度评估</option>
                    </select>
                </div>
                
                <div class="form-group simulation-info" id="simulationDescGroup" style="display: none;">
                    <label for="simDescription">描述信息</label>
                    <textarea id="simDescription" class="form-select" rows="3" placeholder="请输入描述信息"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelBtn">取消</button>
                <button class="btn btn-primary" id="confirmBtn">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 主题切换功能初始化
            const themeSwitch = document.querySelector('.theme-switch');
            const htmlRoot = document.documentElement;
            
            // 检查本地存储中的主题设置，默认为暗色主题
            const savedTheme = localStorage.getItem('theme');
            // 只有当明确保存了"light"时才应用亮色主题
            if (savedTheme === 'light') {
                htmlRoot.classList.add('light-theme');
            } else {
                // 如果没有保存或者保存的不是"light"，则确保是暗色主题
                localStorage.setItem('theme', 'dark');
            }
            
            // 切换主题的函数
            function toggleTheme() {
                if (htmlRoot.classList.contains('light-theme')) {
                    htmlRoot.classList.remove('light-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    htmlRoot.classList.add('light-theme');
                    localStorage.setItem('theme', 'light');
                }
                
                // 主题切换后需要重新渲染图表
                setTimeout(() => {
                    // 更新图表主题配色
                    updateChartsTheme();
                    
                    // 重新调整图表大小
                    simulationChart.resize();
                    taskDonutChart.resize();
                    taskBarChart.resize();
                    sceneDistributionChart.resize();
                }, 200);
            }
            
            // 为主题切换按钮添加点击事件
            themeSwitch.addEventListener('click', toggleTheme);
            
            // 图表对象声明
            let simulationChart, taskDonutChart, taskBarChart, sceneDistributionChart;
            
            // 根据当前主题更新图表配色
            function updateChartsTheme() {
                const isLightTheme = htmlRoot.classList.contains('light-theme');
                const textColor = isLightTheme ? '#333333' : '#A0A8B8';
                const axisLineColor = isLightTheme ? '#E0E6ED' : '#2A3142';
                const splitLineColor = isLightTheme ? '#E8EBF0' : '#2A3142';
                
                // 重新应用配色方案到各个图表
                simulationOption.legend.textStyle.color = textColor;
                simulationOption.xAxis.axisLine.lineStyle.color = axisLineColor;
                simulationOption.xAxis.axisLabel.color = textColor;
                simulationOption.yAxis[0].axisLine.lineStyle.color = axisLineColor;
                simulationOption.yAxis[0].axisLabel.color = textColor;
                simulationOption.yAxis[0].splitLine.lineStyle.color = splitLineColor;
                
                taskDonutOption.legend.textStyle.color = textColor;
                if (isLightTheme) {
                    taskDonutOption.series[0].itemStyle.borderColor = '#FFFFFF';
                } else {
                    taskDonutOption.series[0].itemStyle.borderColor = '#1A202E';
                }
                
                taskBarOption.xAxis.axisLine.lineStyle.color = axisLineColor;
                taskBarOption.xAxis.axisLabel.color = textColor;
                taskBarOption.xAxis.splitLine.lineStyle.color = splitLineColor;
                taskBarOption.yAxis.axisLine.lineStyle.color = axisLineColor;
                taskBarOption.yAxis.axisLabel.color = textColor;
                
                sceneDistributionOption.legend.textStyle.color = textColor;
                if (isLightTheme) {
                    sceneDistributionOption.series[0].itemStyle.borderColor = '#FFFFFF';
                } else {
                    sceneDistributionOption.series[0].itemStyle.borderColor = '#1A202E';
                }
                
                // 重新设置选项
                simulationChart.setOption(simulationOption);
                taskDonutChart.setOption(taskDonutOption);
                taskBarChart.setOption(taskBarOption);
                sceneDistributionChart.setOption(sceneDistributionOption);
            }
            
            // 单行业仿真度评估次数趋势图表
            simulationChart = echarts.init(document.getElementById('simulation-trend-chart'));
            const simulationOption = {
                grid: {
                    left: '3%',
                    right: '5%',
                    bottom: '3%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        let total = 0;
                        let result = params[0].axisValueLabel + '<br/>';
                        
                        // 先计算总和
                        for(let i = 0; i < 5; i++) {
                            if(params[i]) {
                                total += params[i].value;
                            }
                        }
                        
                        // 输出各行业数据
                        for(let i = 0; i < params.length; i++) {
                            const item = params[i];
                            const colorSpan = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + item.color + '"></span>';
                            
                            if(i < 5) { // 柱状图数据
                                result += colorSpan + item.seriesName + ': ' + item.value + '<br/>';
                            } else { // 总数据线
                                result += '<br/>' + colorSpan + '<strong>' + item.seriesName + ': ' + item.value + '</strong><br/>';
                            }
                        }
                        
                        return result;
                    }
                },
                legend: {
                    data: ['电力系统', '交通系统', '水利系统', '燃气系统', '通信系统', '总评估次数'],
                    textStyle: {
                        color: '#A0A8B8'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    axisLine: {
                        lineStyle: {
                            color: '#2A3142'
                        }
                    },
                    axisLabel: {
                        color: '#A0A8B8'
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '测评次数',
                        nameTextStyle: {
                            color: '#A0A8B8'
                        },
                        min: 0,
                        max: 150,
                        interval: 30,
                        axisLine: {
                            lineStyle: {
                                color: '#2A3142'
                            }
                        },
                        axisLabel: {
                            color: '#A0A8B8'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#2A3142',
                                type: 'dashed'
                            }
                        }
                    }
                ],
                series: [
                    {
                        name: '电力系统',
                        type: 'bar',
                        stack: '总量',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [28, 22, 30, 34, 29, 25, 20, 33, 35, 31, 28, 32]
                    },
                    {
                        name: '交通系统',
                        type: 'bar',
                        stack: '总量',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [15, 17, 20, 24, 27, 25, 22, 18, 16, 21, 23, 25]
                    },
                    {
                        name: '水利系统',
                        type: 'bar',
                        stack: '总量',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [12, 14, 16, 18, 20, 22, 23, 19, 17, 15, 13, 14]
                    },
                    {
                        name: '燃气系统',
                        type: 'bar',
                        stack: '总量',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [18, 16, 15, 13, 16, 19, 22, 24, 21, 18, 20, 23]
                    },
                    {
                        name: '通信系统',
                        type: 'bar',
                        stack: '总量',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [10, 12, 15, 18, 22, 25, 28, 26, 23, 20, 18, 16]
                    },
                    {
                        name: '总评估次数',
                        type: 'line',
                        smooth: true,
                        lineStyle: {
                            width: 3,
                            color: '#FFB946'
                        },
                        symbol: 'circle',
                        symbolSize: 8,
                        itemStyle: {
                            color: '#FFB946'
                        },
                        data: [83, 81, 96, 107, 114, 116, 115, 120, 112, 105, 102, 110],
                        z: 5,
                        emphasis: {
                            focus: 'series',
                            itemStyle: {
                                borderWidth: 2,
                                borderColor: '#fff'
                            }
                        }
                    }
                ],
                color: ['#0095FF', '#00C48C', '#FFB946', '#F25767', '#9760FF']
            };
            simulationChart.setOption(simulationOption);

            // 任务状态饼图
            taskDonutChart = echarts.init(document.getElementById('task-donut-chart'));
            const taskDonutOption = {
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    textStyle: {
                        color: '#A0A8B8'
                    }
                },
                series: [
                    {
                        name: '任务状态',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#1A202E',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 16,
                                fontWeight: 'bold',
                                color: '#E0E6F0'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 48, name: '已完成' },
                            { value: 32, name: '进行中' },
                            { value: 12, name: '已暂停' },
                            { value: 8, name: '已失败' }
                        ]
                    }
                ],
                color: ['#00C48C', '#0095FF', '#FFB946', '#F25767']
            };
            taskDonutChart.setOption(taskDonutOption);

            // 任务状态柱状图
            taskBarChart = echarts.init(document.getElementById('task-bar-chart'));
            const taskBarOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#2A3142'
                        }
                    },
                    axisLabel: {
                        color: '#A0A8B8'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#2A3142',
                            type: 'dashed'
                        }
                    }
                },
                yAxis: {
                    type: 'category',
                    data: ['通信行业', '政务行业', '交通行业', '电力行业', '通信-电力级联'],
                    axisLine: {
                        lineStyle: {
                            color: '#2A3142'
                        }
                    },
                    axisLabel: {
                        color: '#A0A8B8'
                    }
                },
                series: [
                    {
                        name: '已完成',
                        type: 'bar',
                        stack: '总量',
                        label: {
                            show: false
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: [12, 14, 8, 6, 8, 3]
                    },
                    {
                        name: '进行中',
                        type: 'bar',
                        stack: '总量',
                        label: {
                            show: false
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: [8, 6, 6, 7, 5, 2]
                    },
                    {
                        name: '已暂停',
                        type: 'bar',
                        stack: '总量',
                        label: {
                            show: false
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: [3, 2, 4, 2, 1, 4]
                    },
                    {
                        name: '已失败',
                        type: 'bar',
                        stack: '总量',
                        label: {
                            show: false
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: [1, 2, 1, 3, 1, 8]
                    }
                ],
                color: ['#00C48C', '#0095FF', '#FFB946', '#F25767']
            };
            taskBarChart.setOption(taskBarOption);

            // 初始化评估场景分布图表
            sceneDistributionChart = echarts.init(document.getElementById('scene-distribution-chart'));
            const sceneDistributionOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    textStyle: {
                        color: '#A0A8B8'
                    }
                },
                series: [
                    {
                        name: '评估场景',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#1A202E',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 16,
                                fontWeight: 'bold',
                                color: '#E0E6F0'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 35, name: '中小企业网场景' },
                            { value: 25, name: '轨道交通场景' },
                            { value: 20, name: '危险气体缓冲工艺场景' },
                            { value: 15, name: '通信-电力级联风险场景' },
                            { value: 5, name: '通信-交通级联风险场景' }
                        ]
                    }
                ],
                color: ['#0095FF', '#00C48C', '#FFB946', '#F25767', '#9760FF']
            };
            sceneDistributionChart.setOption(sceneDistributionOption);

            // 窗口大小变化时自动调整图表尺寸
            window.addEventListener('resize', function() {
                simulationChart.resize();
                taskDonutChart.resize();
                taskBarChart.resize();
                sceneDistributionChart.resize();
            });
            
            // 初始化后应用当前主题配色
            updateChartsTheme();
        });

        // 模拟通知弹出
        document.querySelector('.notification-icon').addEventListener('click', function() {
            alert('通知中心功能将在此处展开');
        });

        // 用户资料弹出
        document.querySelector('.user-profile').addEventListener('click', function() {
            alert('用户个人资料功能将在此处展开');
        });

        // 刷新按钮交互
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.innerHTML.includes('刷新数据')) {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新数据';
                        alert('数据已刷新！');
                    }, 1500);
                } else if (this.innerHTML.includes('仪表盘设置')) {
                    alert('仪表盘设置面板将在此处展开');
                }
            });
        });

        // 新建评估任务弹窗交互
        const newEvalTaskModal = document.getElementById('newEvalTaskModal');
        const createTypeRadios = document.querySelectorAll('input[name="createType"]');
        const templateSelectGroup = document.getElementById('templateSelectGroup');
        const confirmBtn = document.getElementById('confirmBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const closeBtn = document.querySelector('.close-btn');

        // 打开弹窗
        document.querySelector('.action-btn.primary').addEventListener('click', function() {
            newEvalTaskModal.style.display = 'flex';
        });

        // 关闭弹窗
        function closeModal() {
            newEvalTaskModal.style.display = 'none';
        }

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // 点击弹窗外部关闭
        newEvalTaskModal.addEventListener('click', function(e) {
            if (e.target === newEvalTaskModal) {
                closeModal();
            }
        });

        // 创建方式切换
        createTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'empty') {
                    templateSelectGroup.style.display = 'none';
                    document.querySelectorAll('.simulation-info').forEach(el => {
                        el.style.display = 'block';
                    });
                } else {
                    templateSelectGroup.style.display = 'block';
                    document.querySelectorAll('.simulation-info').forEach(el => {
                        el.style.display = 'none';
                    });
                }
            });
        });

        // 确认按钮点击事件
        confirmBtn.addEventListener('click', function() {
            const selectedScene = document.getElementById('assessmentSceneSelect').value;
            if (!selectedScene) {
                alert('请选择评估场景');
                return;
            }

            const selectedType = document.querySelector('input[name="createType"]:checked').value;
            
            if (selectedType === 'empty') {
                // 创建空的评估任务，调用模版库列表中的新建模版弹窗
                alert('将跳转到新建模版页面');
                closeModal();
            } else {
                const selectedTemplate = document.querySelector('.form-select').value;
                if (!selectedTemplate) {
                    alert('请选择模版');
                    return;
                }
                // 从模版创建评估任务
                alert(`将从模版 ${selectedTemplate} 创建评估任务`);
                closeModal();
            }
        });
    </script>
</body>
</html>