div, span, a, p, ul, ol, li, h1, h2, h3, i, input, textarea, body, html {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    letter-spacing: .5px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-tap-highlight-color: transparent;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
    word-wrap: break-word;
    word-break: break-all;
}
.jv-node{position:relative}.jv-node:after{content:','}.jv-node:last-of-type:after{content:''}.jv-node.toggle{margin-left:13px !important}.jv-node .jv-node{margin-left:25px}
.jv-container{box-sizing:border-box;position:relative}.jv-container.boxed{border:1px solid #eee;border-radius:6px}.jv-container.boxed:hover{box-shadow:0 2px 7px rgba(0,0,0,0.15);border-color:transparent;position:relative}.jv-container.jv-light{background:#fff;white-space:nowrap;color:#525252;font-size:14px;font-family:Consolas, Menlo, Courier, monospace}.jv-container.jv-light .jv-ellipsis{color:#999;background-color:#eee;display:inline-block;line-height:0.9;font-size:0.9em;padding:0px 4px 2px 4px;margin:0 4px;border-radius:3px;vertical-align:2px;cursor:pointer;-webkit-user-select:none;user-select:none}.jv-container.jv-light .jv-button{color:#49b3ff}.jv-container.jv-light .jv-key{color:#111111;margin-right:4px}.jv-container.jv-light .jv-item.jv-array{color:#111111}.jv-container.jv-light .jv-item.jv-boolean{color:#fc1e70}.jv-container.jv-light .jv-item.jv-function{color:#067bca}.jv-container.jv-light .jv-item.jv-number{color:#fc1e70}.jv-container.jv-light .jv-item.jv-object{color:#111111}.jv-container.jv-light .jv-item.jv-undefined{color:#e08331}.jv-container.jv-light .jv-item.jv-string{color:#42b983;word-break:break-word;white-space:normal}.jv-container.jv-light .jv-item.jv-string .jv-link{color:#0366d6}.jv-container.jv-light .jv-code .jv-toggle:before{padding:0px 2px;border-radius:2px}.jv-container.jv-light .jv-code .jv-toggle:hover:before{background:#eee}.jv-container .jv-code{overflow:hidden;padding:30px 20px}.jv-container .jv-code.boxed{max-height:300px}.jv-container .jv-code.open{max-height:initial !important;overflow:visible;overflow-x:auto;padding-bottom:45px}.jv-container .jv-toggle{background-image:url(data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9IjE2IiB3aWR0aD0iOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KIAo8cG9seWdvbiBwb2ludHM9IjAsMCA4LDggMCwxNiIKc3R5bGU9ImZpbGw6IzY2NjtzdHJva2U6cHVycGxlO3N0cm9rZS13aWR0aDowIiAvPgo8L3N2Zz4=);background-repeat:no-repeat;background-size:contain;background-position:center center;cursor:pointer;width:10px;height:10px;margin-right:2px;display:inline-block;-webkit-transition:-webkit-transform 0.1s;transition:-webkit-transform 0.1s;transition:transform 0.1s;transition:transform 0.1s, -webkit-transform 0.1s}.jv-container .jv-toggle.open{-webkit-transform:rotate(90deg);transform:rotate(90deg)}.jv-container .jv-more{position:absolute;z-index:1;bottom:0;left:0;right:0;height:40px;width:100%;text-align:center;cursor:pointer}.jv-container .jv-more .jv-toggle{position:relative;top:40%;z-index:2;color:#888;-webkit-transition:all 0.1s;transition:all 0.1s;-webkit-transform:rotate(90deg);transform:rotate(90deg)}.jv-container .jv-more .jv-toggle.open{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}.jv-container .jv-more:after{content:"";width:100%;height:100%;position:absolute;bottom:0;left:0;z-index:1;background:-webkit-linear-gradient(top, rgba(0,0,0,0) 20%, rgba(230,230,230,0.3) 100%);background:linear-gradient(to bottom, rgba(0,0,0,0) 20%, rgba(230,230,230,0.3) 100%);-webkit-transition:all 0.1s;transition:all 0.1s}.jv-container .jv-more:hover .jv-toggle{top:50%;color:#111}.jv-container .jv-more:hover:after{background:-webkit-linear-gradient(top, rgba(0,0,0,0) 20%, rgba(230,230,230,0.3) 100%);background:linear-gradient(to bottom, rgba(0,0,0,0) 20%, rgba(230,230,230,0.3) 100%)}.jv-container .jv-button{position:relative;cursor:pointer;display:inline-block;padding:5px;z-index:5}.jv-container .jv-button.copied{opacity:0.4;cursor:default}.jv-container .jv-tooltip{position:absolute}.jv-container .jv-tooltip.right{right:15px}.jv-container .jv-tooltip.left{left:15px}.jv-container .j-icon{font-size:12px}
@font-face {
    font-family: "xm-iconfont";
    src: url('//at.alicdn.com/t/font_792691_ptvyboo0bno.eot?t=1574048839056');
    /* IE9 */
    src: url('//at.alicdn.com/t/font_792691_ptvyboo0bno.eot?t=1574048839056#iefix') format('embedded-opentype'), /* IE6-IE8 */ url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'), url('//at.alicdn.com/t/font_792691_ptvyboo0bno.woff?t=1574048839056') format('woff'), url('//at.alicdn.com/t/font_792691_ptvyboo0bno.ttf?t=1574048839056') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */ url('//at.alicdn.com/t/font_792691_ptvyboo0bno.svg?t=1574048839056#iconfont') format('svg');
    /* iOS 4.1- */
    }
    .xm-iconfont {
    font-family: "xm-iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    }
    .xm-icon-quanxuan:before {
    content: "\e62c";
    }
    .xm-icon-caidan:before {
    content: "\e610";
    }
    .xm-icon-fanxuan:before {
    content: "\e837";
    }
    .xm-icon-pifu:before {
    content: "\e668";
    }
    .xm-icon-qingkong:before {
    content: "\e63e";
    }
    .xm-icon-sousuo:before {
    content: "\e600";
    }
    .xm-icon-danx:before {
    content: "\e62b";
    }
    .xm-icon-duox:before {
    content: "\e613";
    }
    .xm-icon-close:before {
    content: "\e601";
    }
    .xm-icon-expand:before {
    content: "\e641";
    }
    .xm-icon-banxuan:before {
    content: "\e60d";
    }
    </style><style type="text/css">@-webkit-keyframes xm-upbit {
    from {
      -webkit-transform: translate3d(0, 30px, 0);
      opacity: 0.3;
    }
    to {
      -webkit-transform: translate3d(0, 0, 0);
      opacity: 1;
    }
    }
    @keyframes xm-upbit {
    from {
      transform: translate3d(0, 30px, 0);
      opacity: 0.3;
    }
    to {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }
    }
    @-webkit-keyframes loader {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
    }
    @keyframes loader {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
    }
    xm-select {
    background-color: #FFF;
    position: relative;
    border: 1px solid #E6E6E6;
    border-radius: 2px;
    display: block;
    width: 100%;
    cursor: pointer;
    outline: none;
    }
    xm-select * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: 400;
    text-overflow: ellipsis;
    user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    }
    xm-select:hover,
    xm-select:focus {
    border-color: #C0C4CC;
    }
    xm-select > .xm-tips {
    color: #999999;
    padding: 0 10px;
    position: absolute;
    display: flex;
    height: 100%;
    align-items: center;
    }
    xm-select > .xm-icon {
    display: inline-block;
    overflow: hidden;
    position: absolute;
    width: 0;
    height: 0;
    right: 10px;
    top: 50%;
    margin-top: -3px;
    cursor: pointer;
    border: 6px dashed transparent;
    border-top-color: #C2C2C2;
    border-top-style: solid;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    }
    xm-select > .xm-icon-expand {
    margin-top: -9px;
    transform: rotate(180deg);
    }
    xm-select > .xm-label.single-row {
    position: absolute;
    top: 0;
    bottom: 0px;
    left: 0px;
    right: 30px;
    overflow: auto hidden;
    }
    xm-select > .xm-label.single-row .scroll {
    overflow-y: hidden;
    }
    xm-select > .xm-label.single-row .label-content {
    flex-wrap: nowrap;
    white-space: nowrap;
    }
    xm-select > .xm-label.auto-row .label-content {
    flex-wrap: wrap;
    padding-right: 30px !important;
    }
    xm-select > .xm-label.auto-row .xm-label-block > span {
    white-space: unset;
    height: 100%;
    }
    xm-select > .xm-label .scroll .label-content {
    display: flex;
    padding: 3px 10px;
    }
    xm-select > .xm-label .xm-label-block {
    display: flex;
    position: relative;
    padding: 0px 5px;
    margin: 2px 5px 2px 0;
    border-radius: 3px;
    align-items: baseline;
    color: #FFF;
    }
    xm-select > .xm-label .xm-label-block > span {
    display: flex;
    color: #FFF;
    white-space: nowrap;
    }
    xm-select > .xm-label .xm-label-block > i {
    color: #FFF;
    margin-left: 8px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    }
    xm-select > .xm-label .xm-label-block.disabled {
    background-color: #C2C2C2 !important;
    cursor: no-drop !important;
    }
    xm-select > .xm-label .xm-label-block.disabled > i {
    cursor: no-drop !important;
    }
    xm-select > .xm-body {
    position: absolute;
    left: 0;
    top: 42px;
    padding: 5px 0;
    z-index: 999;
    width: 100%;
    min-width: fit-content;
    border: 1px solid #E6E6E6;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
    animation-name: xm-upbit;
    animation-duration: 0.3s;
    animation-fill-mode: both;
    }
    xm-select > .xm-body .scroll-body {
    overflow-x: hidden;
    overflow-y: auto;
    }
    xm-select > .xm-body .scroll-body::-webkit-scrollbar {
    width: 8px;
    }
    xm-select > .xm-body .scroll-body::-webkit-scrollbar-track {
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    -ms-border-radius: 2em;
    border-radius: 2em;
    background-color: #FFF;
    }
    xm-select > .xm-body .scroll-body::-webkit-scrollbar-thumb {
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    -ms-border-radius: 2em;
    border-radius: 2em;
    background-color: #C2C2C2;
    }
    xm-select > .xm-body.up {
    top: auto;
    bottom: 42px;
    }
    xm-select > .xm-body.relative {
    position: relative;
    display: block !important;
    top: 0;
    box-shadow: none;
    border: none;
    animation-name: none;
    animation-duration: 0;
    min-width: 100%;
    }
    xm-select > .xm-body .xm-group {
    cursor: default;
    }
    xm-select > .xm-body .xm-group-item {
    display: inline-block;
    cursor: pointer;
    padding: 0 10px;
    color: #999;
    font-size: 12px;
    }
    xm-select > .xm-body .xm-option {
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 10px;
    cursor: pointer;
    }
    xm-select > .xm-body .xm-option-icon {
    color: transparent;
    display: flex;
    border: 1px solid #E6E6E6;
    border-radius: 3px;
    justify-content: center;
    align-items: center;
    }
    xm-select > .xm-body .xm-option-icon.xm-custom-icon {
    color: unset;
    border: unset;
    }
    xm-select > .xm-body .xm-option-icon-hidden {
    margin-right: -10px;
    }
    xm-select > .xm-body .xm-option-icon.xm-icon-danx {
    border-radius: 100%;
    }
    xm-select > .xm-body .xm-option-content {
    display: flex;
    position: relative;
    padding-left: 15px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    ;
    width: calc(100% - 20px);
    }
    xm-select > .xm-body .xm-option.hide-icon .xm-option-content {
    padding-left: 0;
    }
    xm-select > .xm-body .xm-option.selected.hide-icon .xm-option-content {
    color: #FFF !important;
    }
    xm-select > .xm-body .xm-option .loader {
    width: 0.8em;
    height: 0.8em;
    margin-right: 6px;
    color: #C2C2C2;
    }
    xm-select > .xm-body .xm-select-empty {
    text-align: center;
    color: #999;
    }
    xm-select > .xm-body .disabled {
    cursor: no-drop;
    }
    xm-select > .xm-body .disabled:hover {
    background-color: #FFF;
    }
    xm-select > .xm-body .disabled .xm-option-icon {
    border-color: #C2C2C2 !important;
    }
    xm-select > .xm-body .disabled .xm-option-content {
    color: #C2C2C2 !important;
    }
    xm-select > .xm-body .disabled.selected > .xm-option-icon {
    color: #C2C2C2 !important;
    }
    xm-select > .xm-body .xm-search {
    background-color: #FFF !important;
    position: relative;
    padding: 0 10px;
    margin-bottom: 5px;
    cursor: pointer;
    }
    xm-select > .xm-body .xm-search > i {
    position: absolute;
    color: ;
    }
    xm-select > .xm-body .xm-search-input {
    border: none;
    border-bottom: 1px solid #E6E6E6;
    padding-left: 27px;
    cursor: text;
    }
    xm-select > .xm-body .xm-paging {
    padding: 0 10px;
    display: flex;
    margin-top: 5px;
    }
    xm-select > .xm-body .xm-paging > span:first-child {
    border-radius: 2px 0 0 2px;
    }
    xm-select > .xm-body .xm-paging > span:last-child {
    border-radius: 0 2px 2px 0;
    }
    xm-select > .xm-body .xm-paging > span {
    display: flex;
    flex: auto;
    justify-content: center;
    vertical-align: middle;
    margin: 0 -1px 0 0;
    background-color: #fff;
    color: #333;
    font-size: 12px;
    border: 1px solid #e2e2e2;
    flex-wrap: nowrap;
    width: 100%;
    overflow: hidden;
    min-width: 50px;
    }
    xm-select > .xm-body .xm-toolbar {
    padding: 0 10px;
    display: flex;
    margin: -3px 0;
    cursor: default;
    }
    xm-select > .xm-body .xm-toolbar .toolbar-tag {
    cursor: pointer;
    display: flex;
    margin-right: 20px;
    color: ;
    align-items: baseline;
    }
    xm-select > .xm-body .xm-toolbar .toolbar-tag:hover {
    opacity: 0.8;
    }
    xm-select > .xm-body .xm-toolbar .toolbar-tag:active {
    opacity: 1;
    }
    xm-select > .xm-body .xm-toolbar .toolbar-tag > i {
    margin-right: 2px;
    font-size: 14px;
    }
    xm-select > .xm-body .xm-toolbar .toolbar-tag:last-child {
    margin-right: 0;
    }
    xm-select > .xm-body .xm-body-custom {
    line-height: initial;
    cursor: default;
    }
    xm-select > .xm-body .xm-body-custom * {
    box-sizing: initial;
    }
    xm-select > .xm-body .xm-tree {
    position: relative;
    }
    xm-select > .xm-body .xm-tree-icon {
    display: inline-block;
    margin-right: 3px;
    cursor: pointer;
    border: 6px dashed transparent;
    border-left-color: #C2C2C2;
    border-left-style: solid;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    z-index: 2;
    visibility: hidden;
    }
    xm-select > .xm-body .xm-tree-icon.expand {
    margin-top: 3px;
    margin-right: 5px;
    margin-left: -2px;
    transform: rotate(90deg);
    }
    xm-select > .xm-body .xm-tree-icon.xm-visible {
    visibility: visible;
    }
    xm-select > .xm-body .xm-tree .left-line {
    position: absolute;
    left: 13px;
    width: 0;
    z-index: 1;
    border-left: 1px dotted #c0c4cc !important;
    }
    xm-select > .xm-body .xm-tree .top-line {
    position: absolute;
    left: 13px;
    height: 0;
    z-index: 1;
    border-top: 1px dotted #c0c4cc !important;
    }
    xm-select > .xm-body .xm-tree .xm-tree-icon + .top-line {
    margin-left: 1px;
    }
    xm-select > .xm-body .scroll-body > .xm-tree > .xm-option > .top-line,
    xm-select > .xm-body .scroll-body > .xm-option > .top-line {
    width: 0 !important;
    }
    xm-select > .xm-body .xm-cascader-box {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 5px 0;
    border: 1px solid #E6E6E6;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
    margin: -1px;
    }
    xm-select > .xm-body .xm-cascader-box::before {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-right-color: #E6E6E6;
    top: 10px;
    left: -12px;
    }
    xm-select > .xm-body .xm-cascader-box::after {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-right-color: #fff;
    top: 10px;
    left: -11px;
    }
    xm-select > .xm-body .xm-cascader-scroll {
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    }
    xm-select > .xm-body.cascader {
    width: unset;
    min-width: unset;
    }
    xm-select > .xm-body.cascader .xm-option-content {
    padding-left: 8px;
    }
    xm-select > .xm-body.cascader .disabled .xm-right-arrow {
    color: #C2C2C2 !important;
    }
    xm-select > .xm-body.cascader .hide-icon.disabled .xm-right-arrow {
    color: #999 !important;
    }
    xm-select .xm-input {
    cursor: pointer;
    border-radius: 2px;
    border-width: 1px;
    border-style: solid;
    border-color: #E6E6E6;
    display: block;
    width: 100%;
    box-sizing: border-box;
    background-color: #FFF;
    line-height: 1.3;
    padding-left: 10px;
    outline: 0;
    user-select: text;
    -ms-user-select: text;
    -moz-user-select: text;
    -webkit-user-select: text;
    }
    xm-select .dis {
    display: none;
    }
    xm-select .loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    }
    xm-select .loader {
    border: 0.2em dotted currentcolor;
    border-radius: 50%;
    -webkit-animation: 1s loader linear infinite;
    animation: 1s loader linear infinite;
    display: inline-block;
    width: 1em;
    height: 1em;
    color: inherit;
    vertical-align: middle;
    pointer-events: none;
    }
    xm-select .xm-select-default {
    position: absolute;
    width: 100%;
    height: 100%;
    border: none;
    visibility: hidden;
    }
    xm-select .xm-select-disabled {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: no-drop;
    z-index: 2;
    opacity: 0.3;
    background-color: #FFF;
    }
    xm-select .item--divided {
    border-top: 1px solid #ebeef5;
    width: calc(100% - 20px);
    cursor: initial;
    }
    xm-select .xm-right-arrow {
    position: absolute;
    color: ;
    right: 5px;
    top: -1px;
    font-weight: 700;
    transform: scale(0.6, 1);
    }
    xm-select .xm-right-arrow::after {
    content: '>';
    }
    xm-select[size='large'] {
    min-height: 40px;
    line-height: 40px;
    }
    xm-select[size='large'] .xm-input {
    height: 40px;
    }
    xm-select[size='large'] .xm-label .scroll .label-content {
    line-height: 34px;
    }
    xm-select[size='large'] .xm-label .xm-label-block {
    height: 30px;
    line-height: 30px;
    }
    xm-select[size='large'] .xm-body .xm-option .xm-option-icon {
    height: 20px;
    width: 20px;
    font-size: 20px;
    }
    xm-select[size='large'] .xm-paging > span {
    height: 34px;
    line-height: 34px;
    }
    xm-select[size='large'] .xm-tree .left-line {
    height: 100%;
    bottom: 20px;
    }
    xm-select[size='large'] .xm-tree .left-line-group {
    height: calc(100% - 40px);
    }
    xm-select[size='large'] .xm-tree .xm-tree-icon.xm-hidden + .top-line {
    top: 19px;
    }
    xm-select[size='large'] .item--divided {
    margin: 10px;
    }
    xm-select {
    min-height: 36px;
    line-height: 36px;
    }
    xm-select .xm-input {
    height: 36px;
    }
    xm-select .xm-label .scroll .label-content {
    line-height: 30px;
    }
    xm-select .xm-label .xm-label-block {
    height: 26px;
    line-height: 26px;
    }
    xm-select .xm-body .xm-option .xm-option-icon {
    height: 18px;
    width: 18px;
    font-size: 18px;
    }
    xm-select .xm-paging > span {
    height: 30px;
    line-height: 30px;
    }
    xm-select .xm-tree .left-line {
    height: 100%;
    bottom: 18px;
    }
    xm-select .xm-tree .left-line-group {
    height: calc(100% - 36px);
    }
    xm-select .xm-tree .xm-tree-icon.xm-hidden + .top-line {
    top: 17px;
    }
    xm-select .item--divided {
    margin: 9px;
    }
    xm-select[size='small'] {
    min-height: 32px;
    line-height: 32px;
    }
    xm-select[size='small'] .xm-input {
    height: 32px;
    }
    xm-select[size='small'] .xm-label .scroll .label-content {
    line-height: 26px;
    }
    xm-select[size='small'] .xm-label .xm-label-block {
    height: 22px;
    line-height: 22px;
    }
    xm-select[size='small'] .xm-body .xm-option .xm-option-icon {
    height: 16px;
    width: 16px;
    font-size: 16px;
    }
    xm-select[size='small'] .xm-paging > span {
    height: 26px;
    line-height: 26px;
    }
    xm-select[size='small'] .xm-tree .left-line {
    height: 100%;
    bottom: 16px;
    }
    xm-select[size='small'] .xm-tree .left-line-group {
    height: calc(100% - 32px);
    }
    xm-select[size='small'] .xm-tree .xm-tree-icon.xm-hidden + .top-line {
    top: 15px;
    }
    xm-select[size='small'] .item--divided {
    margin: 8px;
    }
    xm-select[size='mini'] {
    min-height: 28px;
    line-height: 28px;
    }
    xm-select[size='mini'] .xm-input {
    height: 28px;
    }
    xm-select[size='mini'] .xm-label .scroll .label-content {
    line-height: 22px;
    }
    xm-select[size='mini'] .xm-label .xm-label-block {
    height: 18px;
    line-height: 18px;
    }
    xm-select[size='mini'] .xm-body .xm-option .xm-option-icon {
    height: 14px;
    width: 14px;
    font-size: 14px;
    }
    xm-select[size='mini'] .xm-paging > span {
    height: 22px;
    line-height: 22px;
    }
    xm-select[size='mini'] .xm-tree .left-line {
    height: 100%;
    bottom: 14px;
    }
    xm-select[size='mini'] .xm-tree .left-line-group {
    height: calc(100% - 28px);
    }
    xm-select[size='mini'] .xm-tree .xm-tree-icon.xm-hidden + .top-line {
    top: 13px;
    }
    xm-select[size='mini'] .item--divided {
    margin: 7px;
    }
    .layui-form-pane xm-select {
    margin: -1px -1px -1px 0;
    }
    </style><style type="text/css">.gwd-row {
    display: flex;
    flex-direction: row;
    }
    .gwd-inline-row {
    display: inline-flex;
    flex-direction: row;
    }
    .gwd-column {
    display: flex;
    flex-direction: column;
    }
    .gwd-inline-column {
    display: inline-flex;
    flex-direction: column;
    }
    .gwd-align {
    align-content: center;
    align-items: center;
    }
    .gwd-jcc {
    justify-content: center;
    }
    .gwd-jic {
    justify-items: center;
    }
    .gwd-button {
    outline: none;
    border: none;
    }
    .bjg-bar-button {
    font-size: 0;
    }
    .bjg-hover-bg {
    background: #fffbef;
    }
    .bjg-bar-button:hover {
    background: #fffbef;
    cursor: pointer;
    }
    .bjg-bar-button:hover .bjg-window {
    display: block;
    }
    .mainbar-fold .bjg-bar-button,
    .mainbar-fold #top_coupon_btn,
    .mainbar-fold .rinfo-btn,
    .mainbar-fold .gwd-bottom-tmall {
    display: none!important;
    }
    .gwd-font12 {
    font-size: 12px;
    }
    .gwd-font14 {
    font-size: 14px;
    }
    .gwd-red {
    color: #ff3532;
    }
    .gwd-red-bg {
    background: #ff3532;
    }
    .gwd-hui333 {
    color: #333333;
    }
    .gwd-hui999 {
    color: #999999;
    }
    .gwd-font10 {
    font-size: 12px;
    transform: scale(0.8333);
    transform-origin: bottom center;
    }
    .gwd-font11 {
    font-size: 12px;
    transform: scale(0.91666);
    transform-origin: bottom center;
    }
    .gwd-font9 {
    font-size: 12px;
    transform: scale(0.75);
    transform-origin: bottom center;
    }
    .gwd-hoverable:hover {
    background: #edf1f2;
    }
    .right-info > * {
    border-left: 1px solid #edf1f2;
    }
    .gwd-red-after-visit:hover {
    color: #e03024 !important;
    }
    .gwd-button:hover {
    filter: brightness(1.1);
    }
    .gwd-button {
    padding-top: 1px;
    padding-bottom: 1px;
    }
    .gwd-button:active {
    filter: brightness(0.9);
    }
    .gwd-fadeout-5s {
    opacity: 0;
    transition: opacity 5s;
    }
    .gwd-scrollbar::-webkit-scrollbar {
    width: 6px;
    border-radius: 17px;
    }
    .gwd-scrollbar::-webkit-scrollbar-thumb {
    border-radius: 17px;
    background: #999;
    }
    #gwdang_main,
    .gwdang-main,
    .bjgext-detail {
    font-size: 12px;
    }
    #gwdang_main button,
    .gwdang-main button,
    .bjgext-detail button {
    text-align: center;
    }
    .gwd-width-100 {
    width: 100%;
    }
    .gwd-overlay {
    font-family: "Microsoft YaHei", "Arial", "SimSun", serif;
    font-size: 0;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.35);
    z-index: 999999999;
    }
    .gwd-font-pfm {
    font-family: 'PingFangSC-Medium';
    font-weight: normal!important;
    }
    @font-face {
    font-family: 'PingFangSC-Medium';
    src: local('PingFangSC-Medium');
    }
    .gwd-font-pfm {
    font-family: local('PingFangSC-Medium'), system-ui;
    font-weight: bold;
    }
    #gwd_minibar svg,
    .gwdang-main svg,
    #bjgext_mb_bg svg,
    #bjgext_mainbar svg {
    fill: transparent;
    }
    .gwd-common-font {
    font-family: 'PingFang SC', 'Microsoft YaHei', '\5FAE\8F6F\96C5\9ED1', 'Hiragino Sans GB', 'WenQuanYi Micro Hei';
    }
    </style><style type="text/css">.gwd-row[data-v-07d1070f] {
    display: flex;
    flex-direction: row;
    }
    .gwd-inline-row[data-v-07d1070f] {
    display: inline-flex;
    flex-direction: row;
    }
    .gwd-column[data-v-07d1070f] {
    display: flex;
    flex-direction: column;
    }
    .gwd-inline-column[data-v-07d1070f] {
    display: inline-flex;
    flex-direction: column;
    }
    .gwd-align[data-v-07d1070f] {
    align-content: center;
    align-items: center;
    }
    .gwd-jcc[data-v-07d1070f] {
    justify-content: center;
    }
    .gwd-jic[data-v-07d1070f] {
    justify-items: center;
    }
    .gwd-button[data-v-07d1070f] {
    outline: none;
    border: none;
    }
    .bjg-bar-button[data-v-07d1070f] {
    font-size: 0;
    }
    .bjg-hover-bg[data-v-07d1070f] {
    background: #fffbef;
    }
    .bjg-bar-button[data-v-07d1070f]:hover {
    background: #fffbef;
    cursor: pointer;
    }
    .bjg-bar-button:hover .bjg-window[data-v-07d1070f] {
    display: block;
    }
    .mainbar-fold .bjg-bar-button[data-v-07d1070f],
    .mainbar-fold #top_coupon_btn[data-v-07d1070f],
    .mainbar-fold .rinfo-btn[data-v-07d1070f],
    .mainbar-fold .gwd-bottom-tmall[data-v-07d1070f] {
    display: none!important;
    }
    .gwd-font12[data-v-07d1070f] {
    font-size: 12px;
    }
    .gwd-font14[data-v-07d1070f] {
    font-size: 14px;
    }
    .gwd-red[data-v-07d1070f] {
    color: #ff3532;
    }
    .gwd-red-bg[data-v-07d1070f] {
    background: #ff3532;
    }
    .gwd-hui333[data-v-07d1070f] {
    color: #333333;
    }
    .gwd-hui999[data-v-07d1070f] {
    color: #999999;
    }
    .gwd-font10[data-v-07d1070f] {
    font-size: 12px;
    transform: scale(0.8333);
    transform-origin: bottom center;
    }
    .gwd-font11[data-v-07d1070f] {
    font-size: 12px;
    transform: scale(0.91666);
    transform-origin: bottom center;
    }
    .gwd-font9[data-v-07d1070f] {
    font-size: 12px;
    transform: scale(0.75);
    transform-origin: bottom center;
    }
    .gwd-hoverable[data-v-07d1070f]:hover {
    background: #edf1f2;
    }
    .right-info > *[data-v-07d1070f] {
    border-left: 1px solid #edf1f2;
    }
    .gwd-red-after-visit[data-v-07d1070f]:hover {
    color: #e03024 !important;
    }
    .gwd-button[data-v-07d1070f]:hover {
    filter: brightness(1.1);
    }
    .gwd-button[data-v-07d1070f] {
    padding-top: 1px;
    padding-bottom: 1px;
    }
    .gwd-button[data-v-07d1070f]:active {
    filter: brightness(0.9);
    }
    .gwd-fadeout-5s[data-v-07d1070f] {
    opacity: 0;
    transition: opacity 5s;
    }
    .gwd-scrollbar[data-v-07d1070f]::-webkit-scrollbar {
    width: 6px;
    border-radius: 17px;
    }
    .gwd-scrollbar[data-v-07d1070f]::-webkit-scrollbar-thumb {
    border-radius: 17px;
    background: #999;
    }
    #gwdang_main[data-v-07d1070f],
    .gwdang-main[data-v-07d1070f],
    .bjgext-detail[data-v-07d1070f] {
    font-size: 12px;
    }
    #gwdang_main button[data-v-07d1070f],
    .gwdang-main button[data-v-07d1070f],
    .bjgext-detail button[data-v-07d1070f] {
    text-align: center;
    }
    .gwd-width-100[data-v-07d1070f] {
    width: 100%;
    }
    .gwd-overlay[data-v-07d1070f] {
    font-family: "Microsoft YaHei", "Arial", "SimSun", serif;
    font-size: 0;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.35);
    z-index: 999999999;
    }
    .gwd-font-pfm[data-v-07d1070f] {
    font-family: 'PingFangSC-Medium';
    font-weight: normal!important;
    }
    @font-face {
    font-family: 'PingFangSC-Medium';
    src: local('PingFangSC-Medium');
    }
    .gwd-font-pfm[data-v-07d1070f] {
    font-family: local('PingFangSC-Medium'), system-ui;
    font-weight: bold;
    }
    #gwd_minibar svg[data-v-07d1070f],
    .gwdang-main svg[data-v-07d1070f],
    #bjgext_mb_bg svg[data-v-07d1070f],
    #bjgext_mainbar svg[data-v-07d1070f] {
    fill: transparent;
    }
    .gwd-common-font[data-v-07d1070f] {
    font-family: 'PingFang SC', 'Microsoft YaHei', '\5FAE\8F6F\96C5\9ED1', 'Hiragino Sans GB', 'WenQuanYi Micro Hei';
    }
    .gwd-taobao[data-v-07d1070f] {
    margin-top: 20px;
    }
    .gwd-taobao span[data-v-07d1070f] {
    color: #ff4400;
    }
    .gwd-jd[data-v-07d1070f] {
    margin-top: 20px;
    }
    .gwd-jd span[data-v-07d1070f] {
    color: #e2231a;
    }
    a[data-v-07d1070f] {
    white-space: nowrap;
    position: relative;
    height: 24px;
    font-family: 'Microsoft YaHei';
    }
    a[data-v-07d1070f]:hover {
    text-decoration: none;
    cursor: pointer;
    }
    a:hover .gwd-tooltip[data-v-07d1070f] {
    display: block;
    }
    .gwd-coupon[data-v-07d1070f] {
    height: 24px;
    box-sizing: border-box;
    min-width: 106px;
    }
    .gwd-coupon[data-v-07d1070f] {
    background-size: contain;
    padding-left: 8px;
    padding-right: 8px;
    }
    .gwd-coupon[data-v-07d1070f]:before,
    .gwd-coupon[data-v-07d1070f]:after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 9px;
    background-size: contain;
    }
    .gwd-coupon[data-v-07d1070f]:before {
    left: 0;
    }
    .gwd-coupon[data-v-07d1070f]:after {
    right: 0;
    transform: rotate(180deg);
    }
    .gwd-coupon.gwd-taobao[data-v-07d1070f] {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAA0CAYAAAAjfRLqAAAAAXNSR0IArs4c6QAAAU5JREFUeF7tlNENgDAQhdrd3H+lc4cmGgX8r5YHca+eFgAtsOdaA+IJRb5AQcsDoOHvmekPTbMq5ilosXwiekETrYqZClosn4he0ESrYqaCFssnohc00aqYqaDF8onoBU20KmYqaLF8InpBE62KmQpaLJ+IXtBEq2KmghbLJ6IXNNGqmKmgxfKJ6AVNtCpmKmixfCJ6QROtipkKWiyfiF7QRKtipoIWyyeiFzTRqpipoMXyiegFTbQqZiposXwiekETrYqZClosn4he0ESrYqaCFssnohc00aqYqaDF8onoBU20KmYqaLF8InpBE62KmQpaLJ+IXtBEq2KmghbLJ6IXNNGqmKmgxfKJ6AVNtCpmKmixfCJ6QROtipkKWiyfiF7QRKtipoL+m/yZv9341fvuuVYLvTp5H3tygYJ+ct3zd/eTOdxuH57rWAt8coEbbyjDgopRg1YAAAAASUVORK5CYII=);
    }
    .gwd-coupon.gwd-taobao[data-v-07d1070f]:before,
    .gwd-coupon.gwd-taobao[data-v-07d1070f]:after {
    background-repeat: no-repeat;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAA0CAYAAACHO2h8AAAAAXNSR0IArs4c6QAAAZJJREFUSEvtlz9OAkEUh79NsNJiOy038QDuEUggxM6INWCrDRRSwwE0UtqtiR7A3mKPwBH2CF7AZM2bAeJkIeyb3U004TU0w5f3Z37vNxtQUwTCybvkVXkNgT79E3MzumjDQwJnkbrSYmknITymcB6rYAZkGt4m4og5OSMkM4EpYgNawUJaZEDIewan5Ut0QKurIKm0TUaSWcnYBloCcSVQ3mFMwMKUJKUpwk6tQ0KANMTWMn2F3kiBAXf8xyEMZ9CfqCBy2AV9fIHAPMIF1SYR5ch/J16UiPTn7lld3Bok0ogIuDK3ejiHwUwFcyXSNeNPEeG+ZZjfkrH7Zr8sVRugGa3ll0R8I1rzU7/ZRS0jERlXTO8WpknJ7thjxfGLYJ9S1S4qgmTkNxMvmTQkkdq0drCjbResGYmsfO0v2pFsSKWTFLUmdjSYqwT7H+xI9rTHa237PqrJjuzL4X4B12NVww92tL9d9maLHYmXtRAPir2fx85HTS121B9bsSre1+uiG7Kj/T3deaLe77UKiWz++gOA98aFVwFZGwAAAABJRU5ErkJggg==);
    }
    .gwd-coupon.gwd-taobao[data-v-07d1070f]:hover {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAA0CAYAAAAjfRLqAAAAAXNSR0IArs4c6QAAAVNJREFUeF7tlMENgzAUxZLd2H+NXrvB7w6RQMU290D8bLFXTwuAFthzrQHxhCJfoKDlAdDw93w//aFpVsU8BS2WT0QvaKJVMVNBi+UT0QuaaFXMVNBi+UT0giZaFTMVtFg+Eb2giVbFTAUtlk9EL2iiVTFTQYvlE9ELmmhVzFTQYvlE9IImWhUzFbRYPhG9oIlWxUwFLZZPRC9oolUxU0GL5RPRC5poVcxU0GL5RPSCJloVMxW0WD4RvaCJVsVMBS2WT0QvaKJVMVNBi+UT0QuaaFXMVNBi+UT0giZaFTMVtFg+Eb2giVbFTAUtlk9EL2iiVTFTQYvlE9ELmmhVzFTQYvlE9IImWhUzFbRYPhG9oIlWxUwFLZZPRC9oolUxU0GL5RPRC5poVcxU0G+TP/O2Gz963z3XaqFHJ+9jdy5Q0Heue/7ufjKH2+3Dcx1rgb9c4AdcEryACPINxAAAAABJRU5ErkJggg==);
    }
    .gwd-coupon.gwd-taobao[data-v-07d1070f]:hover:before,
    .gwd-coupon.gwd-taobao[data-v-07d1070f]:hover:after {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAA0CAYAAACHO2h8AAAAAXNSR0IArs4c6QAAAYtJREFUSEvtl79Kw2AUxX+BuCiULroVstfBVadIOwgd3NqxOOii4GAfwDoLdqpjcVdfwKF7HZydHH0JIXJvk+pHWujNH6nQC9mSk/Pde849iUdB5QlO1CTKi1cS0ONnZmIuo/oBnA9gp2YGTB9tqwL9Jwh2TWAKpA0PCdjgmoguwkzADDUDisGq+HwAVYYT0xEdoFgKYyBURsJsyZoH9Abs5QKKGlziMWC7BveTJblMb5tOrcEIj0CPJCUSOOxkAEosslmBTg9apyaQH0YJ0MM7iI4ylCvIwixiHPlv4mmLSH9ObsyHS4DEGgEex6rq9hW0eyYw1yJNHf9YGz58NTV+sbJvX0wboByvRUcEfCFey+Z+3UW+WuRODRt24GJgb7aTImJY0ZNx3bo6krG3zkzTSmiXZJHCvLaOo3kCK8cica6tYhzJhjQmSXrVZtiO/yGOjJ8yfxJHXX1L1qklFKN1HC10v8aRZJnPSOOovg/951WIIzGqXMZMS3utsBQxdcW9udj/tRxEZo9+AyVqv4MZSS3ZAAAAAElFTkSuQmCC);
    }
    .gwd-coupon.gwd-jd[data-v-07d1070f] {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKoAAAAsCAYAAAD1nyNHAAAAAXNSR0IArs4c6QAAAR1JREFUeF7tnbERgFAMhcz+2+k0VnGF33kcWFvwAmfrXD1dAHCBee9nAZwhyi9QqPIAKPNnd/uiUmyJOQtVLJ80vVBJtsSshSqWT5peqCRbYtZCFcsnTS9Uki0xa6GK5ZOmFyrJlpi1UMXySdMLlWRLzFqoYvmk6YVKsiVmLVSxfNL0QiXZErMWqlg+aXqhkmyJWQtVLJ80vVBJtsSshSqWT5peqCRbYtZCFcsnTS9Uki0xa6GK5ZOmFyrJlpi1UMXySdMLlWRLzFqoYvmk6YVKsiVmLVSxfNL0QiXZErMWqlg+aXqhkmyJWQtVLJ80vVBJtsSshSqWT5peqCRbYtZCFcsnTe/3PSRbYtZC/V9+v086cDAH7/RKF/j9Ah900KcI8oZhhwAAAABJRU5ErkJggg==);
    }
    .gwd-coupon.gwd-jd[data-v-07d1070f]:before,
    .gwd-coupon.gwd-jd[data-v-07d1070f]:after {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAsCAYAAABovuiKAAAAAXNSR0IArs4c6QAAAdVJREFUSEvNVlFO20AQfbOzayEhUUepgkIicFBKCmoljpAb0CNwA7gBuUG4AUfhCCvb6jdH4DPFldhqXCWya/Exi5G6n5b9/ObNmzdL6OmQ4LwUZXgv3scAudMZaH8/ilyHEX8egsdjNVgNtMnzFUCZAa5AlPJoBD4cqcBqoO3Z+HJpGI+UOLjFIh6o2cHk+7d4oMr7y8DWwzkkXyMYbYrimog+UcAKQCr6iE6a0+matF9soD0tIHs6g+nDR1qBm6xbjMwgrbWhJNFWhq5GiYOdzdRgO0NuvM+Y7UMAljGCt5wdvE8rts9gRnJxriqvBdSbs3+XP+9ew+squrSqKB8DKANCJqz45Bh8cKAvbRe1xoAnR+A0VYHIy63292ZItzhT+2dLvRu1Eem4K60qigeJWjGjPLTTCcxgoNKp5aNfeX5LZNbR7W/+ettBrfD/hL/PDNun6BGRgQWQsXXrEMKlxImdTvUatXa/c3XUajOp1X4JfTMcgphVbD7O2TH+edPZsq7tfK4u7+8lQnY+QgqDNYiyd18idiv7v4naKi9vAuGe9vbgvsxVFqg1kukPIInFH3XUTifgmOlvOjvmJtIxpLs4V7f9TR+phGm83O89O5ZF87s/IaG3Cf8Fdl8AAAAASUVORK5CYII=);
    background-size: cover;
    background-repeat: no-repeat;
    }
    .gwd-coupon.gwd-jd[data-v-07d1070f]:hover {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKoAAAAsCAYAAAD1nyNHAAAAAXNSR0IArs4c6QAAAR9JREFUeF7tnbENgFAQhbz9t9NdTGzUFX73Q8DagncQW+fo6QKAC8xzXh+AM0T5BQpVHgBl/rz33ReVYkvMWahi+aTphUqyJWYtVLF80vRCJdkSsxaqWD5peqGSbIlZC1UsnzS9UEm2xKyFKpZPml6oJFti1kIVyydNL1SSLTFroYrlk6YXKsmWmLVQxfJJ0wuVZEvMWqhi+aTphUqyJWYtVLF80vRCJdkSsxaqWD5peqGSbIlZC1UsnzS9UEm2xKyFKpZPml6oJFti1kIVyydNL1SSLTFroYrlk6YXKsmWmLVQxfJJ0wuVZEvMWqhi+aTphUqyJWYtVLF80vRCJdkSsxaqWD5per/vIdkSsxbqfvn9PmnBwSy80ytdYPsFfqOApDYolPr+AAAAAElFTkSuQmCC);
    }
    .gwd-coupon.gwd-jd[data-v-07d1070f]:hover:before,
    .gwd-coupon.gwd-jd[data-v-07d1070f]:hover:after {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAsCAYAAABovuiKAAAAAXNSR0IArs4c6QAAAclJREFUSEvNlttNw0AQRe/MrgURChgThUeAmEcBKSEdQAl0AB2QDkIHlEIJlm3xTQl8BoyURePIkY3Fx6yDxPxFcq5m75x5EDYUJDqfWe666v2NkBmdgHs9r+RaGXEYwgwO1GKl0CJNZwDFDNyAKORoHyaKVGKlUBWLJJ+ywQtZCxuP/YXqFQyur/yFiiSZOGMTWIvAJ6NFlt0R0R45zAD4e1QHknrbsKOR6lnycaP8dnQC2gRHWoPraTcy4n4fwhAFQbenlW+1FtIqWrE1kIskiY2xzw6Yik/ilyYaZLskCQtj38GM4PJCo7OqWj0qFLTGN4S+8tfHpVvOfFgqhYosf3GgGHCx/DbHR+CdHf3T1mQzwwwG4N2+SqRFttaXX4G043M1P5VYe9RGEUy07/e0IsueZdQKjKXZw6Hap0b5P9L0gYjnncne2KiVfmNj37xbRAQAxMYGc+fcRMaJORyqDG+vbFlFvmOkIlvKTmEIYlZl83dk+/DzK9nluj4/g1ROE6sjQnY+XAjGHERx5yNivbL/zagt0vzeEZ5oawv27FRj0Wr4S/c7UAjgtlP3148IH6PbR8TlhRfVLSGVKT8+3uyd3SWT6r/fRdq0N2b1Td4AAAAASUVORK5CYII=);
    }
    .gwd-redpack[data-v-07d1070f] {
    padding-right: 5px;
    padding-left: 8px;
    background: white;
    }
    .gwd-redpack.gwd-taobao[data-v-07d1070f] {
    border: 1px solid #ff4400;
    }
    .gwd-redpack.gwd-taobao[data-v-07d1070f]:hover {
    background: #fff0e7;
    }
    .gwd-redpack.gwd-jd[data-v-07d1070f] {
    border: 1px solid #f9d2d3;
    }
    .gwd-redpack.gwd-jd[data-v-07d1070f]:hover {
    background: #fff0e7;
    }
    .gwd-tooltip[data-v-07d1070f] {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    z-index: 1;
    color: #3c3c3c;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: none;
    background: white;
    border-radius: 2px;
    }
    .gwd-tooltip span[data-v-07d1070f] {
    color: #3c3c3c;
    }
    .gwd-tooltip[data-v-07d1070f]:after {
    /* a triangle at bottom */
    content: " ";
    position: absolute;
    bottom: -10px;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent white transparent;
    transform: rotate(180deg);
    }

/* 在文件末尾添加导航菜单样式 */

/* ===== 导航菜单样式 ===== */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  height: 80px;
  max-width: 1400px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.logo-icon {
  margin-right: 0.75rem;
  font-size: 1.5rem;
  color: #ffd700;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: block;
  padding: 1rem 1.5rem;
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.25rem;
}

.nav-link:hover,
.nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 250px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  padding: 0.5rem 0;
  margin: 0;
  z-index: 1001;
}

.nav-item:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.5rem;
}

.dropdown-item:hover {
  background: #f8f9ff;
  color: #667eea;
  transform: translateX(8px);
}

.dropdown-icon {
  margin-right: 0.75rem;
  font-size: 1rem;
  color: #667eea;
}

.nav-tools {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.theme-switch {
  position: relative;
  width: 60px;
  height: 30px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-switch:hover {
  background: rgba(255, 255, 255, 0.3);
}

.theme-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-size: 0.875rem;
}

.theme-icon:first-child {
  left: 8px;
}

.theme-icon:last-child {
  right: 8px;
}

.theme-switch-slider {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.notification-icon {
  position: relative;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notification-icon:hover {
  transform: scale(1.1);
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.user-profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-profile img:hover {
  border-color: white;
  transform: scale(1.05);
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  overflow: hidden;
}

.main-content iframe {
  width: 100%;
  height: calc(100vh - 80px);
  border: none;
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .navbar {
    padding: 0 1rem;
  }
  
  .logo span {
    display: none;
  }
  
  .nav-menu {
    gap: 0;
  }
  
  .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .header {
    position: static;
  }
  
  .navbar {
    flex-direction: column;
    height: auto;
    padding: 1rem;
  }
  
  .nav-menu {
    flex-direction: column;
    width: 100%;
    margin-top: 1rem;
  }
  
  .nav-item {
    width: 100%;
  }
  
  .nav-link {
    text-align: center;
    margin: 0.25rem 0;
  }
  
  .dropdown-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
  }
  
  .main-content iframe {
    height: calc(100vh - 200px);
  }
}

/* ===========================================
   IFRAME环境样式优化
   =========================================== */

/* 确保iframe中的页面具有透明背景 */
body.iframe-content {
    background: transparent !important;
    overflow-x: hidden;
}

/* 移除iframe中页面的顶部边距 */
.container {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* 确保主内容区域在iframe中正确显示 */
.main-content {
    margin-top: 0 !important;
    padding-top: 0 !important;
    min-height: calc(100vh - 20px);
}

/* 调整侧边菜单在iframe中的显示 */
.side-menu {
    position: fixed;
    z-index: 1000;
}

/* 移动端适配：隐藏侧边菜单 */
@media (max-width: 768px) {
    .side-menu {
        display: none;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

/* iframe容器样式 */
.iframe-container {
    width: 100%;
    height: 100%;
    border: none;
    background: transparent;
}

/* 确保页面内容在iframe中正确缩放 */
.assessment-flow-container,
.dashboard-grid,
.dashboard-left-column,
.dashboard-right-column {
    width: 100%;
    max-width: none;
    margin: 0 auto;
}

/* 修复可能的滚动条问题 */
html, body {
    overflow-x: hidden;
}

/* 确保卡片和组件在iframe中正确显示 */
.dashboard-card,
.stat-card,
.task-card {
    box-sizing: border-box;
    width: 100%;
}

/* 响应式网格调整 */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

/* 主题切换在iframe中的适配 */
:root.light-theme .iframe-container {
    background: #FFFFFF;
}

:root .iframe-container {
    background: #0F1520;
}

/* 确保表格在iframe中正确显示 */
.table-container {
    overflow-x: auto;
    width: 100%;
}

/* 确保表单在iframe中正确显示 */
.form-container,
.modal-content {
    max-width: 100%;
    margin: 0 auto;
}

/* 修复可能的定位问题 */
.fixed-elements {
    position: absolute !important;
}

/* 确保图表容器正确显示 */
.chart-container,
.task-chart-container,
.simulation-chart-container {
    width: 100%;
    height: auto;
    min-height: 300px;
}