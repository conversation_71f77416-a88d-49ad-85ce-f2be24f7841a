<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>API测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mockjs/dist/mock.min.js"></script>
    <style>
      body {
        font-family: 'Arial', sans-serif;
        background-color: #0c3b64;
        color: #e0e6f0;
        margin: 0;
        padding: 20px;
      }
      h1,
      h2 {
        color: #07f6ff;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
      }
      .section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: rgba(25, 164, 255, 0.1);
        border: 1px solid rgba(25, 164, 255, 0.3);
        border-radius: 8px;
      }
      button {
        background-color: #0096ff;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
        margin-bottom: 10px;
      }
      button:hover {
        background-color: #007acc;
      }
      pre {
        background-color: rgba(0, 0, 0, 0.2);
        padding: 15px;
        border-radius: 4px;
        overflow: auto;
        color: #e0e6f0;
      }
      .response-container {
        margin-top: 15px;
      }
      .loading {
        color: #ffb946;
      }
      .error {
        color: #f25767;
      }
      .success {
        color: #00c48c;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>API 测试页面</h1>

      <div class="section">
        <h2>模拟数据</h2>
        <button id="setupMock">初始化Mock服务</button>
        <div class="response-container" id="mockResult"></div>
      </div>

      <div class="section">
        <h2>模版列表</h2>
        <button id="getTemplates">获取模版列表</button>
        <div class="response-container" id="templatesResult"></div>
      </div>

      <div class="section">
        <h2>统计数据</h2>
        <button id="getStats">获取统计数据</button>
        <div class="response-container" id="statsResult"></div>
      </div>

      <div class="section">
        <h2>活动日志</h2>
        <button id="getActivities">获取活动日志</button>
        <div class="response-container" id="activitiesResult"></div>
      </div>
    </div>

    <script>
      // 初始化结果容器
      const mockResult = document.getElementById('mockResult')
      const templatesResult = document.getElementById('templatesResult')
      const statsResult = document.getElementById('statsResult')
      const activitiesResult = document.getElementById('activitiesResult')

      // 模版列表数据
      const templateListData = [
        {
          id: 'TPL-20250401-001',
          name: '电力系统仿真度标准评估模版',
          description: '用于电力系统稳态和暂态仿真评估的标准模版',
          domain: '电力系统',
          status: '已发布',
          statusClass: 'active',
          creator: '张三',
          createTime: '2025-03-15',
          usageCount: 124,
          favorite: true,
          key: 'TPL-20250401-001',
        },
        {
          id: 'TPL-20250402-002',
          name: '城市交通流量仿真度评估模版',
          description: '适用于城市交通系统流量和拥堵分析',
          domain: '交通系统',
          status: '已发布',
          statusClass: 'active',
          creator: '李三',
          createTime: '2025-03-20',
          usageCount: 98,
          favorite: true,
          key: 'TPL-20250402-002',
        },
      ]

      // 统计数据
      const statsData = [
        {
          title: '评估模版总数',
          value: 30,
          trend: 'up',
          trendValue: '较上月增长 8 个',
          type: 'info',
          icon: 'cube',
        },
        {
          title: '已发布',
          value: 15,
          trend: 'up',
          trendValue: '较上月增长 5 个',
          type: 'success',
          icon: 'check-circle',
        },
      ]

      // 活动数据
      const activitiesData = [
        {
          title: '张三发布了新模版"电力系统暂态仿真专项评估模版"',
          time: '今天 09:32',
          type: 'success',
        },
        {
          title: '王三提交了"供水管网压力仿真度评估模版"审核申请',
          time: '昨天 16:45',
          type: 'warning',
        },
      ]

      // 初始化Mock服务
      document.getElementById('setupMock').addEventListener('click', function () {
        mockResult.innerHTML = '<div class="loading">初始化Mock服务中...</div>'

        try {
          // 设置Mock配置
          Mock.setup({
            timeout: '200-600',
          })

          // 模版列表接口
          Mock.mock(/\/api\/template\/list(\?.+)?$/, 'get', function (options) {
            console.log('Mock处理获取模版列表请求:', options)
            const params = { pageSize: 7, pageNum: 1 }
            return {
              code: 200,
              message: 'success',
              data: {
                list: templateListData,
                total: 100,
                pageSize: params.pageSize,
                pageNum: params.pageNum,
              },
            }
          })

          // 统计数据接口
          Mock.mock('/api/template/stats', 'get', function () {
            console.log('Mock处理获取统计数据请求')
            return {
              code: 200,
              message: 'success',
              data: statsData,
            }
          })

          // 活动日志接口
          Mock.mock('/api/template/activities', 'get', function () {
            console.log('Mock处理获取活动日志请求')
            return {
              code: 200,
              message: 'success',
              data: activitiesData,
            }
          })

          mockResult.innerHTML = '<div class="success">Mock服务初始化成功!</div>'
          console.log('Mock服务初始化成功')
        } catch (error) {
          mockResult.innerHTML = `<div class="error">Mock服务初始化失败: ${error.message}</div>`
          console.error('Mock服务初始化失败:', error)
        }
      })

      // 获取模版列表
      document.getElementById('getTemplates').addEventListener('click', function () {
        templatesResult.innerHTML = '<div class="loading">获取模版列表中...</div>'

        axios
          .get('/api/template/list', { params: { pageSize: 7, pageNum: 1 } })
          .then(function (response) {
            console.log('模版列表响应:', response)
            templatesResult.innerHTML = `
            <div class="success">请求成功!</div>
            <pre>${JSON.stringify(response.data, null, 2)}</pre>
          `
          })
          .catch(function (error) {
            console.error('获取模版列表失败:', error)
            templatesResult.innerHTML = `<div class="error">请求失败: ${error.message}</div>`
          })
      })

      // 获取统计数据
      document.getElementById('getStats').addEventListener('click', function () {
        statsResult.innerHTML = '<div class="loading">获取统计数据中...</div>'

        axios
          .get('/api/template/stats')
          .then(function (response) {
            console.log('统计数据响应:', response)
            statsResult.innerHTML = `
            <div class="success">请求成功!</div>
            <pre>${JSON.stringify(response.data, null, 2)}</pre>
          `
          })
          .catch(function (error) {
            console.error('获取统计数据失败:', error)
            statsResult.innerHTML = `<div class="error">请求失败: ${error.message}</div>`
          })
      })

      // 获取活动日志
      document.getElementById('getActivities').addEventListener('click', function () {
        activitiesResult.innerHTML = '<div class="loading">获取活动日志中...</div>'

        axios
          .get('/api/template/activities')
          .then(function (response) {
            console.log('活动日志响应:', response)
            activitiesResult.innerHTML = `
            <div class="success">请求成功!</div>
            <pre>${JSON.stringify(response.data, null, 2)}</pre>
          `
          })
          .catch(function (error) {
            console.error('获取活动日志失败:', error)
            activitiesResult.innerHTML = `<div class="error">请求失败: ${error.message}</div>`
          })
      })
    </script>
  </body>
</html>
