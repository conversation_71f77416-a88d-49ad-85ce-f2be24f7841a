import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  const browser = await chromium.launch()
  const context = await browser.newContext()

  // 设置默认的Authorization头
  await context.setExtraHTTPHeaders({
    'Authorization': 'd6d662a80b3f4f04922ff2503b8b1e60'
  })

  // 设置Cookie
  await context.addCookies([
    {
      name: 'Authorization',
      value: 'd6d662a80b3f4f04922ff2503b8b1e60',
      domain: 'localhost',
      path: '/'
    }
  ])

  // 保存认证状态到文件
  await context.storageState({ path: 'e2e/auth.json' })

  await browser.close()
}

export default globalSetup 