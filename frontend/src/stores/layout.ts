import { ref } from 'vue'
import { defineStore } from 'pinia'

// 定义路由路径与标题的映射关系
const defaultTitles: Record<string, string> = {
  '/dashboard': '仪表盘',
  '/template-manager': '模版列表-管理所有仿真度测评模版',
  '/algorithm-library': '算法库',
  '/data-collection': '数据采集',
  '/evaluation-task': '评估任务',
}

export const useLayoutStore = defineStore('layout', () => {
  // 当前页面标题
  const pageTitle = ref('仪表盘')

  // 用户信息
  const userName = ref('caiyong')
  const userAvatar = ref(new URL('../assets/images/default-user-icon.png', import.meta.url).href)

  // 设置页面标题
  function setPageTitle(title: string) {
    pageTitle.value = title
  }

  // 根据路由路径设置默认标题
  function setTitleByPath(path: string) {
    // 提取基本路径（移除查询参数）
    const basePath = path.split('?')[0]

    // 检查是否有对应的默认标题
    if (defaultTitles[basePath]) {
      pageTitle.value = defaultTitles[basePath]
    }
  }

  // 根据路由和查询参数设置自定义标题
  function setCustomTitleByQuery(path: string, query: Record<string, string>) {
    // 提取基本路径和默认标题
    const basePath = path.split('?')[0]
    const baseTitle = defaultTitles[basePath] || '页面'

    // 根据查询参数自定义标题
    // 例如: 如果URL是 /data-collection?id=123，可以设置标题为"数据采集-123"
    if (query.id) {
      pageTitle.value = `${baseTitle}-${query.id}`
    } else {
      pageTitle.value = baseTitle
    }
  }

  // 设置用户名
  function setUserName(name: string) {
    userName.value = name
  }

  // 设置用户头像
  function setUserAvatar(avatarUrl: string) {
    userAvatar.value = avatarUrl
  }

  return {
    pageTitle,
    userName,
    userAvatar,
    setPageTitle,
    setTitleByPath,
    setCustomTitleByQuery,
    setUserName,
    setUserAvatar,
  }
})
