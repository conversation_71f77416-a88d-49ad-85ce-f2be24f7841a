// Mock路由映射
import Mock from 'mockjs'
import * as templateManager from './handlers/templateManager'

// 注册所有Mock路由
export function setupMockRoutes() {
  console.log('注册Mock路由...')

  // 模版列表
  Mock.mock(/\/api\/template\/list(\?.+)?$/, 'get', (options) => {
    console.log('处理模版列表请求:', options)
    try {
      const url = new URL(options.url, 'http://localhost')
      const params = Object.fromEntries(url.searchParams.entries())
      const result = templateManager.getTemplateList(params)
      console.log('模版列表返回:', result)
      return result
    } catch (error) {
      console.error('处理模版列表请求出错:', error)
      return {
        code: 500,
        message: '服务器内部错误',
        data: null,
      }
    }
  })

  // 模版详情
  Mock.mock(/\/api\/template\/detail\/(.*)/, 'get', (options) => {
    console.log('处理模版详情请求:', options)
    try {
      const id = options.url.match(/\/api\/template\/detail\/([^/]+)/)[1]
      const result = templateManager.getTemplateDetail(id)
      console.log('模版详情返回:', result)
      return result
    } catch (error) {
      console.error('处理模版详情请求出错:', error)
      return {
        code: 500,
        message: '服务器内部错误',
        data: null,
      }
    }
  })

  // 模版统计
  Mock.mock('/api/template/stats', 'get', () => {
    console.log('处理模版统计请求')
    try {
      const result = templateManager.getTemplateStats()
      console.log('模版统计返回:', result)
      return result
    } catch (error) {
      console.error('处理模版统计请求出错:', error)
      return {
        code: 500,
        message: '服务器内部错误',
        data: null,
      }
    }
  })

  // 活动日志
  Mock.mock('/api/template/activities', 'get', () => {
    console.log('处理活动日志请求')
    try {
      const result = templateManager.getTemplateActivities()
      console.log('活动日志返回:', result)
      return result
    } catch (error) {
      console.error('处理活动日志请求出错:', error)
      return {
        code: 500,
        message: '服务器内部错误',
        data: null,
      }
    }
  })

  // 收藏模版
  Mock.mock('/api/template/favorite', 'post', (options) => {
    console.log('处理收藏模版请求:', options)
    try {
      const { id, isFavorite } = JSON.parse(options.body)
      const result = templateManager.toggleTemplateFavorite(id, isFavorite)
      console.log('收藏模版返回:', result)
      return result
    } catch (error) {
      console.error('处理收藏模版请求出错:', error)
      return {
        code: 500,
        message: '服务器内部错误',
        data: null,
      }
    }
  })

  // 删除模版
  Mock.mock(/\/api\/template\/delete\/(.*)/, 'delete', (options) => {
    console.log('处理删除模版请求:', options)
    try {
      const id = options.url.match(/\/api\/template\/delete\/([^/]+)/)[1]
      const result = templateManager.deleteTemplate(id)
      console.log('删除模版返回:', result)
      return result
    } catch (error) {
      console.error('处理删除模版请求出错:', error)
      return {
        code: 500,
        message: '服务器内部错误',
        data: null,
      }
    }
  })

  console.log('Mock路由注册完成')
}
