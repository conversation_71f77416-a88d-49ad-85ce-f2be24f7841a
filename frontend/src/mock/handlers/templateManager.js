// 模版管理相关的Mock数据处理

// 模版列表数据
const templateListData = [
  {
    id: 'TPL-20250401-001',
    name: '电力系统仿真度标准评估模版',
    description: '用于电力系统稳态和暂态仿真评估的标准模版',
    domain: '电力系统',
    status: '已发布',
    statusClass: 'active',
    creator: '张三',
    createTime: '2025-03-15',
    usageCount: 124,
    favorite: true,
    key: 'TPL-20250401-001',
  },
  {
    id: 'TPL-20250402-002',
    name: '城市交通流量仿真度评估模版',
    description: '适用于城市交通系统流量和拥堵分析',
    domain: '交通系统',
    status: '已发布',
    statusClass: 'active',
    creator: '李三',
    createTime: '2025-03-20',
    usageCount: 98,
    favorite: true,
    key: 'TPL-20250402-002',
  },
  {
    id: 'TPL-20250403-003',
    name: '供水管网压力仿真度评估模版',
    description: '用于城市供水系统压力和流量仿真评估',
    domain: '水利系统',
    status: '审核中',
    statusClass: 'review',
    creator: '王三',
    createTime: '2025-03-28',
    usageCount: 42,
    favorite: false,
    key: 'TPL-20250403-003',
  },
  {
    id: 'TPL-20250404-004',
    name: '燃气管网安全性能仿真评估模版',
    description: '城市燃气系统安全与泄漏仿真测评标准',
    domain: '燃气系统',
    status: '草稿',
    statusClass: 'draft',
    creator: '赵三',
    createTime: '2025-04-01',
    usageCount: 5,
    favorite: false,
    key: 'TPL-20250404-004',
  },
  {
    id: 'TPL-20250405-005',
    name: '通信网络容量评估模版',
    description: '城市通信网络容量和时延性能评估',
    domain: '通信系统',
    status: '草稿',
    statusClass: 'draft',
    creator: '陈三',
    createTime: '2025-02-10',
    usageCount: 76,
    favorite: false,
    key: 'TPL-20250405-005',
  },
  {
    id: 'TPL-20250406-006',
    name: '电力系统暂态仿真专项评估模版',
    description: '针对电力系统暂态事件的详细仿真度评估',
    domain: '电力系统',
    status: '已发布',
    statusClass: 'active',
    creator: '张三',
    createTime: '2025-03-05',
    usageCount: 67,
    favorite: false,
    key: 'TPL-20250406-006',
  },
  {
    id: 'TPL-20250407-007',
    name: '城市轨道交通运行仿真评估模版',
    description: '轨道交通系统运行效率和安全性评估',
    domain: '交通系统',
    status: '审核中',
    statusClass: 'review',
    creator: '李三',
    createTime: '2025-03-30',
    usageCount: 12,
    favorite: false,
    key: 'TPL-20250407-007',
  },
]

// 统计数据
const statsData = [
  {
    title: '评估模版总数',
    value: 30,
    trend: 'up',
    trendValue: '较上月增长 8 个',
    type: 'info',
    icon: 'cube',
  },
  {
    title: '已发布',
    value: 15,
    trend: 'up',
    trendValue: '较上月增长 5 个',
    type: 'success',
    icon: 'check-circle',
  },
  {
    title: '审核中',
    value: 10,
    trend: 'up',
    trendValue: '较上月增长 1 个',
    type: 'warning',
    icon: 'hourglass-half',
  },
  {
    title: '草稿',
    value: 5,
    trend: 'down',
    trendValue: '较上月减少 3 个',
    type: 'danger',
    icon: 'exclamation-circle',
  },
]

// 活动数据
const activitiesData = [
  {
    title: '张三发布了新模版"电力系统暂态仿真专项评估模版"',
    time: '今天 09:32',
    type: 'success',
  },
  {
    title: '王三提交了"供水管网压力仿真度评估模版"审核申请',
    time: '昨天 16:45',
    type: 'warning',
  },
  {
    title: '李三编辑了"城市交通流量仿真度评估模版"',
    time: '昨天 14:23',
    type: '',
  },
  {
    title: '您复制并创建了新模版"城市轨道交通运行仿真评估模版"',
    time: '04-02 11:05',
    type: 'success',
  },
  {
    title: '陈三归档了"通信网络容量评估模版"',
    time: '04-01 09:17',
    type: 'danger',
  },
  {
    title: '系统管理员更新了模版库版本控制策略',
    time: '04-01 08:30',
    type: '',
  },
]

// 模拟数据总量
const totalCount = 128

// 获取模版列表
export function getTemplateList(params) {
  const { pageSize = 7, pageNum = 1, keyword, status, domain } = params || {}

  let filteredData = [...templateListData]

  // 关键字搜索
  if (keyword) {
    const lowerKeyword = keyword.toLowerCase()
    filteredData = filteredData.filter(
      (item) =>
        item.name.toLowerCase().includes(lowerKeyword) ||
        item.id.toLowerCase().includes(lowerKeyword) ||
        item.creator.toLowerCase().includes(lowerKeyword),
    )
  }

  // 状态筛选
  if (status) {
    filteredData = filteredData.filter((item) => item.statusClass === status)
  }

  // 领域筛选
  if (domain) {
    filteredData = filteredData.filter((item) => item.domain === domain)
  }

  // 分页
  const startIndex = (pageNum - 1) * pageSize
  const endIndex = Math.min(startIndex + pageSize, totalCount)
  const paginatedData = filteredData.slice(startIndex, endIndex)

  return {
    code: 200,
    message: 'success',
    data: {
      list: paginatedData,
      total: totalCount,
      pageSize: Number(pageSize),
      pageNum: Number(pageNum),
    },
  }
}

// 获取模版详情
export function getTemplateDetail(id) {
  const template = templateListData.find((item) => item.id === id)

  if (!template) {
    return {
      code: 404,
      message: '模版不存在',
      data: null,
    }
  }

  return {
    code: 200,
    message: 'success',
    data: template,
  }
}

// 获取模版统计数据
export function getTemplateStats() {
  return {
    code: 200,
    message: 'success',
    data: statsData,
  }
}

// 获取模版活动日志
export function getTemplateActivities() {
  return {
    code: 200,
    message: 'success',
    data: activitiesData,
  }
}

// 切换模版收藏状态
export function toggleTemplateFavorite(id, isFavorite) {
  const template = templateListData.find((item) => item.id === id)

  if (!template) {
    return {
      code: 404,
      message: '模版不存在',
      data: null,
    }
  }

  if (isFavorite !== undefined) {
    template.favorite = isFavorite
  } else {
    template.favorite = !template.favorite
  }

  return {
    code: 200,
    message: template.favorite ? '收藏成功' : '取消收藏成功',
    data: null,
  }
}

// 删除模版
export function deleteTemplate(id) {
  const index = templateListData.findIndex((item) => item.id === id)

  if (index === -1) {
    return {
      code: 404,
      message: '模版不存在',
      data: null,
    }
  }

  templateListData.splice(index, 1)

  return {
    code: 200,
    message: '删除成功',
    data: null,
  }
}
