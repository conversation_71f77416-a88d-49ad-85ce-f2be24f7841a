<template>
  <div class="create-template-container">
    <div class="form-section">
      <div class="section-title">
        <div class="section-icon"><i class="fas fa-info-circle"></i></div>
        <span>基本信息</span>
      </div>
      <div class="form-row">
        <div class="form-col">
          <div class="form-group">
            <label class="form-label">模版名称</label>
            <a-input v-model:value="formData.name" placeholder="输入模版名称" class="form-input" />
          </div>
        </div>
        <div class="form-col">
          <div class="form-group">
            <label class="form-label">所属领域</label>
            <a-select v-model:value="formData.domain" class="form-select" style="width: 100%">
              <a-select-option value="multi">跨行业综合</a-select-option>
              <a-select-option value="power">电力系统</a-select-option>
              <a-select-option value="transportation">交通系统</a-select-option>
              <a-select-option value="water">水利系统</a-select-option>
              <a-select-option value="gas">燃气系统</a-select-option>
              <a-select-option value="communication">通信系统</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="form-label">模版描述</label>
        <a-textarea
          v-model:value="formData.description"
          placeholder="描述此模版的用途和适用场景"
          :rows="4"
          class="form-textarea"
        />
      </div>
      <div class="form-group">
        <label class="form-label">标签</label>
        <div class="tag-input-container">
          <a-tag
            v-for="(tag, index) in formData.tags"
            :key="index"
            closable
            @close="removeTag(index)"
            class="tag"
          >
            {{ tag }}
          </a-tag>
          <a-input
            v-if="inputVisible"
            ref="tagInputRef"
            v-model:value="tagInputValue"
            class="tag-input"
            size="small"
            :style="{ width: '100px' }"
            @blur="handleTagInputConfirm"
            @keyup.enter="handleTagInputConfirm"
          />
          <a-tag v-else class="tag-add" @click="showTagInput">
            <i class="fas fa-plus"></i> 添加标签
          </a-tag>
        </div>
      </div>
    </div>

    <div class="form-section">
      <div class="section-title">
        <div class="section-icon"><i class="fas fa-sitemap"></i></div>
        <span>指标体系</span>
      </div>
      <div class="tree-editor">
        <div class="tree-toolbar">
          <div class="tree-actions">
            <a-button class="tree-btn" @click="addRootIndicator">
              <i class="fas fa-plus"></i> 添加指标
            </a-button>
            <a-button class="tree-btn"> <i class="fas fa-file-import"></i> 导入指标 </a-button>
            <a-button class="tree-btn" @click="expandAllNodes"> <i class="fas fa-expand-alt"></i> 展开全部 </a-button>
          </div>
          <div class="tree-actions">
            <a-input
              v-if="searchVisible"
              v-model:value="searchValue"
              placeholder="搜索指标"
              class="search-input"
              @pressEnter="filterNodes"
              ref="searchInputRef"
            />
            <a-button class="tree-btn" @click="toggleSearch">
              <i class="fas fa-search"></i>
            </a-button>
            <!-- <a-button class="tree-btn" @click="clearIndicators">
              <i class="fas fa-trash-alt"></i> 清空
            </a-button> -->
          </div>
        </div>
        <div class="tree-container">
          <a-tree
            v-if="formData.indicators.length > 0"
            :tree-data="searchValue ? filteredTreeData : formData.indicators"
            :replaceFields="{
              key: 'id',
              title: 'title',
              children: 'children',
            }"
            show-line
            block-node
            :expandedKeys="expandedKeys"
            :autoExpandParent="autoExpandParent"
            @expand="onExpand"
          >
            <template #title="{ title, id, weight, type, algorithm, dataset }">
              <div class="node-content">
                <div class="node-icon">
                  <i :class="['fas', type === 'root' ? 'fa-layer-group' : 'fa-cube']"></i>
                </div>
                <div class="node-title">
                  <span
                    v-if="editingNodeId !== id"
                    @click.stop="startEditNodeName({id, title})"
                  >{{ title }}</span>
                  <a-input
                    v-else
                    v-model:value="editingNodeValue"
                    @blur="saveNodeName"
                    @keydown="handleNodeNameKeyDown"
                    class="node-title-input"
                    ref="nodeNameInputRef"
                    autoFocus
                  />
                </div>

                <a-popover
                    :visible="weightEditVisible && currentEditNodeId === id"
                    placement="right"
                    trigger="click"
                    :destroyTooltipOnHide="true"
                    @visibleChange="e => e ? editIndicator(id) : closeWeightEdit()"
                    overlayClassName="weight-popover"
                  >
                    <template #content>
                      <div class="weight-editor">
                        <div class="weight-title">调整{{weightNodes && weightNodes.find(n => n.id === currentEditNodeId).title || ''}}的权重分配</div>
                        <div class="weight-nodes">
                          <div
                            v-for="(node, index) in weightNodes"
                            :key="node.id"
                            class="weight-node-item"
                            :style="{
                              color: currentEditNodeId === node.id ? '#07f6ff' : '#e0e6f0'
                            }"
                          >
                            {{ index + 1 }}. {{ node.title }} ({{ node.weight }}%)
                          </div>
                        </div>

                        <div class="weight-slider-container">
                          <a-slider
                            v-if="weightNodes.length > 1"
                            v-model:value="sliderValues"
                            :min="0"
                            :max="100"
                            :step="1"
                            range
                            :marks="sliderMarks"
                            @change="handleSliderChange"
                          />
                        </div>

                        <div class="weight-actions">
                          <a-button class="weight-action-btn cancel" @click="closeWeightEdit">
                            取消
                          </a-button>
                          <a-button class="weight-action-btn confirm" @click="updateNodeWeights">
                            确认
                          </a-button>
                        </div>
                      </div>
                    </template>
                    <a-button class="node-action" @click.stop="editIndicator(id)">
                      <div class="node-weight"><i class="fas fa-edit"></i>权重: {{ weight }}%</div>
                    </a-button>
                  </a-popover>
                <div class="node-actions">
                  <a-button
                    class="node-action"
                    @click.stop="addChildIndicator(id)"
                    v-if="type === 'root'"
                  >
                    <i class="fas fa-plus"></i>
                  </a-button>
                  <a-button class="node-action" @click.stop="removeIndicator(id)">
                    <i class="fas fa-trash-alt"></i>
                  </a-button>
                </div>
              </div>
              <!-- 为叶子节点添加算法和数据采集任务选择器 -->
              <div v-if="type === 'leaf'" class="leaf-node-associations">
                <div class="association-item">
                  <span class="association-label">关联算法:</span>
                  <a-select
                    v-model:value="findNodeById(id).algorithm"
                    class="association-select"
                    show-search
                    placeholder="请选择算法"
                    :filter-option="filterOption"
                    @click.stop
                    :default-value="algorithm"
                  >
                    <a-select-option v-for="alg in algorithms" :key="alg.id" :value="alg.name">
                      {{ alg.name }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="association-item">
                  <span class="association-label">关联数据:</span>
                  <a-select
                    v-model:value="findNodeById(id).dataset"
                    class="association-select"
                    show-search
                    placeholder="请选择数据采集任务"
                    :filter-option="filterOption"
                    @click.stop
                    :default-value="dataset"
                  >
                    <a-select-option v-for="task in dataTasks" :key="task.id" :value="task.name">
                      {{ task.name }}
                    </a-select-option>
                  </a-select>
                </div>
              </div>
            </template>
          </a-tree>
          <div v-else class="empty-tree">
            <i class="fas fa-sitemap"></i>
            <p>暂无指标，请添加指标</p>
          </div>
        </div>
      </div>
    </div>

    <div class="form-section">
      <div class="section-title">
        <div class="section-icon"><i class="fas fa-file-alt"></i></div>
        <span>附件上传</span>
      </div>
      <a-upload-dragger
        v-model:fileList="fileList"
        :multiple="true"
        action="/api/upload"
        @change="handleUploadChange"
        class="file-upload-container"
      >
        <div class="upload-icon"><i class="fas fa-cloud-upload-alt"></i></div>
        <div class="upload-text">拖拽文件到此处或 <span class="upload-browse">浏览文件</span></div>
        <div class="upload-desc">支持 PDF, Word, Excel, ZIP 格式，单个文件不超过20MB</div>
      </a-upload-dragger>
      <div class="upload-file-list" v-if="fileList.length > 0">
        <div class="upload-file-item" v-for="(file, index) in fileList" :key="index">
          <div class="file-info">
            <div class="file-icon">
              <i
                :class="[
                  'fas',
                  file.type.includes('pdf')
                    ? 'fa-file-pdf'
                    : file.type.includes('excel') || file.type.includes('sheet')
                      ? 'fa-file-excel'
                      : file.type.includes('word')
                        ? 'fa-file-word'
                        : file.type.includes('zip') || file.type.includes('rar')
                          ? 'fa-file-archive'
                          : 'fa-file',
                ]"
              ></i>
            </div>
            <div>
              <div class="file-name">{{ file.name }}</div>
              <div class="file-size">{{ formatFileSize(file.size) }}</div>
            </div>
          </div>
          <div class="file-progress">
            <div
              class="file-progress-bar"
              :style="{ width: file.status === 'uploading' ? file.percent + '%' : '100%' }"
            ></div>
          </div>
          <div class="file-actions">
            <a-button class="file-action" @click="previewFile(file)">
              <i class="fas fa-eye"></i>
            </a-button>
            <a-button class="file-action delete" @click="removeFile(file)">
              <i class="fas fa-trash-alt"></i>
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { message, Popover, Slider } from 'ant-design-vue'

export default {
  name: 'CreateTemplate',
  components: {
    APopover: Popover,
    ASlider: Slider,
  },
  setup(props, { emit }) {
    // 表单数据
    const formData = reactive({
      name: '城市级联关基行业综合仿真度评估模版',
      domain: 'multi',
      description:
        '用于城市级关键基础设施跨行业协同仿真度评估的综合模版，覆盖电力-通信-交通三大行业的关键性能指标和交互影响，适用于城市灾害应急响应、关键基础设施级联故障分析及韧性评估等场景。',
      tags: ['跨行业仿真', '电力-通信-交通', '关键基础设施', '级联故障分析', '城市韧性评估'],
      indicators: [
        {
          id: '1',
          title: '电力系统仿真精度',
          weight: 35,
          type: 'root',
          children: [
            {
              id: '1-1',
              title: '配电网拓扑结构精度',
              weight: 30,
              type: 'leaf',
              algorithm: '电网拓扑一致性评估算法',
              dataset: '配电网GIS数据',
            },
            {
              id: '1-2',
              title: '负荷预测精度',
              weight: 35,
              type: 'leaf',
              algorithm: '负荷预测评估算法',
              dataset: '历史负荷数据',
            },
            {
              id: '1-3',
              title: '电网故障响应特性',
              weight: 35,
              type: 'leaf',
              algorithm: '电网暂态响应评估算法',
              dataset: '故障录波数据',
            },
          ],
        },
        {
          id: '2',
          title: '通信网络仿真精度',
          weight: 30,
          type: 'root',
          children: [
            {
              id: '2-1',
              title: '通信网络容量仿真精度',
              weight: 40,
              type: 'leaf',
              algorithm: '网络流量模型评估算法',
              dataset: '通信网络流量数据',
            },
            {
              id: '2-2',
              title: '通信时延仿真精度',
              weight: 35,
              type: 'leaf',
              algorithm: '网络时延评估算法',
              dataset: '通信网络RTT数据',
            },
            {
              id: '2-3',
              title: '通信网络覆盖精度',
              weight: 25,
              type: 'leaf',
              algorithm: '信号覆盖评估算法',
              dataset: '基站覆盖测试数据',
            },
          ],
        },
      ],
    })

    // 标签相关
    const tagInputRef = ref(null)
    const inputVisible = ref(false)
    const tagInputValue = ref('')

    // 显示标签输入框
    const showTagInput = () => {
      inputVisible.value = true
      nextTick(() => {
        tagInputRef.value?.focus()
      })
    }

    // 处理标签输入确认
    const handleTagInputConfirm = () => {
      if (tagInputValue.value && formData.tags.indexOf(tagInputValue.value) === -1) {
        formData.tags.push(tagInputValue.value)
      }
      inputVisible.value = false
      tagInputValue.value = ''
    }

    // 移除标签
    const removeTag = (index) => {
      formData.tags.splice(index, 1)
    }

    // 文件上传相关
    const fileList = ref([
      {
        uid: '-1',
        name: '城市级联关基行业仿真度测评方法.pdf',
        status: 'done',
        size: 3200000,
        type: 'application/pdf',
        url: 'https://example.com/file1.pdf',
      },
      {
        uid: '-2',
        name: '跨行业指标体系及权重.xlsx',
        status: 'done',
        size: 2100000,
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        url: 'https://example.com/file2.xlsx',
      },
    ])

    // 处理文件上传变化
    const handleUploadChange = (info) => {
      fileList.value = [...info.fileList]

      // 上传状态处理
      const status = info.file.status
      if (status === 'done') {
        message.success(`${info.file.name} 文件上传成功`)
      } else if (status === 'error') {
        message.error(`${info.file.name} 文件上传失败`)
      }
    }

    // 预览文件
    const previewFile = (file) => {
      console.log('预览文件', file)
      window.open(file.url || file.response?.url)
    }

    // 移除文件
    const removeFile = (file) => {
      const index = fileList.value.indexOf(file)
      const newFileList = fileList.value.slice()
      newFileList.splice(index, 1)
      fileList.value = newFileList
    }

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 算法列表示例数据
    const algorithms = ref([
      { id: '1', name: '电网拓扑一致性评估算法', description: '用于评估电网拓扑结构的精确性' },
      { id: '2', name: '负荷预测评估算法', description: '用于评估负荷预测的准确性' },
      { id: '3', name: '电网暂态响应评估算法', description: '用于评估电网在故障状态下的响应特性' },
      { id: '4', name: '网络流量模型评估算法', description: '用于评估通信网络流量模型的准确性' },
      { id: '5', name: '网络时延评估算法', description: '用于评估通信网络时延模型的准确性' },
      { id: '6', name: '信号覆盖评估算法', description: '用于评估通信网络覆盖模型的准确性' },
      { id: '7', name: '交通流量评估算法', description: '用于评估交通流量仿真模型的准确性' },
      { id: '8', name: '交通信号控制评估算法', description: '用于评估交通信号灯控制仿真的准确性' },
    ])

    // 数据采集任务列表示例数据
    const dataTasks = ref([
      { id: '1', name: '配电网GIS数据', description: '配电网空间地理信息数据' },
      { id: '2', name: '历史负荷数据', description: '过去30天的电力负荷数据' },
      { id: '3', name: '故障录波数据', description: '电网故障事件的录波数据' },
      { id: '4', name: '通信网络流量数据', description: '通信网络节点间的流量数据' },
      { id: '5', name: '通信网络RTT数据', description: '通信网络节点间的往返时延数据' },
      { id: '6', name: '基站覆盖测试数据', description: '移动通信基站覆盖范围测试数据' },
      { id: '7', name: '交通流量监测数据', description: '主要交叉路口的交通流量监测数据' },
      { id: '8', name: '信号灯控制参数数据', description: '交通信号灯控制参数记录数据' },
    ])

    // 权重编辑相关
    const weightEditVisible = ref(false)
    const currentEditNodeId = ref(null)
    const siblingNodes = ref([])
    const sliderMarks = ref({})
    const sliderValues = ref([])
    const weightNodes = ref([])

    // 搜索过滤相关
    const searchVisible = ref(false)
    const searchValue = ref('')
    const searchInputRef = ref(null)
    const expandedKeys = ref([])
    const autoExpandParent = ref(true)
    const filteredTreeData = ref([...formData.indicators]) // 用于存储过滤后的树数据

    // 搜索过滤方法
    const filterOption = (input, option) => {
      return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 根据ID查找节点
    const findNodeById = (id) => {
      let foundNode = null

      const traverse = (nodes) => {
        for (const node of nodes) {
          if (node.id === id) {
            foundNode = node
            return true
          }
          if (node.children && node.children.length > 0) {
            if (traverse(node.children)) {
              return true
            }
          }
        }
        return false
      }

      traverse(formData.indicators)
      return foundNode
    }

    // 指标体系相关
    const addRootIndicator = () => {
      const id = Date.now().toString()
      formData.indicators.push({
        id,
        title: '新指标',
        weight: 10,
        type: 'root',
        children: [],
      })
      expandedKeys.value = [...expandedKeys.value, id] // 展开新添加的节点
    }

    const addChildIndicator = (parentId) => {
      const findParentAndAddChild = (items) => {
        for (let i = 0; i < items.length; i++) {
          if (items[i].id === parentId) {
            if (!items[i].children) {
              items[i].children = []
            }
            const childId = parentId + '-' + (items[i].children.length + 1)
            items[i].children.push({
              id: childId,
              title: '新子指标',
              weight: 10,
              type: 'leaf',
              algorithm: '',
              dataset: '',
            })
            expandedKeys.value = [...expandedKeys.value, childId] // 展开新添加的节点
            return true
          }
          if (items[i].children) {
            if (findParentAndAddChild(items[i].children)) {
              return true
            }
          }
        }
        return false
      }

      findParentAndAddChild(formData.indicators)
    }

    const editIndicator = (id) => {
      // 查找当前节点及其同级节点
      const node = findNodeById(id)
      if (!node) return

      // 保存当前正在编辑的节点ID
      currentEditNodeId.value = id

      // 查找同级节点（包括自身）
      const parentId = getParentIdFromNodeId(id)
      siblingNodes.value = findSiblingNodes(id, parentId)
      weightNodes.value = [...siblingNodes.value] // 创建副本用于编辑

      // 按权重排序
      weightNodes.value.sort((a, b) => a.weight - b.weight)

      // 准备滑块标记和值
      prepareSliderValues()

      // 显示编辑弹窗
      weightEditVisible.value = true
    }

    // 获取节点ID的父ID
    const getParentIdFromNodeId = (id) => {
      const parts = id.split('-')
      if (parts.length <= 1) return null // 根节点没有父节点
      return parts.slice(0, parts.length - 1).join('-')
    }

    // 查找同级节点（包括自身）
    const findSiblingNodes = (id, parentId) => {
      let siblings = []

      // 如果是根节点
      if (!parentId) {
        siblings = formData.indicators
      } else {
        // 如果是子节点，查找其父节点
        const parent = findNodeById(parentId)
        if (parent && parent.children) {
          siblings = parent.children
        }
      }

      return siblings
    }

    // 准备滑块的值和标记
    const prepareSliderValues = () => {
      if (weightNodes.value.length <= 1) return

      // 计算累积权重位置
      let cumulativeWeight = 0
      sliderValues.value = []
      sliderMarks.value = {}

      // 对于n个节点，我们需要n-1个滑块控制点
      for (let i = 0; i < weightNodes.value.length - 1; i++) {
        cumulativeWeight += weightNodes.value[i].weight
        sliderValues.value.push(cumulativeWeight)
      }
    }

    // 更新节点权重
    const updateNodeWeights = () => {
      if (weightNodes.value.length <= 1) return

      let prevPosition = 0

      // 根据滑块位置计算每个节点的权重
      for (let i = 0; i < weightNodes.value.length; i++) {
        const position = i < sliderValues.value.length ? sliderValues.value[i] : 100
        weightNodes.value[i].weight = position - prevPosition
        prevPosition = position
      }

      // 应用权重更改到原始节点
      for (const node of weightNodes.value) {
        const originalNode = findNodeById(node.id)
        if (originalNode) {
          originalNode.weight = node.weight
        }
      }

      // 关闭弹窗
      closeWeightEdit()
    }

    // 关闭权重编辑
    const closeWeightEdit = () => {
      weightEditVisible.value = false
      currentEditNodeId.value = null
    }

    // 处理滑块值变化
    const handleSliderChange = (values) => {
      // 确保值按升序排列
      sliderValues.value = [...values].sort((a, b) => a - b)

      // 实时更新节点权重
      let prevPosition = 0

      // 根据滑块位置计算每个节点的权重
      for (let i = 0; i < weightNodes.value.length; i++) {
        const position = i < sliderValues.value.length ? sliderValues.value[i] : 100
        weightNodes.value[i].weight = position - prevPosition
        prevPosition = position
      }
    }

    const removeIndicator = (id) => {
      const removeItem = (items) => {
        for (let i = 0; i < items.length; i++) {
          if (items[i].id === id) {
            items.splice(i, 1)
            return true
          }
          if (items[i].children) {
            if (removeItem(items[i].children)) {
              return true
            }
          }
        }
        return false
      }

      removeItem(formData.indicators)
    }

    const clearIndicators = () => {
      formData.indicators = []
      expandedKeys.value = [] // 清空时也清空展开的节点
    }

    // 提交表单
    const submitForm = () => {
      console.log('提交表单', formData)
      emit('submit', formData)
    }

    // 编辑节点名称相关
    const editingNodeId = ref(null)
    const editingNodeValue = ref('')
    const nodeNameInputRef = ref(null) // 新增的 ref

    // 开始编辑节点名称
    const startEditNodeName = (node) => {
      editingNodeId.value = node.id
      editingNodeValue.value = node.title
      nextTick(() => {
        nodeNameInputRef.value?.focus()
      })
    }

    // 保存节点名称
    const saveNodeName = () => {
      if (editingNodeId.value) {
        const node = findNodeById(editingNodeId.value)
        if (node && editingNodeValue.value.trim() !== '') {
          node.title = editingNodeValue.value.trim()
        }
        editingNodeId.value = null
      }
    }

    // 取消编辑节点名称
    const cancelEditNodeName = () => {
      editingNodeId.value = null
    }

    // 处理编辑节点名称的键盘事件
    const handleNodeNameKeyDown = (e) => {
      if (e.key === 'Enter') {
        saveNodeName()
      } else if (e.key === 'Escape') {
        cancelEditNodeName()
      }
    }

    // 展开/折叠所有节点
    const expandAllNodes = () => {
      const traverse = (nodes) => {
        for (const node of nodes) {
          if (node.children && node.children.length > 0) {
            expandedKeys.value.push(node.id)
            traverse(node.children)
          }
        }
      }
      traverse(formData.indicators)
      autoExpandParent.value = true
    }

    // 过滤树节点
    const filterNodes = () => {
      if (!searchValue.value) {
        filteredTreeData.value = formData.indicators
        expandedKeys.value = []
        autoExpandParent.value = true
        return
      }

      const filter = (nodes) => {
        const filtered = []
        for (const node of nodes) {
          const matches = node.title.toLowerCase().includes(searchValue.value.toLowerCase())
          if (matches) {
            filtered.push(node)
          }
          if (node.children && node.children.length > 0) {
            const children = filter(node.children)
            if (children.length > 0) {
              filtered.push({ ...node, children })
            }
          }
        }
        return filtered
      }
      filteredTreeData.value = filter(formData.indicators)
      expandedKeys.value = []
      autoExpandParent.value = true
    }

    // 切换搜索框显示
    const toggleSearch = () => {
      searchVisible.value = !searchVisible.value
      if (searchVisible.value) {
        nextTick(() => {
          searchInputRef.value?.focus()
        })
      }
    }

    // 监听树节点展开/收起事件
    const onExpand = (keys) => {
      expandedKeys.value = [...keys]
      autoExpandParent.value = false
    }

    return {
      formData,
      tagInputRef,
      inputVisible,
      tagInputValue,
      showTagInput,
      handleTagInputConfirm,
      removeTag,
      fileList,
      handleUploadChange,
      previewFile,
      removeFile,
      formatFileSize,
      addRootIndicator,
      addChildIndicator,
      editIndicator,
      removeIndicator,
      clearIndicators,
      submitForm,
      algorithms,
      dataTasks,
      filterOption,
      findNodeById,
      // 权重编辑相关
      weightEditVisible,
      currentEditNodeId,
      siblingNodes,
      weightNodes,
      sliderMarks,
      sliderValues,
      closeWeightEdit,
      updateNodeWeights,
      handleSliderChange,
      // 搜索过滤相关
      searchVisible,
      searchValue,
      searchInputRef,
      expandedKeys,
      autoExpandParent,
      filteredTreeData,
      filterNodes,
      toggleSearch,
      onExpand,
      expandAllNodes,
      // 编辑节点名称相关
      editingNodeId,
      editingNodeValue,
      startEditNodeName,
      saveNodeName,
      cancelEditNodeName,
      handleNodeNameKeyDown,
      nodeNameInputRef,
    }
  },
}
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.create-template-container {
  color: #e0e6f0;
  font-size: @font-size-md-vw;
}

.form-section {
  margin-bottom: 2vw;
  padding: 1vw;

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 1vw;
    padding-bottom: 0.5vw;
    border-bottom: 1px solid rgba(25, 164, 255, 0.5);

    .section-icon {
      margin-right: 0.5vw;
      color: #07f6ff;
      font-size: @font-size-md-vw;
    }

    span {
      font-size: @font-size-md-vw;
      color: #e0e6f0;
      font-weight: bold;
      text-shadow: 0 0 5px rgba(7, 246, 255, 0.5);
    }
  }
}

.form-row {
  display: flex;
  gap: 1vw;
  margin-bottom: 1vw;

  .form-col {
    flex: 1;
  }
}

.form-group {
  margin-bottom: 1vw;

  .form-label {
    display: block;
    margin-bottom: 0.5vw;
    color: #07f6ff;
    font-size: @font-size-md-vw;
  }

  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.05);
    color: #e0e6f0;
    transition: all 0.3s ease;
    font-size: @font-size-md-vw;

    &:focus,
    &:hover {
      border-color: #07f6ff;
      box-shadow: 0 0 5px rgba(7, 246, 255, 0.5);
    }
  }

  // 为表单元素增加统一的内边距
  :deep(.ant-select-selector) {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(25, 164, 255, 0.5) !important;
    color: #e0e6f0 !important;
    font-size: @font-size-md-vw !important;
    height: 2.5vw !important; /* 保持与输入框相同高度 */
    line-height: 2.5vw !important;
    padding-top: 0 !important; /* 移除额外内边距 */
    padding-bottom: 0 !important;
  }

  :deep(.ant-select-selection-item) {
    font-size: @font-size-md-vw !important;
    line-height: 2.5vw !important; /* 垂直居中 */
  }

  :deep(.ant-select-arrow) {
    color: #07f6ff !important;
  }

  :deep(.ant-input) {
    font-size: @font-size-md-vw !important;
  }

  :deep(.ant-input-textarea) {
    font-size: @font-size-md-vw !important;
  }
}

.tag-input-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5vw;
  padding: 0.5vw;
  border: 1px solid rgba(25, 164, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.05);
  min-height: 2.5vw;
}

.tag {
  display: flex;
  align-items: center; /* 确保标签内容垂直居中 */
  padding: 0.2vw 0.5vw;
  background-color: rgba(7, 246, 255, 0.2);
  border: 1px solid rgba(7, 246, 255, 0.5);
  border-radius: 0.2vw;
  color: #e0e6f0;
  font-size: @font-size-md-vw;
  height: 2vw; /* 添加固定高度 */
  line-height: 1.5vw; /* 确保文字垂直居中 */

  :deep(span) {
    font-size: @font-size-md-vw !important;
    display: flex; /* 确保内部元素也垂直居中 */
    align-items: center;
  }

  .tag-close {
    margin-left: 0.5vw;
    cursor: pointer;
    color: #e0e6f0;

    &:hover {
      color: #f25767;
    }
  }
}

.tag-add {
  cursor: pointer;
  color: #07f6ff;
  background-color: transparent !important;
  border: 1px dashed rgba(7, 246, 255, 0.5) !important;
  font-size: @font-size-md-vw !important;
  height: 2vw; /* 添加固定高度 */
  display: flex; /* 确保内容垂直居中 */
  align-items: center;

  :deep(span) {
    font-size: @font-size-md-vw !important;
    display: flex; /* 确保内部元素也垂直居中 */
    align-items: center;
  }

  &:hover {
    border-color: #07f6ff !important;
    background-color: rgba(7, 246, 255, 0.1) !important;
  }
}

.tag-input {
  border: none;
  background: transparent;
  color: #e0e6f0;
  outline: none;
  flex: 1;
  min-width: 100px;
  font-size: @font-size-md-vw;
}

// 树形编辑器样式
.tree-editor {
  border: 1px solid rgba(25, 164, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.02);
}

.tree-toolbar {
  display: flex;
  justify-content: space-between;
  padding: 0.5vw;
  border-bottom: 1px solid rgba(25, 164, 255, 0.5);
  background-color: rgba(25, 164, 255, 0.1);
}

.tree-actions {
  display: flex;
  gap: 0.5vw;
}

.tree-btn {
  background-color: transparent;
  border: 1px solid rgba(25, 164, 255, 0.5);
  color: #07f6ff;
  font-size: @font-size-md-vw;
  padding: 0.2vw 0.5vw;
  height: auto;

  :deep(span) {
    font-size: @font-size-md-vw !important;
  }

  &:hover {
    background-color: rgba(7, 246, 255, 0.1);
    border-color: #07f6ff;
  }
}

.tree-container {
  padding: 1vw;
  overflow-y: auto;
}

.empty-tree {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 10vw;
  color: rgba(255, 255, 255, 0.3);

  i {
    font-size: 3vw;
    margin-bottom: 1vw;
  }

  p {
    font-size: @font-size-md-vw;
  }
}

:deep(.ant-tree) {
  background-color: transparent;
  color: #e0e6f0;
  font-size: @font-size-md-vw !important;

  .ant-tree-node-content-wrapper {
    color: #e0e6f0;
    font-size: @font-size-md-vw !important;
  }

  .ant-tree-title {
    font-size: @font-size-md-vw !important;
  }

  .ant-tree-node-content-wrapper:hover {
    background-color: rgba(7, 246, 255, 0.1);
  }

  .ant-tree-node-selected {
    background-color: rgba(7, 246, 255, 0.2);
  }
}

.node-content {
  display: flex;
  align-items: center;
  padding: 0.2vw 0;
  font-size: @font-size-md-vw;
}

.node-icon {
  margin-right: 0.5vw;
  color: #07f6ff;
  font-size: @font-size-md-vw;
}

.node-title {
  flex: 1;
  margin-right: 1vw;
  font-size: @font-size-md-vw;
}

.node-title-input {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.05);
  color: #e0e6f0;
  border: 1px solid rgba(25, 164, 255, 0.5);
  border-radius: 0.2vw;
  padding: 0.2vw 0.5vw;
  font-size: @font-size-md-vw;
  transition: all 0.3s ease;

  &:focus {
    border-color: #07f6ff;
    box-shadow: 0 0 5px rgba(7, 246, 255, 0.5);
  }
}

.node-weight {
  margin-right: 1vw;
  color: rgba(255, 255, 255, 0.7);
  font-size: @font-size-md-vw;
}

.node-actions {
  display: flex;
  gap: 0.2vw;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.node-content:hover .node-actions {
  opacity: 1;
}

.node-action {
  background: none;
  border: none;
  color: #07f6ff;
  cursor: pointer;
  padding: 0.1vw 0.3vw;
  font-size: @font-size-md-vw;

  :deep(span) {
    font-size: @font-size-md-vw !important;
  }

  &:hover {
    color: #e0e6f0;
  }
}

/* 叶子节点关联样式 */
.leaf-node-associations {
  margin-top: 0.5vw;
  padding-left: 1.5vw;
  border-left: 1px dashed rgba(7, 246, 255, 0.3);
}

.association-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5vw;
  padding: 0.3vw 0;
}

.association-label {
  width: 5vw;
  color: rgba(224, 230, 240, 0.7);
  font-size: @font-size-md-vw;
  margin-right: 0.5vw;
}

.association-select {
  flex: 1;
  min-width: 10vw;

  :deep(.ant-select-selector) {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(25, 164, 255, 0.3) !important;
    height: 2.5vw !important;
    line-height: 2.5vw !important;
  }

  :deep(.ant-select-selection-item) {
    color: #e0e6f0 !important;
    font-size: @font-size-md-vw !important;
    line-height: 2.5vw !important;
  }

  :deep(.ant-select-arrow) {
    color: #07f6ff !important;
  }
}

/* 确保下拉菜单样式与主题一致 */
:deep(.ant-select-dropdown) {
  background-color: rgba(16, 22, 36, 0.95) !important;
  border: 1px solid rgba(7, 246, 255, 0.3);

  .ant-select-item {
    color: #e0e6f0;
    font-size: @font-size-md-vw !important;

    &:hover {
      background-color: rgba(7, 246, 255, 0.1) !important;
    }

    &-option-selected {
      background-color: rgba(7, 246, 255, 0.2) !important;
      color: #07f6ff !important;
    }
  }
}

/* 权重编辑器样式 */
:deep(.weight-popover) {
  .ant-popover-inner {
    background-color: rgba(10, 19, 39, 0.95) !important;
    border: 1px solid rgba(7, 246, 255, 0.3);
    border-radius: 0.2vw;
    box-shadow: 0 0 15px rgba(7, 246, 255, 0.2);
  }

  .ant-popover-arrow {
    display: none;
  }
}

.weight-editor {
  width: 20vw;
  padding: 0.5vw;
  color: #e0e6f0;
}

.weight-title {
  font-size: @font-size-md-vw;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 0.5vw;
}

.weight-description {
  font-size: @font-size-md-vw;
  color: rgba(224, 230, 240, 0.7);
  margin-bottom: 1vw;
}

.weight-nodes {
  margin-bottom: 1vw;
  max-height: 10vw;
  overflow-y: auto;
  border-top: 1px solid rgba(7, 246, 255, 0.2);
  border-bottom: 1px solid rgba(7, 246, 255, 0.2);
  padding: 0.5vw 0;
}

.weight-node-item {
  padding: 0.3vw 0;
  font-size: @font-size-md-vw;
}

.weight-slider-container {
  margin: 1vw 0;
  padding: 0.5vw 0;

  :deep(.ant-slider) {
    margin: 1vw 0;
  }

  :deep(.ant-slider-rail) {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  :deep(.ant-slider-track) {
    background-color: rgba(7, 246, 255, 0.5) !important;
  }

  :deep(.ant-slider-handle) {
    border-color: #07f6ff !important;
    box-shadow: 0 0 5px rgba(7, 246, 255, 0.5) !important;

    &:focus {
      box-shadow: 0 0 8px rgba(7, 246, 255, 0.8) !important;
    }
  }

  :deep(.ant-slider-mark-text) {
    color: rgba(224, 230, 240, 0.7) !important;
    font-size: @font-size-md-vw !important;
  }
}

.weight-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5vw;
  margin-top: 1vw;
}

.weight-action-btn {
  background-color: transparent;
  border: 1px solid rgba(25, 164, 255, 0.5);
  color: white;
  font-size: @font-size-md-vw;
  height: auto;
  padding: 0.2vw 0.8vw;

  &:hover {
    color: white;
    background-color: rgba(7, 246, 255, 0.1);
  }

  &.confirm {
    background-color: rgba(7, 246, 255, 0.2);
    color: white;

    &:hover {
      background-color: rgba(7, 246, 255, 0.3);
      border-color: white;
    }
  }
}

/* 文件上传样式 */
.file-upload-container {
  &:hover {
    border-color: #07f6ff;
    background-color: rgba(7, 246, 255, 0.05);
  }

  .upload-icon {
    font-size: 2vw;
    color: #07f6ff;
    margin-bottom: 0.5vw;
  }

  .upload-text {
    color: #e0e6f0;
    margin-bottom: 0.5vw;
    font-size: @font-size-md-vw;

    .upload-browse {
      color: #07f6ff;
      text-decoration: underline;
      cursor: pointer;
      font-size: @font-size-md-vw;
    }
  }

  .upload-desc {
    color: rgba(255, 255, 255, 0.5);
    font-size: @font-size-md-vw;
  }
}

:deep(.ant-upload-list) {
  display: none;
}

:deep(.ant-upload-drag) {
  background-color: transparent !important;
  border: none !important;
  font-size: @font-size-md-vw !important;
  position: relative !important;
  z-index: 2 !important;
  width: 100% !important;
  height: auto !important;

  .ant-upload-btn {
    font-size: @font-size-md-vw !important;
    position: relative !important;
    z-index: 3 !important;
  }

  .ant-upload-text {
    font-size: @font-size-md-vw !important;
  }

  .ant-upload-hint {
    font-size: @font-size-md-vw !important;
  }
}

.upload-file-list {
  margin-top: 1vw;
}

.upload-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5vw;
  margin-bottom: 0.5vw;
  border: 1px solid rgba(25, 164, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.05);
  font-size: @font-size-md-vw;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;

  .file-icon {
    margin-right: 0.5vw;
    color: #07f6ff;
    font-size: @font-size-md-vw;
  }

  .file-name {
    color: #e0e6f0;
    font-size: @font-size-md-vw;
  }

  .file-size {
    color: rgba(255, 255, 255, 0.5);
    font-size: @font-size-md-vw;
  }
}

.file-progress {
  flex: 1;
  height: 0.3vw;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 0 1vw;

  .file-progress-bar {
    height: 100%;
    background-color: #07f6ff;
  }
}

.file-actions {
  display: flex;
  gap: 0.5vw;

  .file-action {
    background: none;
    border: none;
    color: #07f6ff;
    cursor: pointer;
    font-size: @font-size-md-vw;

    :deep(span) {
      font-size: @font-size-md-vw !important;
    }

    &:hover {
      color: #e0e6f0;
    }

    &.delete:hover {
      color: #f25767;
    }
  }
}

.search-input {
  width: 15vw;
  background-color: rgba(255, 255, 255, 0.05);
  color: #e0e6f0;
  border: 1px solid rgba(25, 164, 255, 0.5);
  border-radius: 0.2vw;
  padding: 0.2vw 0.5vw;
  font-size: @font-size-md-vw;
  transition: all 0.3s ease;

  &:focus {
    border-color: #07f6ff;
    box-shadow: 0 0 5px rgba(7, 246, 255, 0.5);
  }
}
</style>
