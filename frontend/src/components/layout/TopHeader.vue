<template>
  <header class="top-header">
    <!-- 左侧区域：返回按钮 + 徽标 -->
    <div class="header-left">
      <button class="back-btn" @click="handleBack">
        <img src="../../assets/images/back.png" alt="返回" class="back-icon" />
        <span class="back-text">返回</span>
      </button>
    </div>

    <!-- 中央区域：状态指示器 -->
    <div class="header-center"></div>
  </header>

  <!-- 用户信息栏 (独立于header) -->
  <div class="user-bar">
    <div class="user-info">
      <img :src="layoutStore.userAvatar" :alt="layoutStore.userName" class="user-avatar" />
      <div class="username">{{ layoutStore.userName }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout'

// 布局 store
const layoutStore = useLayoutStore()

// 事件处理
const handleBack = () => {
  // 返回逻辑，可以是路由后退或自定义逻辑
  if (window.history.length > 1) {
    window.history.back()
  } else {
    // 如果没有历史记录，返回首页
    console.log('返回首页')
  }
}

// 无需显式暴露属性
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.top-header {
  height: @layout-header-height;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  position: relative;
  background: transparent;

  // 背景透明，移除原有科技风效果
  backdrop-filter: none;
  border-bottom: none;
  box-shadow: none;
}

// ========== 左侧区域 ==========
.header-left {
  display: flex;
  align-items: center;
}

.back-btn {
  top: @header-back-top;
  left: @header-back-left;
  // 使用原型中的返回按钮样式
  .back-button();
  border: none;
  padding: 0;

  &:hover {
    border-color: transparent;
    background: transparent;
    box-shadow: none;
    transform: none;
  }

  .back-icon {
    width: @icon-back-width;
    height: @icon-back-height;
    filter: none;
  }

  .back-text {
    font-size: @font-size-text;
    color: @text-white;
  }
}

// ========== 中央区域 ==========
.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

// ========== 用户信息栏 ==========
.user-bar {
  position: absolute;
  right: @header-user-right;
  top: @header-user-top;
  color: @text-white;
  font-size: @font-size-text;
  z-index: @z-index-user-bar;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 0;
  transition: all @transition-base;
  cursor: pointer;

  &:hover {
    border-color: transparent;
    background: transparent;
    box-shadow: none;
  }

  .user-avatar {
    width: @icon-avatar-size;
    height: @icon-avatar-size;
    min-width: @icon-avatar-size;
    min-height: @icon-avatar-size;
    margin-top: @margin-xs-vw;
    border: none !important;
    border-radius: 50%;

    &:hover {
      border: none !important;
      box-shadow: none;
    }
  }

  .username {
    color: @text-white;
    font-size: @font-size-text;
    font-weight: @font-weight-normal;
    text-shadow: none;
    margin-left: @icon-margin-left;
  }
}

// 结束样式
</style>
