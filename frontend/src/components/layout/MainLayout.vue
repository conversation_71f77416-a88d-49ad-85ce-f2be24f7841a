<template>
  <!-- 根据路由元数据决定是否使用简化布局 -->
  <div v-if="isSimpleLayout" class="simple-layout">
    <router-view />
  </div>
  <div v-else class="main-layout">
    <!-- 顶部导航 -->
    <TopHeader />

    <!-- 主要Tab区域 (合并TabNavigation和main内容) -->
    <div class="tab-container taskflow-main">
      <a-tabs v-model:activeKey="activeTab" class="main-tabs" @change="handleTabChange">
        <!-- 添加仪表盘标签 -->
        <a-tab-pane key="dashboard" :tab="renderTabWithIcon('仪表盘', 'dashboard')">
          <div class="content-title">
            <img src="@/assets/images/sub-title.png" class="title-bg" />
            <span class="title-text">{{ layoutStore.pageTitle }}</span>
          </div>
          <div class="tab-content">
            <router-view v-if="activeTab === 'dashboard'" name="dashboard" />
            <Dashboard v-if="activeTab === 'dashboard'" />
          </div>
        </a-tab-pane>

        <a-tab-pane key="template-manager" :tab="renderTabWithIcon('模版管理', 'template-manager')">
          <div class="content-title">
            <img src="@/assets/images/sub-title.png" class="title-bg" />
            <span class="title-text">{{ layoutStore.pageTitle }}</span>
          </div>
          <div class="tab-content">
            <router-view v-if="activeTab === 'template-manager'" name="template-manager" />
            <TemplateManager v-if="activeTab === 'template-manager'" />
          </div>
        </a-tab-pane>

        <a-tab-pane key="algorithm-library" :tab="renderTabWithIcon('算法库', 'algorithm-library')">
          <div class="content-title">
            <img src="@/assets/images/sub-title.png" class="title-bg" />
            <span class="title-text">{{ layoutStore.pageTitle }}</span>
          </div>
          <div class="tab-content">
            <router-view v-if="activeTab === 'algorithm-library'" name="algorithm-library" />
            <AlgorithmLibrary v-if="activeTab === 'algorithm-library'" />
          </div>
        </a-tab-pane>

        <a-tab-pane key="data-collection" :tab="renderTabWithIcon('数据采集', 'data-collection')">
          <div class="content-title">
            <img src="@/assets/images/sub-title.png" class="title-bg" />
            <span class="title-text">{{ layoutStore.pageTitle }}</span>
          </div>
          <div class="tab-content">
            <router-view v-if="activeTab === 'data-collection'" name="data-collection" />
            <DataCollection v-if="activeTab === 'data-collection'" />
          </div>
        </a-tab-pane>

        <a-tab-pane key="evaluation-task" :tab="renderTabWithIcon('评估任务', 'evaluation-task')">
          <div class="content-title">
            <img src="@/assets/images/sub-title.png" class="title-bg" />
            <span class="title-text">{{ layoutStore.pageTitle }}</span>
          </div>
          <div class="tab-content">
            <router-view v-if="activeTab === 'evaluation-task'" name="evaluation-task" />
            <EvaluationTask v-if="activeTab === 'evaluation-task'" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, h, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLayoutStore } from '@/stores/layout'
import TopHeader from './TopHeader.vue'

// 动态导入页面组件
import Dashboard from '@/views/Dashboard/index.vue'
import TemplateManager from '@/views/TemplateManager/index.vue'
import AlgorithmLibrary from '@/views/AlgorithmLibrary/index.vue'
import DataCollection from '@/views/DataCollection/index.vue'
import EvaluationTask from '@/views/EvaluationTask/index.vue'

const route = useRoute()
const router = useRouter()
const layoutStore = useLayoutStore()

// 是否使用简化布局（无侧边栏）
const isSimpleLayout = computed(() => {
  return route.meta.hideSidebar === true
})

// 当前激活的Tab
const activeTab = ref('dashboard')

// 路由路径到Tab key的映射
const routeToTabMap: Record<string, string> = {
  '/': 'dashboard',
  '/dashboard': 'dashboard',
  '/template-manager': 'template-manager',
  '/algorithm-library': 'algorithm-library',
  '/data-collection': 'data-collection',
  '/evaluation-task': 'evaluation-task',
}

// Tab key到路由路径的映射
const tabToRouteMap: Record<string, string> = {
  'dashboard': '/dashboard',
  'template-manager': '/template-manager',
  'algorithm-library': '/algorithm-library',
  'data-collection': '/data-collection',
  'evaluation-task': '/evaluation-task',
}

// 渲染带图标的Tab标题
const renderTabWithIcon = (title: string, key: string) => {
  const isActive = activeTab.value === key
  const iconName = isActive ? `${key}-active.png` : `${key}-normal.png`

  return h('div', { class: 'tab-title-with-icon' }, [
    h('img', {
      src: new URL(`../../assets/images/${iconName}`, import.meta.url).href,
      class: 'tab-icon',
      alt: `${title} 图标`,
    }),
    h('span', { class: 'tab-title' }, title),
  ])
}

// 根据当前路由设置激活的Tab和页面标题
const updateFromRoute = () => {
  // 提取基本路径（不含查询参数）
  const basePath = route.path.split('?')[0]

  // 设置激活的Tab
  const tabKey = routeToTabMap[basePath] || 'dashboard'
  activeTab.value = tabKey

  // 设置页面标题
  if (Object.keys(route.query).length > 0) {
    layoutStore.setCustomTitleByQuery(route.path, route.query as Record<string, string>)
  } else {
    layoutStore.setTitleByPath(route.path)
  }
}

// 监听路由变化
watch(() => route.path, updateFromRoute, { immediate: true })
watch(
  () => route.query,
  () => updateFromRoute(),
  { deep: true },
)

// Tab切换处理
const handleTabChange = (key: string) => {
  const routePath = tabToRouteMap[key]
  if (routePath && route.path !== routePath) {
    router.push(routePath)
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.main-layout {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  // 确保布局占满全屏
  margin: 0;
  padding: 0;
}

.tab-container {
  // 使用taskFlow-main样式混入
  .taskflow-main();
}

// 自定义Tab标题带图标样式
:deep(.tab-title-with-icon) {
  display: flex;
  align-items: center;

  .tab-icon {
    width: @icon-tab-normal-size;
    height: @icon-tab-normal-size;
    margin-right: @icon-margin-right;
    object-fit: contain;
  }

  .tab-title {
    line-height: normal;
  }
}

// 激活状态的Tab图标尺寸
:deep(.ant-tabs-tab-active) {
  .tab-icon {
    width: @icon-tab-active-size !important;
    height: @icon-tab-active-size !important;
    padding-top: @padding-top-sm;
  }
}

// ========== Ant Design Tab样式重写 (基于原型分析) ==========
:deep(.main-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;

  // 隐藏底部蓝色横线
  .ant-tabs-ink-bar {
    display: none !important;
  }

  // Tab头部区域样式 (基于原型分析)
  .ant-tabs-nav {
    // 应用taskFlow-tab样式
    .taskflow-tab();

    // 确保tab头部不会被压缩
    flex-shrink: 0;

    // 移除Ant Design默认样式
    margin: 0;
    border-bottom: none;
    background: transparent;

    &::before {
      display: none; // 移除默认的底部边框
    }

    .ant-tabs-nav-wrap {
      height: @layout-tab-height;
    }

    .ant-tabs-nav-list {
      height: @layout-tab-height;
      padding: 0;
    }
  }

  // Tab按钮样式
  .ant-tabs-tab {
    // 基于原型分析结果
    height: @layout-tab-height;
    min-height: @layout-tab-height;
    padding: 0 @spacing-md-vw;
    margin: 0;

    // 文字样式 (基于原型分析)
    color: @text-white;
    font-size: @font-size-base;
    font-weight: @font-weight-normal;
    font-family: @font-family;
    line-height: normal;
    text-align: center;

    // 基础状态
    background: @bg-tab-normal;
    border: none;
    border-radius: @border-radius-tab @border-radius-tab 0 0 !important;
    transition: all @transition-base;
    margin-right: @margin-sm-vw;
    color: @text-white-60;

    .ant-tabs-tab-btn {
      color: inherit;
      font-size: @font-size-sm;
      font-weight: inherit;
      font-family: inherit;
      transition: all @transition-base;
    }

    // 鼠标悬停效果 (基于原型分析)
    &:hover {
      color: @text-white;
      opacity: 0.8;

      .ant-tabs-tab-btn {
        color: @text-white;
      }
    }

    // 激活状态 (基于原型分析)
    &.ant-tabs-tab-active {
      color: @text-white;
      background-color: @bg-overlay-medium;
      font-weight: @font-weight-normal;

      .ant-tabs-tab-btn {
        color: @text-white;
        font-weight: @font-weight-normal;
      }
    }
  }

  // Tab内容区域
  .ant-tabs-content-holder {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .ant-tabs-content {
    height: calc(100vh - @layout-tab-height - @layout-header-height - @layout-tab-margin-bottom);
    padding: @layout-content-padding;
    overflow: hidden;
    background-color: @bg-overlay-medium; // 基于原型分析
  }

  .ant-tabs-tabpane {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

// Tab内容区域
.tab-content {
  width: 100%;
  position: relative;

  // 滚动处理
  overflow-y: auto;
  overflow-x: hidden;
}

// 内容标题区域
.content-title {
  position: relative;
  padding: @title-padding;

  .title-bg {
    display: block;
    width: auto;
    max-width: 100%;
    height: auto;
  }

  .title-text {
    position: absolute;
    top: 0;
    left: 0;
    font-size: @font-size-title;
    font-family: YouSheBiaoTiHei, sans-serif;
    color: @text-white;
    text-shadow: @text-shadow-base;
    padding-left: @title-padding-left;
    margin-top: @margin-xs-vw;
    letter-spacing: @title-letter-spacing;
  }
}

// 结束
</style>
