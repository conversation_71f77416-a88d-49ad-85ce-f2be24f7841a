<template>
  <div class="evaluation-detail-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <a-breadcrumb>
        <a-breadcrumb-item>
          <router-link to="/dashboard">
            <i class="fas fa-home"></i>
            首页
          </router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>
          <router-link to="/evaluation-task">评估任务</router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>{{ taskDetail.name }}</a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 可滚动的主要内容区域 -->
    <div class="scrollable-content">
      <!-- 任务详情头部 -->
    <div class="task-detail-header">
      <div class="task-detail-title-section">
        <div class="task-detail-title">
          <h1>{{ taskDetail.name }}</h1>
          <div class="task-detail-subtitle">{{ taskDetail.description }}</div>
        </div>
        <div class="task-detail-actions">
          <a-button
            class="action-btn"
            type="primary"
            @click="handleStart"
            v-if="taskDetail.status === 'pending'"
          >
            <i class="fas fa-play"></i>
            启动任务
          </a-button>
          <a-button class="action-btn" @click="handlePause" v-if="taskDetail.status === 'running'">
            <i class="fas fa-pause"></i>
            暂停任务
          </a-button>
          <a-button
            class="action-btn"
            @click="handleStop"
            v-if="['running', 'paused'].includes(taskDetail.status)"
          >
            <i class="fas fa-stop"></i>
            停止任务
          </a-button>
          <a-button class="action-btn" @click="handleSaveAsTemplate">
            <i class="fas fa-save"></i>
            保存为模版
          </a-button>
          <a-button class="action-btn" @click="handleExport">
            <i class="fas fa-download"></i>
            下载报告
          </a-button>
        </div>
      </div>
      <div class="tags-container">
        <div class="tag" v-for="tag in taskDetail.tags" :key="tag">{{ tag }}</div>
      </div>
      <div class="task-meta-info">
        <div class="meta-item">
          <i class="fas fa-user"></i>
          <span>创建人：{{ taskDetail.creator }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-calendar"></i>
          <span>创建时间：{{ taskDetail.createTime }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-clock"></i>
          <span>预计完成：{{ taskDetail.estimatedTime }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-info-circle"></i>
          <span class="status-badge" :class="taskDetail.statusClass">{{
            taskDetail.statusText
          }}</span>
        </div>
      </div>
    </div>

    <!-- 总体进度指示器 -->
    <div class="progress-container" v-if="taskDetail.status !== 'pending'">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: taskDetail.progress + '%' }"></div>
      </div>
      <div class="progress-text">{{ taskDetail.progress }}% 完成</div>
    </div>

    <!-- 主要内容区域 -->
    <div class="task-detail-content">
      <!-- 标签页导航 -->
      <div class="tab-navigation">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          <i :class="tab.icon"></i>
          {{ tab.label }}
        </div>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content-container">
        <!-- 指标体系内容 -->
        <div v-show="activeTab === 'indicators'" class="tab-content">
          <div class="detail-content-section">
            <div class="mindmap-container-wrapper">
              <div class="mindmap-main-area">
                <div id="mind-map-container" ref="mindMapContainer">
                  <!-- 思维导图画布将在此处渲染 -->
                  <!-- 悬浮工具栏 -->
                  <div class="floating-toolbar">
                    <div class="toolbar-group">
                      <button class="floating-btn" @click="zoomIn" title="放大">
                        <i class="fas fa-search-plus"></i>
                      </button>
                      <button class="floating-btn" @click="zoomOut" title="缩小">
                        <i class="fas fa-search-minus"></i>
                      </button>
                      <button class="floating-btn" @click="fitView" title="适应画布">
                        <i class="fas fa-expand"></i>
                      </button>
                    </div>
                    <div class="toolbar-group">
                      <button class="floating-btn" @click="saveLayout" title="保存布局">
                        <i class="fas fa-save"></i>
                      </button>
                      <button class="floating-btn primary" @click="addNode" title="添加指标">
                        <i class="fas fa-plus"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <!-- 节点提示框 -->
                <div class="node-tooltip" ref="nodeTooltip" style="display: none"></div>
                <!-- 节点编辑表单 -->
                <div class="node-form" ref="nodeForm" style="display: none">
                  <input type="hidden" ref="nodeId" />
                  <div class="form-group">
                    <label>指标名称</label>
                    <input type="text" ref="nodeName" />
                  </div>
                  <div class="form-group">
                    <label>权重 (%)</label>
                    <input type="number" ref="nodeWeight" min="0" max="100" />
                  </div>
                  <div ref="leafNodeFields" style="display: none">
                    <div class="form-group">
                      <label>计算结果</label>
                      <input type="number" ref="nodeResult" min="0" max="100" step="0.1" />
                    </div>
                    <div class="form-group">
                      <label>关联算法</label>
                      <select ref="nodeAlgorithm">
                        <option value="algorithm1">算法1</option>
                        <option value="algorithm2">算法2</option>
                        <option value="algorithm3">算法3</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label>关联数据</label>
                      <select ref="nodeData">
                        <option value="data1">数据集1</option>
                        <option value="data2">数据集2</option>
                        <option value="data3">数据集3</option>
                      </select>
                    </div>
                  </div>
                  <div class="node-form-actions">
                    <button class="mind-map-btn" @click="cancelNodeEdit">
                      <i class="fas fa-times"></i> 取消
                    </button>
                    <button class="mind-map-btn primary" @click="saveNodeEdit">
                      <i class="fas fa-check"></i> 保存
                    </button>
                  </div>
                </div>
              </div>
              <!-- 关联任务卡片区域 - 右侧显示 -->
              <div class="related-tasks-container">
                <div class="section-header">
                  <h3 class="section-title">
                    <i class="fas fa-link"></i> 关联任务
                  </h3>
                  <div class="section-subtitle">
                    当前指标：{{ currentMetricName || '未选择' }}
                  </div>
                </div>

                <!-- 关联的采集任务卡片 -->
                <div class="task-card">
                  <div class="task-card-header">
                    <div class="task-card-title">
                      <i class="fas fa-database"></i> 关联的采集任务
                    </div>
                    <div class="task-card-status">
                      <span class="status-badge status-running">运行中</span>
                    </div>
                  </div>
                  <div class="task-card-content">
                    <div class="task-info-item">
                      <span class="task-info-label">任务名称：</span>
                      <span class="task-info-value">{{ relatedTasks.collection.name }}</span>
                    </div>
                    <div class="task-info-item">
                      <span class="task-info-label">采集类型：</span>
                      <span class="task-info-value">{{ relatedTasks.collection.type }}</span>
                    </div>
                    <div class="task-info-item">
                      <span class="task-info-label">采集进度：</span>
                      <div class="task-progress">
                        <div class="task-progress-bar">
                          <div class="task-progress-fill" :style="{ width: relatedTasks.collection.progress + '%' }"></div>
                        </div>
                        <span class="task-progress-value">{{ relatedTasks.collection.progress }}%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 关联的计算任务卡片 -->
                <div class="task-card">
                  <div class="task-card-header">
                    <div class="task-card-title">
                      <i class="fas fa-calculator"></i> 关联的计算任务
                    </div>
                    <div class="task-card-status">
                      <span class="status-badge status-waiting">等待中</span>
                    </div>
                  </div>
                  <div class="task-card-content">
                    <div class="task-info-item">
                      <span class="task-info-label">任务名称：</span>
                      <span class="task-info-value">{{ relatedTasks.computation.name }}</span>
                    </div>
                    <div class="task-info-item">
                      <span class="task-info-label">计算类型：</span>
                      <span class="task-info-value">{{ relatedTasks.computation.type }}</span>
                    </div>
                    <div class="task-info-item">
                      <span class="task-info-label">计算进度：</span>
                      <div class="task-progress">
                        <div class="task-progress-bar">
                          <div class="task-progress-fill" :style="{ width: relatedTasks.computation.progress + '%' }"></div>
                        </div>
                        <span class="task-progress-value">{{ relatedTasks.computation.progress }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 任务进度内容 -->
        <div v-show="activeTab === 'progress'" class="tab-content">
          <div class="detail-content-section">
            <div class="section-header">
              <h3 class="section-title">
                <i class="fas fa-tasks"></i>
                任务执行进度
              </h3>
            </div>
            <div class="progress-tree">
              <div class="tree-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <p>进度数据加载中...</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 评估报告内容 -->
        <div v-show="activeTab === 'report'" class="tab-content">
          <div class="results-tab-navigation">
            <div
              v-for="resultTab in resultTabs"
              :key="resultTab.key"
              class="results-tab-item"
              :class="{ active: activeResultTab === resultTab.key }"
              @click="activeResultTab = resultTab.key"
            >
              {{ resultTab.label }}
            </div>
          </div>

          <!-- 评估总览 -->
          <div v-show="activeResultTab === 'summary'" class="results-tab-content">
            <div class="result-summary-card">
              <div class="result-header">
                <div>
                  <span class="result-score">{{ taskDetail.score || '79.8' }}</span>
                  <span class="result-unit">分</span>
                </div>
                <div class="result-status">
                  <span class="status-text">评估完成</span>
                  <span class="completion-time">{{
                    taskDetail.completionTime || '2025-04-09 15:30'
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 详细指标 -->
          <div v-show="activeResultTab === 'detail'" class="results-tab-content">
            <div class="detail-content-section">
              <div class="section-header">
                <h3 class="section-title">
                  <i class="fas fa-chart-bar"></i>
                  详细指标分析
                </h3>
              </div>
              <div class="metrics-placeholder">
                <i class="fas fa-chart-line"></i>
                <p>详细指标数据加载中...</p>
              </div>
            </div>
          </div>

          <!-- 评估报告 -->
          <div v-show="activeResultTab === 'report'" class="results-tab-content">
            <div class="detail-content-section">
              <div class="section-header">
                <h3 class="section-title">
                  <i class="fas fa-file-alt"></i>
                  评估报告
                </h3>
              </div>
              <div class="report-placeholder">
                <i class="fas fa-file-pdf"></i>
                <p>评估报告生成中...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

// 定义组件名称
defineOptions({
  name: 'EvaluationDetail',
})

const route = useRoute()
const router = useRouter()

// 当前激活的标签页
const activeTab = ref('indicators')
const activeResultTab = ref('summary')

// 标签页配置
const tabs = [
  { key: 'indicators', label: '指标体系', icon: 'fas fa-chart-bar' },
  { key: 'progress', label: '任务进度', icon: 'fas fa-tasks' },
  { key: 'report', label: '评估报告', icon: 'fas fa-file-alt' },
]

const resultTabs = [
  { key: 'summary', label: '评估总览' },
  { key: 'detail', label: '详细指标' },
  { key: 'report', label: '评估报告' },
]

// 思维导图相关数据
const mindMapContainer = ref<HTMLElement>()
const nodeTooltip = ref<HTMLElement>()
const nodeForm = ref<HTMLElement>()
const nodeId = ref<HTMLInputElement>()
const nodeName = ref<HTMLInputElement>()
const nodeWeight = ref<HTMLInputElement>()
const nodeResult = ref<HTMLInputElement>()
const nodeAlgorithm = ref<HTMLSelectElement>()
const nodeData = ref<HTMLSelectElement>()
const leafNodeFields = ref<HTMLElement>()

// 当前选中的指标名称
const currentMetricName = ref('')

// 关联任务数据
const relatedTasks = ref({
  collection: {
    name: '配电网设备参数采集任务',
    type: '实时数据采集',
    progress: 75
  },
  computation: {
    name: '拓扑结构一致性计算',
    type: '模型比对',
    progress: 40
  }
})

// 思维导图数据
const mindMapData = ref({
  id: 'root',
  label: '城市级联关基行业仿真度测评',
  dataProgress: 68,
  calcProgress: 62,
  children: [
    {
      id: 'power',
      label: '基础运行可信度指标',
      weight: 35,
      result: 82.6,
      children: [
        {
          id: 'resource-info',
          label: '资产重要性/节点重要性可信度',
          weight: 20,
          result: 85.3,
          dataProgress: 100,
          calcProgress: 100,
          algorithm: 'algorithm1',
          data: 'data1'
        },
        {
          id: 'network-topo',
          label: '网络拓扑相似度',
          weight: 15,
          result: 78.9,
          dataProgress: 100,
          calcProgress: 100,
          algorithm: 'algorithm2',
          data: 'data2'
        }
      ]
    },
    {
      id: 'comm',
      label: '脆弱性可信度指标',
      weight: 30,
      result: 79.4,
      children: [
        {
          id: 'comm-capacity',
          label: '通信网络容量仿真度',
          weight: 40,
          result: 76.8,
          dataProgress: 100,
          calcProgress: 100,
          algorithm: 'algorithm3',
          data: 'data3'
        }
      ]
    }
  ]
})

let graph: any = null
let currentNode: any = null

// 任务详情数据
const taskDetail = ref({
  id: '',
  name: '',
  description: '',
  creator: '',
  createTime: '',
  estimatedTime: '',
  status: 'pending',
  statusClass: 'pending',
  statusText: '待执行',
  progress: 0,
  score: null as number | null,
  completionTime: '',
  tags: [] as string[],
})

// 获取任务详情
function fetchTaskDetail() {
  const taskId = route.params.id
  // 模拟数据
  taskDetail.value = {
    id: taskId as string,
    name: '城市X跨行业仿真度评估',
    description: '针对城市X的电力、通信、交通等关键基础设施进行跨行业仿真度综合评估',
    creator: '张工程师',
    createTime: '2025-04-08 14:30',
    estimatedTime: '2025-04-10 18:00',
    status: 'running',
    statusClass: 'running',
    statusText: '进行中',
    progress: 68,
    score: 79.8,
    completionTime: '2025-04-09 15:30',
    tags: ['城市X', '跨行业仿真', '综合评估'],
  }
}

// 操作方法
function handleStart() {
  message.success('任务启动成功')
}

function handlePause() {
  message.success('任务已暂停')
}

function handleStop() {
  message.success('任务已停止')
}

function handleSaveAsTemplate() {
  message.info('保存为模版功能开发中')
}

function handleExport() {
  message.info('导出报告功能开发中')
}

// 思维导图相关方法
function zoomIn() {
  if (graph) {
    const zoom = graph.getZoom()
    graph.zoomTo(zoom * 1.2)
  }
}

function zoomOut() {
  if (graph) {
    const zoom = graph.getZoom()
    graph.zoomTo(zoom / 1.2)
  }
}

function fitView() {
  if (graph) {
    graph.fitView(20)
  }
}

function saveLayout() {
  message.success('布局已保存')
}

function addNode() {
  if (!currentNode) {
    currentNode = mindMapData.value
  }
  addChildNode(currentNode.id)
}

function addChildNode(parentId: string) {
  const parentNode = findNodeById(mindMapData.value, parentId)
  if (!parentNode) return

  // 生成唯一ID
  const newId = 'node-' + Date.now()

  // 创建新节点
  const newNode = {
    id: newId,
    label: '新指标',
    weight: 0,
    dataProgress: 0,
    calcProgress: 0,
    algorithm: 'algorithm1',
    data: 'data1'
  }

  // 添加到父节点
  if (!parentNode.children) {
    parentNode.children = []
  }
  parentNode.children.push(newNode)

  // 更新图形
  if (graph) {
    // 重新初始化思维导图
    initMindMap()
  }

  // 编辑新节点
  editNode(newId)
  message.success('已添加新指标节点')
}

function editNode(nodeId: string) {
  const form = nodeForm.value
  const nodeData = findNodeById(mindMapData.value, nodeId)
  if (!nodeData || !form) return

  currentNode = nodeData

  // 填充表单
  const nodeIdInput = nodeId.value
  const nodeNameInput = nodeName.value
  const nodeWeightInput = nodeWeight.value

  if (nodeIdInput) nodeIdInput.value = nodeData.id
  if (nodeNameInput) nodeNameInput.value = nodeData.label
  if (nodeWeightInput) nodeWeightInput.value = nodeData.weight?.toString() || ''

  const isLeaf = !nodeData.children || nodeData.children.length === 0
  const leafFields = leafNodeFields.value

  if (isLeaf && leafFields) {
    leafFields.style.display = 'block'
    const nodeResultInput = nodeResult.value
    const nodeAlgorithmInput = nodeAlgorithm.value
    const nodeDataInput = nodeData.value

    if (nodeResultInput) nodeResultInput.value = nodeData.result?.toString() || ''
    if (nodeAlgorithmInput) nodeAlgorithmInput.value = nodeData.algorithm || ''
    if (nodeDataInput) nodeDataInput.value = nodeData.data || ''
  } else if (leafFields) {
    leafFields.style.display = 'none'
  }

  // 显示表单
  form.style.display = 'block'
  form.style.left = '50%'
  form.style.top = '50%'
  form.style.transform = 'translate(-50%, -50%)'
}

function saveNodeEdit() {
  const form = nodeForm.value
  if (!form || !currentNode) return

  const nodeIdInput = nodeId.value
  const nodeNameInput = nodeName.value
  const nodeWeightInput = nodeWeight.value

  const nodeIdValue = nodeIdInput?.value
  const nodeNameValue = nodeNameInput?.value
  const nodeWeightValue = parseFloat(nodeWeightInput?.value || '0')

  const node = findNodeById(mindMapData.value, nodeIdValue || '')
  if (!node) return

  // 更新节点数据
  node.label = nodeNameValue || node.label
  if (!isNaN(nodeWeightValue)) {
    node.weight = nodeWeightValue
  }

  const isLeaf = !node.children || node.children.length === 0
  if (isLeaf) {
    const nodeResultInput = nodeResult.value
    const nodeAlgorithmInput = nodeAlgorithm.value
    const nodeDataInput = nodeData.value

    const nodeResultValue = parseFloat(nodeResultInput?.value || '0')
    const nodeAlgorithmValue = nodeAlgorithmInput?.value
    const nodeDataValue = nodeDataInput?.value

    if (!isNaN(nodeResultValue)) {
      node.result = nodeResultValue
    }
    if (nodeAlgorithmValue) {
      node.algorithm = nodeAlgorithmValue
    }
    if (nodeDataValue) {
      node.data = nodeDataValue
    }
  }

  // 更新图形
  if (graph) {
    // 重新初始化思维导图
    initMindMap()
  }

  // 隐藏表单
  form.style.display = 'none'
  message.success('指标节点已更新')
}

function cancelNodeEdit() {
  const form = nodeForm.value
  if (form) {
    form.style.display = 'none'
  }
}

function findNodeById(data: any, id: string): any {
  if (data.id === id) {
    return data
  }

  if (data.children) {
    for (let i = 0; i < data.children.length; i++) {
      const found = findNodeById(data.children[i], id)
      if (found) {
        return found
      }
    }
  }

  return null
}

// 查找父节点
function findParentNode(data: any, childId: string): any {
  if (data.children) {
    for (let i = 0; i < data.children.length; i++) {
      if (data.children[i].id === childId) {
        return data
      }
      const found = findParentNode(data.children[i], childId)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 更新关联任务信息
function updateRelatedTasks(nodeData: any) {
  if (nodeData.algorithm && nodeData.data) {
    relatedTasks.value = {
      collection: {
        name: `${nodeData.label}数据采集任务`,
        type: '实时数据采集',
        progress: nodeData.dataProgress || 75
      },
      computation: {
        name: `${nodeData.label}计算任务`,
        type: nodeData.algorithm || '模型比对',
        progress: nodeData.calcProgress || 40
      }
    }
  }
}

// 初始化思维导图
function initMindMap() {
  nextTick(async () => {
    const container = mindMapContainer.value
    if (!container) return

    try {
      // 动态导入G6
      const { Graph, treeToGraphData, register, ExtensionCategory, Rect } = await import('@antv/g6')

      // 定义自定义节点类
      class MetricNode extends Rect {
        get nodeData() {
          return this.context.graph.getNodeData(this.id)
        }

        get data() {
          return this.nodeData.data || {}
        }

        // 获取节点标题样式
        getTitleStyle(attributes: any) {
          const [width, height] = this.getSize(attributes)
          return {
            x: 0,
            y: -height / 2 + 15,
            text: String(attributes.title || ''),
            fontSize: 14,
            fill: '#E0E6F0',
            fontWeight: 'bold',
            textAlign: 'center',
            textBaseline: 'middle',
            maxWidth: width - 20
          }
        }

        // 获取权重样式
        getWeightStyle(attributes: any) {
          const [width, height] = this.getSize(attributes)
          return {
            x: -width / 2 + 10,
            y: -height / 2 + 5,
            text: attributes.weight ? `${attributes.weight}%` : '',
            fontSize: 10,
            fill: '#0096FF',
            textAlign: 'left',
            textBaseline: 'middle'
          }
        }

        // 获取结果样式
        getResultStyle(attributes: any) {
          const [width, height] = this.getSize(attributes)
          return {
            x: width / 2 - 10,
            y: -height / 2 + 5,
            text: attributes.result ? String(attributes.result) : '',
            fontSize: 10,
            fill: '#52C41A',
            textAlign: 'right',
            textBaseline: 'middle'
          }
        }

        // 获取算法标签样式
        getAlgorithmStyle(attributes: any) {
          const [width, height] = this.getSize(attributes)
          return {
            x: 0,
            y: height / 2 - 15,
            text: String(attributes.algorithm || ''),
            fontSize: 9,
            fill: '#A0A8B8',
            textAlign: 'center',
            textBaseline: 'middle'
          }
        }

        // 获取数据标签样式
        getDataStyle(attributes: any) {
          const [width, height] = this.getSize(attributes)
          return {
            x: 0,
            y: height / 2 - 5,
            text: String(attributes.dataSource || ''),
            fontSize: 9,
            fill: '#A0A8B8',
            textAlign: 'center',
            textBaseline: 'middle'
          }
        }

        render(attributes: any, container: any) {
          // 渲染基础矩形
          super.render(attributes, container)

          // 添加标题
          if (attributes.title) {
            const titleStyle = this.getTitleStyle(attributes)
            this.upsert('title', 'text', titleStyle, container)
          }

          // 添加权重
          if (attributes.weight) {
            const weightStyle = this.getWeightStyle(attributes)
            this.upsert('weight', 'text', weightStyle, container)
          }

          // 添加结果（仅叶子节点）
          if (attributes.result && !this.data.children?.length) {
            const resultStyle = this.getResultStyle(attributes)
            this.upsert('result', 'text', resultStyle, container)
          }

          // 添加算法标签（仅叶子节点）
          if (attributes.algorithm && !this.data.children?.length) {
            const algorithmStyle = this.getAlgorithmStyle(attributes)
            this.upsert('algorithm', 'text', algorithmStyle, container)
          }

          // 添加数据标签（仅叶子节点）
          if (attributes.dataSource && !this.data.children?.length) {
            const dataStyle = this.getDataStyle(attributes)
            this.upsert('dataSource', 'text', dataStyle, container)
          }
        }
      }

      // 注册自定义节点
      register(ExtensionCategory.NODE, 'metric-node', MetricNode)

      // 转换数据格式
      const graphData = treeToGraphData(mindMapData.value)

      // 创建图实例
      graph = new Graph({
        container: container,
        width: container.clientWidth,
        height: container.clientHeight,
        autoFit: 'view',
        data: graphData,
        layout: {
          type: 'mindmap',
          direction: 'H',
          getHeight: () => 60,
          getWidth: (d) => {
            // 根据节点类型调整宽度
            if (d.id === 'root') return 200
            if (d.children && d.children.length > 0) return 160
            return 140
          },
          getVGap: () => 20,
          getHGap: () => 80,
          getSide: (d) => {
            // 根据数据索引分配左右
            if (d.id === 'root') return 'center'
            const parent = findParentNode(mindMapData.value, d.id)
            if (parent && parent.children) {
              const index = parent.children.findIndex(child => child.id === d.id)
              return index % 2 === 0 ? 'right' : 'left'
            }
            return 'right'
          }
        },
        node: {
          type: 'metric-node',
          style: {
            fill: (d) => {
              if (d.id === 'root') return 'rgba(0, 150, 255, 0.2)'
              if (d.children && d.children.length > 0) return 'rgba(25, 164, 255, 0.15)'
              return 'rgba(82, 196, 26, 0.15)'
            },
            stroke: (d) => {
              if (d.id === 'root') return '#0096FF'
              if (d.children && d.children.length > 0) return '#19A4FF'
              return '#52C41A'
            },
            lineWidth: 2,
            radius: 8,
            title: (d) => d.label,
            weight: (d) => d.weight,
            result: (d) => d.result,
            algorithm: (d) => d.algorithm,
            dataSource: (d) => d.data
          }
        },
        edge: {
          type: 'cubic-horizontal',
          style: {
            stroke: 'rgba(25, 164, 255, 0.6)',
            lineWidth: 2,
            endArrow: {
              path: 'M 0,0 L 8,4 L 8,-4 Z',
              fill: 'rgba(25, 164, 255, 0.6)'
            }
          }
        },
        behaviors: [
          'drag-canvas',
          'zoom-canvas',
          {
            type: 'drag-element',
            enable: (event) => event.targetType === 'node'
          }
        ]
      })

      // 添加节点点击事件
      graph.on('node:click', (event) => {
        const nodeId = event.target.id
        const nodeData = graph.getNodeData(nodeId)
        currentMetricName.value = nodeData.label || '未知指标'

        // 更新关联任务信息
        updateRelatedTasks(nodeData)

        // 编辑节点
        editNode(nodeId)
      })

      // 添加节点双击事件（添加子节点）
      graph.on('node:dblclick', (event) => {
        const nodeId = event.target.id
        addChildNode(nodeId)
      })

      // 渲染图形
      await graph.render()

      console.log('思维导图初始化完成')
    } catch (error) {
      console.error('思维导图初始化失败:', error)
      // 显示错误信息
      container.innerHTML = `
        <div style="width: 100%; height: 600px; background: #1A202E; border-radius: 8px; position: relative; overflow: hidden;">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: #E0E6F0;">
            <div style="font-size: 24px; margin-bottom: 20px; color: #FF4D4F;">
              <i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>
              思维导图加载失败
            </div>
            <div style="font-size: 16px; color: #A0A8B8;">
              ${error.message}
            </div>
          </div>
        </div>
      `
    }
  })
}

onMounted(() => {
  fetchTaskDetail()
  initMindMap()
})
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.evaluation-detail-container {
  width: 100%;
  height: 100vh;
  padding: 3.4875vw @padding-left-l @padding-top-l @padding-left-l;
  background: url(/src/assets/images/background.png) no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  color: #fff;
  font-size: 1.041667vw;
  display: flex;
  flex-direction: column;
}

// 可滚动内容区域
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  min-height: 0; // 重要：允许flex子项收缩

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 23, 51, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(25, 164, 255, 0.6);
    border-radius: 3px;

    &:hover {
      background: rgba(25, 164, 255, 0.8);
    }
  }
}

// 面包屑导航
.breadcrumb {
  flex-shrink: 0; // 防止收缩
  margin-bottom: @spacing-lg-vw;

  :deep(.ant-breadcrumb) {
    font-size: @font-size-md-vw;

    .ant-breadcrumb-link {
      color: @text-secondary;

      &:hover {
        color: @primary-blue;
      }
    }

    .ant-breadcrumb-separator {
      color: @text-secondary;
    }
  }

  a {
    color: @text-secondary;
    text-decoration: none;

    &:hover {
      color: @primary-blue;
    }
  }

  i {
    margin-right: @spacing-xs-vw;
  }
}

// 任务详情头部
.task-detail-header {
  background-color: @bg-overlay-medium;
  border-radius: @card-border-radius-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  padding: @card-padding-vw;
  margin-bottom: @spacing-lg-vw;
}

.task-detail-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @spacing-md-vw;
}

.task-detail-title {
  h1 {
    font-size: @font-size-xl-vw;
    color: @text-white;
    margin: 0 0 @spacing-xs-vw 0;
    font-weight: 600;
  }
}

.task-detail-subtitle {
  font-size: @font-size-md-vw;
  color: @text-secondary;
  line-height: 1.5;
}

.task-detail-actions {
  display: flex;
  gap: @spacing-sm-vw;

  .action-btn {
    height: 2.5vw;
    padding: 0 @spacing-md-vw;
    border-radius: @border-radius-base;
    font-size: @font-size-sm-vw;

    i {
      margin-right: @spacing-xs-vw;
    }
  }
}

.tags-container {
  display: flex;
  gap: @spacing-sm-vw;
  margin-bottom: @spacing-md-vw;

  .tag {
    background: rgba(25, 164, 255, 0.2);
    color: @primary-blue;
    padding: @spacing-xs-vw @spacing-sm-vw;
    border-radius: @border-radius-sm;
    font-size: @font-size-sm-vw;
    border: 1px solid rgba(25, 164, 255, 0.3);
  }
}

.task-meta-info {
  display: flex;
  gap: @spacing-lg-vw;

  .meta-item {
    display: flex;
    align-items: center;
    font-size: @font-size-sm-vw;
    color: @text-secondary;

    i {
      margin-right: @spacing-xs-vw;
      color: @primary-blue;
    }
  }

  .status-badge {
    padding: @spacing-xs-vw @spacing-sm-vw;
    border-radius: @border-radius-sm;
    font-size: @font-size-xs-vw;

    &.pending {
      background: rgba(255, 185, 70, 0.2);
      color: @warning-orange;
    }

    &.running {
      background: rgba(25, 164, 255, 0.2);
      color: @primary-blue;
    }

    &.completed {
      background: rgba(0, 196, 140, 0.2);
      color: @success-green;
    }

    &.failed {
      background: rgba(242, 87, 103, 0.2);
      color: @danger-red;
    }
  }
}

// 进度条
.progress-container {
  background-color: @bg-overlay-medium;
  border-radius: @card-border-radius-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  padding: @card-padding-vw;
  margin-bottom: @spacing-lg-vw;
  display: flex;
  align-items: center;
  gap: @spacing-md-vw;
}

.progress-bar {
  flex: 1;
  height: 0.5vw;
  background: rgba(255, 255, 255, 0.1);
  border-radius: @border-radius-sm;
  overflow: hidden;

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, @primary-blue, @primary-hover);
    border-radius: @border-radius-sm;
    transition: width 0.3s ease;
  }
}

.progress-text {
  font-size: @font-size-sm-vw;
  color: @text-white;
  white-space: nowrap;
}

// 主要内容区域
.task-detail-content {
  background-color: @bg-overlay-medium;
  border-radius: @card-border-radius-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  overflow: hidden;
}

// 标签页导航
.tab-navigation {
  display: flex;
  background: rgba(25, 164, 255, 0.3);
  border-bottom: 1px solid rgba(25, 164, 255, 0.5);

  .tab-item {
    padding: @spacing-md-vw @spacing-lg-vw;
    cursor: pointer;
    font-size: @font-size-md-vw;
    color: #e0e6f0;
    border-right: 1px solid rgba(25, 164, 255, 0.5);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: @spacing-xs-vw;

    &:hover {
      background: rgba(25, 164, 255, 0.1);
      color: #07f6ff;
    }

    &.active {
      background: #0096ff;
      color: white;

      &:hover {
        background: #0096ff;
      }
    }

    &:last-child {
      border-right: none;
    }
  }
}

// 标签页内容容器
.tab-content-container {
  padding: @card-padding-vw;
}

.tab-content {
  min-height: 80vh; // 增加最小高度以确保能够滚动
}

// 内容区域通用样式
.detail-content-section {
  margin-bottom: @card-margin-bottom-vw;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: @spacing-md-vw;

  .section-title {
    font-size: @font-size-lg-vw;
    font-family: YouSheBiaoTiHei, sans-serif;
    color: #07f6ff;
    margin: 0;
    display: flex;
    align-items: center;
    gap: @spacing-sm-vw;

    i {
      color: #0096ff;
    }
  }
}

// 占位符样式
.mindmap-placeholder,
.tree-placeholder,
.metrics-placeholder,
.report-placeholder,
.log-placeholder,
.config-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 40vh;
  color: #a0a8b8;

  i {
    font-size: 4vw;
    margin-bottom: @spacing-md-vw;
    color: #0096ff;
    opacity: 0.6;
  }

  p {
    font-size: @font-size-md-vw;
    margin: 0;
    color: #e0e6f0;
  }
}

// 结果标签页导航
.results-tab-navigation {
  display: flex;
  background: rgba(0, 0, 0, 0.1);
  border-radius: @border-radius-base;
  margin-bottom: @spacing-lg-vw;
  padding: @spacing-xs-vw;

  .results-tab-item {
    flex: 1;
    padding: @spacing-sm-vw @spacing-md-vw;
    text-align: center;
    cursor: pointer;
    font-size: @font-size-sm-vw;
    color: @text-secondary;
    border-radius: @border-radius-sm;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(25, 164, 255, 0.1);
      color: @primary-blue;
    }

    &.active {
      background: @primary-blue;
      color: @text-white;
    }
  }
}

// 结果摘要卡片
.result-summary-card {
  background-color: rgba(25, 164, 255, 0.5);
  border-radius: @card-border-radius-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  padding: @card-padding-vw;
  margin-bottom: @card-margin-bottom-vw;

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .result-score {
      font-size: 4vw;
      font-weight: bold;
      color: #00c48c;
    }

    .result-unit {
      font-size: @font-size-lg-vw;
      color: #a0a8b8;
      margin-left: @spacing-xs-vw;
    }

    .result-status {
      text-align: right;

      .status-text {
        display: block;
        font-size: @font-size-md-vw;
        color: #e0e6f0;
        margin-bottom: @spacing-xs-vw;
      }

      .completion-time {
        font-size: @font-size-sm-vw;
        color: #07f6ff;
      }
    }
  }
}

// 思维导图相关样式
.mindmap-container-wrapper {
  display: flex;
  gap: 20px;
  height: calc(100vh - 180px);
  min-height: 0;
}

.mindmap-main-area {
  flex: 1;
  min-width: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

#mind-map-container {
  width: 100%;
  height: 100%;
  background-color: @bg-overlay-medium;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(25, 164, 255, 0.5);
}

// 悬浮工具栏样式
.floating-toolbar {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .toolbar-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    background-color: rgba(16, 22, 36, 0.9);
    border-radius: 8px;
    padding: 8px;
    border: 1px solid rgba(25, 164, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

.floating-btn {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  border: 1px solid rgba(25, 164, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.05);
  color: @text-white;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(25, 164, 255, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 164, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  &.primary {
    background-color: rgba(0, 150, 255, 0.8);
    border-color: @primary-blue;
    color: white;

    &:hover {
      background-color: @primary-blue;
      box-shadow: 0 2px 8px rgba(0, 150, 255, 0.5);
    }
  }

  i {
    font-size: 12px;
  }
}

.mind-map-btn {
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid rgba(25, 164, 255, 0.5);
  background-color: @bg-overlay-medium;
  color: @text-white;
  cursor: pointer;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &.primary {
    background-color: @primary-blue;
    border-color: @primary-blue;
    color: white;

    &:hover {
      background-color: @primary-hover;
    }
  }

  &.danger {
    background-color: @danger-red;
    border-color: @danger-red;
    color: white;

    &:hover {
      background-color: rgba(242, 87, 103, 0.8);
    }
  }
}

.node-tooltip {
  position: absolute;
  padding: 10px;
  background-color: rgba(16, 22, 36, 0.95);
  border: 1px solid rgba(25, 164, 255, 0.5);
  border-radius: 6px;
  z-index: 999;
  font-size: 13px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s;
  max-width: 300px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  color: @text-white;
}

.node-form {
  padding: 15px;
  background-color: @bg-overlay-medium;
  border: 1px solid rgba(25, 164, 255, 0.5);
  border-radius: 8px;
  position: absolute;
  z-index: 100;
  width: 300px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);

  label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: @text-white;
  }

  input,
  select {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 164, 255, 0.5);
    border-radius: 4px;
    color: @text-white;

    &:focus {
      outline: none;
      border-color: @primary-blue;
    }
  }

  .form-group {
    margin-bottom: 15px;
  }

  .node-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 10px;
  }
}

.related-tasks-container {
  width: 350px;
  margin-top: 0;
  padding-top: 0;
  border-top: none;
  border-left: 1px solid rgba(25, 164, 255, 0.5);
  padding-left: 20px;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
      color: #07f6ff;
    }

    .section-subtitle {
      color: @text-secondary;
      font-size: 14px;
    }
  }
}

.task-card {
  background-color: @bg-overlay-medium;
  border-radius: 8px;
  border: 1px solid rgba(25, 164, 255, 0.5);
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  }

  .task-card-header {
    padding: 12px 16px;
    background-color: rgba(16, 22, 36, 0.5);
    border-bottom: 1px solid rgba(25, 164, 255, 0.5);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .task-card-title {
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
      color: @text-white;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.status-running {
        background-color: rgba(0, 196, 140, 0.15);
        color: @success-green;
      }

      &.status-waiting {
        background-color: rgba(255, 185, 70, 0.15);
        color: @warning-orange;
      }

      &.status-completed {
        background-color: rgba(62, 155, 255, 0.15);
        color: @primary-blue;
      }

      &.status-error {
        background-color: rgba(242, 87, 103, 0.15);
        color: @danger-red;
      }
    }
  }

  .task-card-content {
    padding: 16px;

    .task-info-item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      .task-info-label {
        width: 120px;
        color: @text-secondary;
        font-size: 14px;
      }

      .task-info-value {
        flex: 1;
        font-size: 14px;
        color: @text-white;
      }
    }

    .task-progress {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 10px;

      .task-progress-bar {
        flex: 1;
        height: 6px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        overflow: hidden;

        .task-progress-fill {
          height: 100%;
          background-color: @primary-blue;
          border-radius: 3px;
        }
      }

      .task-progress-value {
        font-size: 14px;
        min-width: 40px;
        text-align: right;
        color: @text-white;
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 1200px) {
  .task-detail-title-section {
    flex-direction: column;
    gap: @spacing-md-vw;
  }

  .task-meta-info {
    flex-wrap: wrap;
    gap: @spacing-md-vw;
  }

  .tab-navigation {
    flex-wrap: wrap;

    .tab-item {
      flex: 1;
      min-width: 120px;
    }
  }
}
</style>
