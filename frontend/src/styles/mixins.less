// ===========================================
// CA-Frontend 样式混入库
// ===========================================

// ===== 文本省略 =====
.text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-multi(@lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// ===== 布局居中 =====
.absolute-center() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between() {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// ===== 科技风容器 =====
.tech-container() {
  background-color: @bg-overlay-medium;
  border: 1px solid @border-color-primary;
  border-radius: @border-radius-base;
  backdrop-filter: blur(10px);
  transition: all @transition-base;

  &:hover {
    border-color: @border-color-active;
    box-shadow: @glow-primary;
  }
}

// taskFlow-main样式混入
.taskflow-main() {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  flex-direction: column;
  border: none;
  border-radius: 0;
  box-shadow: none;
  overflow: visible;
  padding: @margin-xs-vw @layout-tab-margin-bottom @layout-tab-margin-bottom
    @layout-tab-margin-bottom;
  margin: 0;
  color: @text-white;
  font-size: @font-size-base;
  font-family: @font-family;
}

// taskFlow-tab样式混入
.taskflow-tab() {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: @layout-tab-height;
  background: transparent;
  border-bottom: none;
}

// ===== 按钮混入 =====
.btn-tech() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: @spacing-sm-vw @spacing-md-vw;
  font-size: @font-size-base;
  font-weight: @font-weight-medium;
  color: @text-white;
  background-color: transparent;
  border: 1px solid @border-color-primary;
  border-radius: @border-radius-base;
  cursor: pointer;
  transition: all @transition-base;
  user-select: none;

  &:hover {
    color: @primary-blue;
    border-color: @border-color-active;
    box-shadow: @glow-primary;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    color: @text-disabled;
    border-color: rgba(64, 158, 255, 0.2);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
  }
}

.btn-primary() {
  background-color: @primary-blue;
  color: @text-white;
  border-color: @primary-blue;

  &:hover {
    background-color: lighten(@primary-blue, 10%);
    color: @text-white;
    border-color: lighten(@primary-blue, 10%);
    box-shadow: @glow-primary;
  }
}

// ===== 科技风按钮混入 =====
.tech-action-btn() {
  padding: @spacing-sm-vw 1.2vw;
  height: 2.2vw;
  border: none;
  color: #e0e6f0;
  display: flex;
  align-items: center;
  font-size: @font-size-md-vw;
  background: url('@/assets/images/btn-bg-3.png') no-repeat;
  background-size: 100% 100%;

  // 确保所有按钮内文字大小一致
  span {
    font-size: @font-size-md-vw;
  }

  // 确保所有按钮内图标大小一致
  i,
  .anticon {
    font-size: @font-size-md-vw;
  }

  &:hover {
    opacity: 0.9;
    color: #ffffff;
  }

  &.primary {
    background: url('@/assets/images/btn-bg-2.png') no-repeat;
    background-size: 100% 100%;
  }
}

.tech-bulk-action-btn() {
  padding: @spacing-sm-vw 1.2vw;
  height: 2.2vw;
  background-color: transparent;
  border: none;
  color: #e0e6f0;
  border-radius: @border-radius-md-vw;
  display: flex;
  align-items: center;
  margin-right: @spacing-md-vw;
  text-shadow: 1px 0.104167vw 0.208333vw rgba(0, 0, 0, 0.5);

  &:first-child {
    background: url('@/assets/images/btn-bg-2.png') no-repeat;
    background-size: 100% 100%;
  }

  &:not(:first-child) {
    background: url('@/assets/images/btn-bg-3.png') no-repeat;
    background-size: 100% 100%;
  }

  .anticon {
    margin-right: 0.5vw;
    font-size: @font-size-md-vw;
  }

  // 确保所有按钮内文字大小一致
  span {
    font-size: @font-size-md-vw;
  }

  // 确保所有按钮内图标大小一致
  i,
  .anticon {
    font-size: @font-size-md-vw;
  }

  &:hover {
    opacity: 0.9;
    color: #ffffff !important;
  }

  &.delete:hover {
    color: #46fff4 !important;
  }
}

.tech-icon-action-btn() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2vw;
  height: 2vw;
  background: url('@/assets/images/btn-bg-3.png') no-repeat;
  background-size: 100% 100%;
  color: #e0e6f0;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: @spacing-sm-vw;

  // 确保所有图标按钮内的图标大小一致
  i,
  .anticon {
    font-size: @font-size-md-vw;
  }

  &:hover {
    opacity: 0.9;
    color: #ffffff;
  }

  &.delete:hover {
    color: #46fff4;
  }
}

// ===== 返回按钮样式 =====
.back-button() {
  display: flex;
  position: absolute;
  background: transparent;
  border: none;
  color: @text-white;
  cursor: pointer;
  transition: all @transition-base;

  &:hover {
    color: @primary-blue;
    text-shadow: @glow-primary;
  }
}

// ===== 用户头像样式 =====
.user-avatar() {
  width: @icon-avatar-size;
  height: @icon-avatar-size;
  border-radius: 50%;
  border: 2px solid @text-secondary;
  cursor: pointer;
  transition: all @transition-base;

  &:hover {
    border-color: @text-white;
    box-shadow: 0 0 10px @text-secondary;
  }
}

// ===== 表格混入 =====
.table-tech() {
  width: 100%;
  background-color: transparent;
  border-collapse: collapse;
  color: @text-white;
  font-size: @font-size-base;

  th {
    background-color: @bg-overlay-light;
    color: @primary-blue;
    font-weight: @font-weight-medium;
    padding: @spacing-sm-vw @spacing-md-vw;
    border-bottom: 1px solid @border-color-primary;
    text-align: left;
  }

  td {
    padding: @spacing-sm-vw @spacing-md-vw;
    border-bottom: 1px solid rgba(64, 158, 255, 0.1);
    transition: background-color @transition-fast;
  }

  tr {
    &:hover {
      background-color: rgba(64, 158, 255, 0.05);
    }
  }
}

// ===== 透明面板混入 =====
.transparent-panel(@opacity: 0.5) {
  background-color: rgba(0, 23, 51, @opacity);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: @border-radius-base;
}

// 深色透明面板
.dark-panel() {
  .transparent-panel(0.8);
}

// 中等透明面板
.medium-panel() {
  .transparent-panel(0.5);
}

// 浅色透明面板
.light-panel() {
  .transparent-panel(0.2);
}

// ===== 辅助样式 =====
.truncate() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-glow() {
  text-shadow: @glow-primary;
}
