// ===========================================
// CA-Frontend 全局科技风样式
// ===========================================

@import './variables.less';
@import './mixins.less';

// ========== CSS变量 ==========
:root {
  // 基础调色板 (从base.css)
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;
  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;
  --vt-c-indigo: #2c3e50;
  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);
  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);

  // 语义变量 (从base.css)
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);
  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);
  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);
  --section-gap: 160px;
}

// ========== 字体声明 ==========
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('../assets/YouSheBiaoTiHei.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

// ========== 重置和基础样式 ==========
* {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

html {
  font-size: 16px;
}

body {
  margin: 0;
  padding: 0;
  font-family: @font-family;
  font-size: @font-size-base;
  line-height: 1.5;
  color: @text-white;
  background: @primary-dark-blue;
  overflow-x: hidden;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  padding: 0;
  margin: 0 auto;
  font-weight: normal;
}

// ========== 全屏背景容器 ==========
.main-layout {
  width: 100vw;
  height: 100vh;
  background: url('@/assets/images/background.png') no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  color: #fff;
  font-size: 1.041667vw;
  padding: 4.6875vw 1.5625vw 1.5625vw;
}

// ========== 滚动条样式 ==========
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 23, 51, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgb(126, 213, 255);
  border-radius: 3px;

  &:hover {
    background: rgb(126, 213, 255);
  }
}

// ========== 全局文字选择样式 ==========
::selection {
  background-color: rgba(64, 158, 255, 0.3);
  color: @text-white;
}

::-moz-selection {
  background-color: rgba(64, 158, 255, 0.3);
  color: @text-white;
}

// ========== 链接样式 ==========
a {
  color: @primary-blue;
  text-decoration: none;
  transition: color @transition-fast;
  padding: 3px;

  &:hover {
    color: lighten(@primary-blue, 15%);
    text-shadow: 0 0 8px rgba(64, 158, 255, 0.6);
  }

  &:focus {
    outline: 2px solid rgba(64, 158, 255, 0.5);
    outline-offset: 2px;
  }
}

// 兼容main.css中的绿色链接类
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

// ========== 表单元素基础样式 ==========
input,
textarea,
select {
  background-color: rgba(0, 23, 51, 0.3);
  border: 1px solid @border-color-primary;
  border-radius: @border-radius-base;
  color: @text-white;
  font-size: @font-size-base;
  padding: @spacing-sm-vw;
  transition: all @transition-base;

  &::placeholder {
    color: @text-secondary;
  }

  &:focus {
    outline: none;
    border-color: @border-color-active;
    box-shadow: @glow-primary;
  }

  &:disabled {
    background-color: rgba(0, 23, 51, 0.1);
    border-color: rgba(64, 158, 255, 0.2);
    color: @text-disabled;
    cursor: not-allowed;
  }
}

// ========== 按钮基础样式 ==========
button {
  .btn-tech();

  // 特殊按钮变体
  &.btn-primary {
    .btn-primary();
  }

  &.btn-success {
    border-color: @success-green;
    color: @success-green;

    &:hover {
      background-color: @success-green;
      color: white;
      box-shadow: @glow-success;
    }
  }

  &.btn-danger {
    border-color: @danger-red;
    color: @danger-red;

    &:hover {
      background-color: @danger-red;
      color: white;
      box-shadow: 0 0 8px rgba(245, 108, 108, 0.6);
    }
  }
}

// ========== 全局科技风按钮样式 ==========
.action-btn {
  .tech-action-btn();

  :deep(span) {
    font-size: @font-size-md-vw !important;
  }

  :deep(i),
  :deep(.anticon) {
    font-size: @font-size-md-vw !important;
  }
}

.bulk-action-btn {
  .tech-bulk-action-btn();

  :deep(span) {
    font-size: @font-size-md-vw !important;
  }

  :deep(i),
  :deep(.anticon) {
    font-size: @font-size-md-vw !important;
  }
}

.icon-action-btn {
  .tech-icon-action-btn();

  :deep(i),
  :deep(.anticon) {
    font-size: @font-size-md-vw !important;
  }
}

// ========== 表格基础样式 ==========
table {
  .table-tech();
}

.custom-modal-wrap {
  .ant-modal {
    .ant-modal-content {
      background-color: rgb(0 124 205 / 50%) !important;
      border: 1px solid rgba(25, 164, 255, 0.5) !important;
      box-shadow: 0 0 20px rgba(7, 246, 255, 0.2) !important;
    }
  }
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1vw;
    margin-top: 1vw;
    padding-top: 1vw;
    border-top: 1px solid white;
  }
  .modal-btn {
    background: url('@/assets/images/btn-bg-3.png') no-repeat;
    background-size: 100% 100%;
    border: none;
    color: #e0e6f0;
    height: 2.2vw;
    padding: 0 1vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: @font-size-md-vw;
    min-width: 6vw;

    &:hover {
      opacity: 0.9;
      color: #ffffff;
    }

    &.primary {
      background: url('@/assets/images/btn-bg-2.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}

// ========== 全局分页样式 ==========
.ant-pagination {
  text-align: center;
  padding: @spacing-md-vw 0;
  font-size: @font-size-md-vw;
  color: #fff;

  .ant-pagination-total-text {
    color: #e0e6f0;
    font-size: @font-size-sm-vw;
  }

  .ant-pagination-item {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 164, 255, 0.5);
    font-size: @font-size-sm-vw;

    a {
      color: #e0e6f0;
    }

    &:hover {
      border-color: #0096ff;
      a {
        color: #07f6ff;
      }
    }
  }

  .ant-pagination-item-active {
    background-color: #0096ff;
    border-color: #0096ff;

    a {
      color: white;
    }

    &:hover {
      background-color: #3e9bff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    button {
      background-color: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(25, 164, 255, 0.5);
      color: #e0e6f0;
      font-size: @font-size-sm-vw;

      &:hover {
        border-color: #0096ff;
        color: #07f6ff;
      }
    }

    &.ant-pagination-disabled button {
      background-color: rgba(255, 255, 255, 0.02);
      border-color: rgba(25, 164, 255, 0.2);
      color: rgba(224, 230, 240, 0.3);
    }
  }

  .pagination {
    display: flex;
    gap: 5px;
  }

  .ant-pagination-options {
    margin-right: @spacing-base-vw;
    .ant-select {
      .ant-select-selector {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(25, 164, 255, 0.5);
        color: #e0e6f0;
      }

      .ant-select-arrow {
        color: #07f6ff;
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #e0e6f0;
      font-size: @font-size-sm-vw;

      input {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(25, 164, 255, 0.5);
        color: #e0e6f0;
        font-size: @font-size-sm-vw;

        &:hover,
        &:focus {
          border-color: #0096ff;
          box-shadow: 0 0 5px rgba(7, 246, 255, 0.2);
        }
      }
    }
  }
}

// 优化分页组件的样式
.ant-pagination {
  font-size: @font-size-md-vw;
  color: #e0e6f0;

  .ant-pagination-item {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 164, 255, 0.5);
    min-width: 2vw;
    height: 2vw;
    line-height: 2vw;

    a {
      color: #e0e6f0;
      font-size: @font-size-md-vw;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .ant-pagination-item-active {
    background-color: #0096ff;
    border-color: #0096ff;

    a {
      color: white;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    min-width: 2vw;
    height: 2vw;
    line-height: 2vw;

    .ant-pagination-item-link {
      color: #e0e6f0;
      font-size: @font-size-md-vw;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(25, 164, 255, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .ant-pagination-disabled .ant-pagination-item-link {
    color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.02);
    border-color: rgba(25, 164, 255, 0.1);
  }

  .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
  .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
    color: rgba(255, 255, 255, 0.7);
  }

  .ant-pagination-options {
    .ant-select {
      color: #e0e6f0;

      .ant-select-arrow {
        color: #e0e6f0;
      }
    }

    .ant-select-selector {
      height: 2vw !important;
      line-height: 2vw;
      padding: 0 @spacing-sm-vw !important;
      font-size: @font-size-md-vw;
      background-color: rgba(255, 255, 255, 0.05) !important;
      border: 1px solid rgba(25, 164, 255, 0.5) !important;
      // border-radius: @border-radius-md-vw !important;

      .ant-select-selection-item {
        display: flex;
        align-items: center;
        height: 100%;
        color: #e0e6f0;
        font-size: @font-size-md-vw;
        line-height: 2vw;
      }
    }

    .ant-pagination-options-quick-jumper {
      color: #e0e6f0;
      height: 2vw;
      line-height: 2vw;
      font-size: @font-size-md-vw;
      margin-left: @spacing-base-vw;

      input {
        height: 2vw;
        padding: 0 @spacing-sm-vw;
        font-size: @font-size-md-vw;
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(25, 164, 255, 0.5);
        color: #e0e6f0;
        margin: 0 @spacing-xs-vw;
        width: 3vw;
        text-align: center;
        // border-radius: @border-radius-md-vw;
      }

      input:focus {
        border-color: rgba(25, 164, 255, 0.8);
        outline: none;
        box-shadow: 0 0 0.3vw rgba(25, 164, 255, 0.5);
      }
    }
  }

  .ant-pagination-total-text {
    color: #e0e6f0;
    font-size: @font-size-md-vw;
    line-height: 2vw;
    height: 2vw;
    display: flex;
    align-items: center;
    margin-right: @spacing-base-vw;
  }
}

.ant-pagination a {
  padding: 0 !important;
}

// 修改表格分页区域的间距
.ant-table-pagination.ant-pagination {
  margin: 1vw 0 0;
  padding: @spacing-sm-vw 0;
}
.ant-pagination-jump-next-custom-icon {
  height: 2vw;
  line-height: 0.8vw;
}
.ant-pagination-jump-prev-custom-icon {
  height: 2vw;
  line-height: 0.8vw;
}
.ant-pagination-options-size-changer {
  padding-right: @spacing-sm-vw;
  vertical-align: top;
}

// 隐藏所有高级筛选功能
.advanced-filter-toggle {
  display: none !important;
}

.ant-modal-header {
  background: transparent !important;
  .ant-modal-title {
    color: white !important;
    font-size: @font-size-xl-vw !important;
  }
}

.ant-modal-mask {
  background: #041222f2 !important;
}

// 针对创建模板表单组件中的控件字体大小
.create-template-container {
  // 输入框
  .ant-input,
  .ant-input-affix-wrapper,
  .ant-input-textarea {
    font-size: @font-size-md-vw !important;

    input,
    textarea {
      font-size: @font-size-md-vw !important;
    }
  }

  // 下拉选择框
  .ant-select {
    font-size: @font-size-md-vw !important;

    .ant-select-selector,
    .ant-select-selection-item,
    .ant-select-selection-placeholder {
      font-size: @font-size-md-vw !important;
      height: 2.5vw !important; /* 保持与输入框相同高度 */
      line-height: 2.5vw !important; /* 垂直居中文本 */
      display: flex !important;
      align-items: center !important;
    }

    .ant-select-dropdown {
      font-size: @font-size-md-vw !important;

      .ant-select-item {
        font-size: @font-size-md-vw !important;
      }
    }
  }

  // 标签
  .ant-tag {
    font-size: @font-size-md-vw !important;
    display: flex !important;
    align-items: center !important;
    height: 2vw !important; /* 添加固定高度 */

    .anticon {
      display: flex !important;
      align-items: center !important;
    }
  }

  // 按钮
  .ant-btn {
    font-size: @font-size-md-vw !important;

    .ant-btn-icon,
    span {
      font-size: @font-size-md-vw !important;
    }
  }

  // 树形控件
  .ant-tree {
    font-size: @font-size-md-vw !important;

    .ant-tree-node-content-wrapper,
    .ant-tree-title,
    .ant-tree-switcher {
      font-size: @font-size-md-vw !important;
    }
  }

  // 上传组件
  .ant-upload,
  .ant-upload-drag {
    font-size: @font-size-md-vw !important;
    position: relative !important;
    z-index: 1 !important;

    .ant-upload-text,
    .ant-upload-hint,
    .ant-upload-drag-container {
      font-size: @font-size-md-vw !important;
    }
  }

  // 修复文件上传区域的显示问题
  .ant-upload-wrapper {
    width: 100% !important;

    .ant-upload-drag {
      width: 100% !important;
      border: none !important;
      background: transparent !important;
    }
  }
}

// 为下拉菜单添加全局样式
.ant-select-dropdown {
  font-size: @font-size-md-vw !important;

  .ant-select-item {
    font-size: @font-size-md-vw !important;
    line-height: 2vw !important; /* 确保下拉项也具有合适的高度和行高 */
    padding: 0.25vw 0.5vw !important; /* 添加适当的内边距 */
  }
}

// 确保创建模板中的选择器下拉菜单也能正确应用字体大小
.create-template-container {
  .ant-select-dropdown {
    .ant-select-item {
      font-size: @font-size-md-vw !important;
      line-height: 2vw !important;
    }
  }
}

.ant-tree-show-line .ant-tree-switcher-line-icon {
  vertical-align: -0.6vw !important;
}

.ant-popover .ant-popover-inner {
  background-color: rgb(0 124 205) !important;
}