// ===========================================
// CA-Frontend 科技风主题变量系统
// 统一变量配置
// ===========================================

// ========== 科技风深蓝色主题 ==========
@primary-dark-blue: #001733; // 深蓝色主背景
@primary-blue: #409eff; // 主蓝色
@primary-color: @primary-blue; // 兼容性变量
@primary-hover: #40a9ff;
@primary-active: #096dd9;

// ========== 功能色彩 ==========
@success-green: #67c23a; // 成功绿色
@warning-orange: #e6a23c; // 警告橙色
@danger-red: #f56c6c; // 危险红色
@info-gray: #909399; // 信息灰色

// ========== 透明度变量 ==========
@bg-overlay-dark: rgba(0, 23, 51, 0.8); // 深色遮罩
@bg-overlay-medium: #00173380; // 中等遮罩 (taskFlow-main背景)
@bg-overlay-light: rgba(0, 15, 33, 0.2); // 浅色遮罩 (tab背景)
@bg-tab-normal: #000f2133; // Tab正常状态背景
@bg-color: @bg-overlay-medium; // 兼容性变量

// ========== 文字颜色 ==========
@text-white: rgb(255, 255, 255); // 白色文字
@text-secondary: rgba(255, 255, 255, 0.7); // 次要文字色
@text-disabled: rgba(255, 255, 255, 0.4); // 禁用文字色
@text-white-60: #fff6; // 60%透明度白色
@text-color: @text-white; // 兼容性变量

// ========== 字体设置 ==========
@font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
  Arial, sans-serif;
@font-size-base: 1.145833vw; // 基本字体大小
@font-size-sm: 1.145833vw;
@font-size-title: 1.40625vw; // 标题字体大小
@font-size-text: 1.041667vw; // 普通文本字体大小
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-bold: 600;
@line-height-base: 1.5715;

// ========== 间距 ==========
@margin-sm-vw: 0.520833vw; // 小型vw间距
@margin-xs-vw: 0.104167vw; // 超小型vw间距
@padding-top-sm: 0.208333vw; // 小型顶部内边距
@padding-top-l: 1.041667vw; // 大中型vw间距
@padding-left-l: 1.5625vw; // 大中型左侧内边距
@page-padding: @padding-top-l @padding-left-l; // tab页面内容区域
@title-padding: 0 @padding-left-l; // 标题区域

// ========== 边框和圆角 ==========
@border-radius-base: 6px; // 基础圆角
@border-radius-sm: 2px; // 小圆角
@border-radius-lg: 8px; // 大圆角
@border-radius-tab: 0.416667vw; // Tab圆角
@border-color-primary: rgba(64, 158, 255, 0.4); // 主要边框色
@border-color-active: rgba(64, 158, 255, 0.8); // 激活边框色
@border-color: @border-color-primary; // 兼容性变量

// ========== 发光和阴影效果 ==========
@glow-primary: 0 0 10px rgba(64, 158, 255, 0.6); // 主要发光
@glow-success: 0 0 8px rgba(103, 194, 58, 0.6); // 成功发光
@shadow-light: 0px 0px 8px rgba(0, 0, 0, 0.12); // 轻阴影
@shadow-card: 0 2px 24px rgba(0, 0, 0, 0.06);
@text-shadow-base: 1px 0.104167vw 0.208333vw rgba(0, 0, 0, 0.5); // 基础文本阴影

// ========== 布局尺寸 ==========
@layout-header-height: 4.6875vw; // 头部高度
@layout-tab-height: 2.604167vw; // Tab高度
@layout-sider-width: 256px;
@layout-content-padding: 1.041667vw 0; // 内容区域内边距
@layout-tab-margin-bottom: 1.5625vw; // Tab底部间距

// ========== 组件特定尺寸 ==========
// 头部组件
@header-back-top: 0.260417vw;
@header-back-left: 1.041667vw;
@header-user-right: 2.083333vw;
@header-user-top: 1.5625vw;

// 图标尺寸
@icon-back-width: 5.15625vw;
@icon-back-height: 4.6875vw;
@icon-tab-normal-size: 1.25vw;
@icon-tab-active-size: 1.666667vw;
@icon-avatar-size: 1.458333vw;
@icon-margin-right: 0.520833vw;
@icon-margin-left: 0.78125vw;

// 标题布局
@title-padding-left: calc(3.385417vw + 1.5625vw);
@title-letter-spacing: 0.15625vw;

// ========== 过渡动画 ==========
@transition-fast: 0.15s ease-out; // 快速过渡
@transition-base: 0.3s ease-out; // 基础过渡
@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);

// ========== Z-index层级 ==========
@z-index-normal: 1;
@z-index-dropdown: 1000;
@z-index-sticky: 1020;
@z-index-fixed: 1030;
@z-index-modal: 1050;
@z-index-popover: 1060;
@z-index-tooltip: 1070;
@z-index-user-bar: 999; // 用户信息栏层级

// ===========================================
// CA-Frontend 尺寸变量系统
// 所有px单位转为vw单位
// ===========================================

// ========== 卡片尺寸 ==========
@card-padding-vw: 1.041667vw; // 20px
@card-padding-lg-vw: 1.25vw; // 24px
@card-margin-bottom-vw: 1.25vw; // 24px
@card-border-radius-vw: 0.520833vw; // 10px
@card-border-width-vw: 0.052083vw; // 1px

// ========== 图标尺寸 ==========
@icon-size-sm-vw: 0.833333vw; // 16px
@icon-size-md-vw: 1.041667vw; // 20px
@icon-size-lg-vw: 1.458333vw; // 28px

// ========== 元素间距 ==========
@spacing-xs-vw: 0.208333vw; // 4px
@spacing-sm-vw: 0.416667vw; // 8px
@spacing-md-vw: 0.625vw; // 12px
@spacing-base-vw: 0.833333vw; // 16px
@spacing-lg-vw: 1.25vw; // 24px
@spacing-xl-vw: 1.666667vw; // 32px

// ========== 文字大小 ==========
@font-size-xs-vw: 0.833333vw; // 16px
@font-size-sm-vw: 1.041667vw; // 20px
@font-size-md-vw: 1.145833vw; // 22px
@font-size-lg-vw: 1.458333vw; // 28px
@font-size-xl-vw: 1.666667vw; // 32px

// ========== 边框和阴影 ==========
@border-width-vw: 0.052083vw; // 1px
@border-radius-xs-vw: 0.104167vw; // 2px
@border-radius-sm-vw: 0.208333vw; // 4px
@border-radius-md-vw: 0.3125vw; // 6px
@border-radius-lg-vw: 0.416667vw; // 8px
@border-radius-xl-vw: 0.520833vw; // 10px
@shadow-offset-vw: 0.104167vw; // 2px
@shadow-blur-vw: 0.625vw; // 12px
@shadow-spread-vw: 0.416667vw; // 8px

// ========== 组件特定尺寸 ==========
@table-action-size-vw: 1.666667vw; // 32px
@avatar-border-width-vw: 0.104167vw; // 2px
@dot-size-vw: 0.833333vw; // 16px
@timeline-width-vw: 0.104167vw; // 2px
@timeline-padding-left-vw: 1.25vw; // 24px
