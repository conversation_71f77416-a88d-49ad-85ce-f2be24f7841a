<template>
  <div class="algorithm-library-container">
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div v-for="(stat, index) in statsList" :key="index" :class="['stat-card', stat.type]">
        <div class="stat-header">
          <div class="stat-title">{{ stat.title }}</div>
          <div class="stat-icon">
            <i :class="['fas', `fa-${stat.icon}`]"></i>
          </div>
        </div>
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-description">
          <i :class="['fas', `fa-arrow-${stat.trend}`, `stat-trend-${stat.trend}`]"></i>
          <span>{{ stat.trendValue }}</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <div class="search-row">
        <div class="search-input-wrapper">
          <i class="fas fa-search search-icon"></i>
          <a-input
            v-model:value="searchQuery"
            class="search-input"
            placeholder="搜索算法名称、ID、创建人..."
            @change="handleSearch"
          />
        </div>
        <a-button class="advanced-filter-toggle" type="link" @click="toggleAdvancedFilter">
          <i :class="showAdvancedFilter ? 'fas fa-chevron-up' : 'fas fa-sliders-h'"></i>
          {{ showAdvancedFilter ? '收起筛选' : '高级筛选' }}
        </a-button>
      </div>
      <div class="advanced-filter-row" v-show="showAdvancedFilter">
        <div class="filter-group">
          <label class="filter-label">算法类型</label>
          <a-select v-model:value="filters.type" class="filter-select" style="width: 100%">
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option value="network">网络层算法</a-select-option>
            <a-select-option value="business">业务层算法</a-select-option>
            <a-select-option value="evaluation">评估算法</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">适用领域</label>
          <a-select v-model:value="filters.domain" class="filter-select" style="width: 100%">
            <a-select-option value="">全部领域</a-select-option>
            <a-select-option value="power">电力系统</a-select-option>
            <a-select-option value="transportation">交通系统</a-select-option>
            <a-select-option value="water">水利系统</a-select-option>
            <a-select-option value="gas">燃气系统</a-select-option>
            <a-select-option value="communication">通信系统</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">创建时间</label>
          <a-select v-model:value="filters.createTime" class="filter-select" style="width: 100%">
            <a-select-option value="">全部时间</a-select-option>
            <a-select-option value="today">今天</a-select-option>
            <a-select-option value="yesterday">昨天</a-select-option>
            <a-select-option value="last7days">最近7天</a-select-option>
            <a-select-option value="last30days">最近30天</a-select-option>
            <a-select-option value="custom">自定义范围</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">创建人</label>
          <a-select v-model:value="filters.creator" class="filter-select" style="width: 100%">
            <a-select-option value="">全部创建人</a-select-option>
            <a-select-option value="self">我创建的</a-select-option>
            <a-select-option value="team">我团队的</a-select-option>
          </a-select>
        </div>
      </div>
      <div class="filter-actions" v-show="showAdvancedFilter">
        <a-button class="action-btn" @click="resetFilters">重置筛选</a-button>
        <a-button type="primary" class="action-btn primary" @click="applyFilters"
          >应用筛选</a-button
        >
      </div>
    </div>

    <!-- 算法列表 -->
    <div class="algorithms-section">
      <div class="table-responsive">
        <div class="table-header">
          <div class="table-title">全部算法</div>
          <div class="table-actions">
            <div class="bulk-actions">
              <a-button class="bulk-action-btn" @click="openUploadAlgorithmModal">
                <template #icon><i class="fas fa-plus"></i></template>
                上传算法
              </a-button>
              <a-button class="bulk-action-btn">
                <template #icon><i class="fas fa-archive"></i></template>
                导入算法
              </a-button>
              <a-button class="bulk-action-btn">
                <template #icon><i class="fas fa-download"></i></template>
                导出算法
              </a-button>
              <a-button class="bulk-action-btn delete" danger>
                <template #icon><i class="fas fa-trash-alt"></i></template>
                删除算法
              </a-button>
            </div>
          </div>
        </div>
        <!-- Ant Design 表格 -->
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
          :row-key="(record) => record.id"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }"
        >
          <!-- 算法名称列 -->
          <template #bodyCell="{ column, record }">
            <!-- 算法名称列 -->
            <template v-if="column.key === 'name'">
              <div>
                <span class="algorithm-name">{{ record.name }}</span>
                <div class="algorithm-desc">{{ record.description }}</div>
              </div>
            </template>

            <!-- 算法类型列 -->
            <template v-else-if="column.key === 'type'">
              <span :class="['algorithm-type-badge', `type-${record.typeClass}`]">
                {{ record.type }}
              </span>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <div class="algorithm-actions-cell">
                <a-button class="algorithm-action view" title="查看" type="text">
                  <i class="fas fa-eye"></i>
                </a-button>
                <a-button
                  class="algorithm-action edit"
                  title="编辑"
                  type="text"
                  @click="openEditAlgorithmModal(record)"
                >
                  <i class="fas fa-edit"></i>
                </a-button>
                <a-button class="algorithm-action copy" title="复制" type="text">
                  <i class="fas fa-copy"></i>
                </a-button>
                <a-button class="algorithm-action download" title="下载" type="text">
                  <i class="fas fa-download"></i>
                </a-button>
                <a-popconfirm
                  title="确定要删除该算法吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="deleteAlgorithmItem(record)"
                >
                  <a-button class="algorithm-action delete" title="删除" type="text">
                    <i class="fas fa-trash-alt"></i>
                  </a-button>
                </a-popconfirm>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 定义组件名称
defineOptions({
  name: 'AlgorithmLibraryView',
})

// 定义表格列
const columns = [
  {
    title: '算法名称',
    dataIndex: 'name',
    key: 'name',
    width: '30%',
    ellipsis: true,
  },
  {
    title: '算法类型',
    dataIndex: 'type',
    key: 'type',
    width: '15%',
  },
  {
    title: '适用领域',
    dataIndex: 'domain',
    key: 'domain',
    width: '15%',
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: '10%',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: '15%',
  },
  {
    title: '操作',
    key: 'action',
    width: '15%',
  },
]

// 组件状态
const searchQuery = ref('')
const showAdvancedFilter = ref(false)
const selectAll = ref(false)
const loading = ref(false)
const tableData = ref([
  {
    id: '1',
    name: '网络流量分析算法',
    description: '分析网络流量模式和异常特征',
    type: '网络层算法',
    typeClass: 'network',
    domain: '电力系统',
    creator: '张三',
    createTime: '2023-09-25 14:30',
  },
  {
    id: '2',
    name: '业务风险评估算法',
    description: '评估业务层面的安全风险和影响',
    type: '业务层算法',
    typeClass: 'business',
    domain: '交通系统',
    creator: '李四',
    createTime: '2023-09-24 09:15',
  },
  {
    id: '3',
    name: '跨行业级联分析算法',
    description: '分析跨行业级联失效关系和模式',
    type: '评估算法',
    typeClass: 'evaluation',
    domain: '燃气系统',
    creator: '王五',
    createTime: '2023-09-23 16:45',
  },
  {
    id: '4',
    name: '网络拓扑分析算法',
    description: '自动发现和分析网络拓扑结构',
    type: '网络层算法',
    typeClass: 'network',
    domain: '水利系统',
    creator: '赵六',
    createTime: '2023-09-22 11:20',
  },
  {
    id: '5',
    name: '关键节点识别算法',
    description: '识别网络中的关键节点和链路',
    type: '评估算法',
    typeClass: 'evaluation',
    domain: '通信系统',
    creator: '张三',
    createTime: '2023-09-21 15:10',
  },
])

const statsList = ref([
  {
    title: '算法总数',
    value: '125',
    trendValue: '较上月增长 8.5%',
    trend: 'up',
    type: 'success',
    icon: 'code',
  },
  {
    title: '网络层算法',
    value: '48',
    trendValue: '较上月增长 5.2%',
    trend: 'up',
    type: 'info',
    icon: 'network-wired',
  },
  {
    title: '业务层算法',
    value: '53',
    trendValue: '较上月增长 12.8%',
    trend: 'up',
    type: 'warning',
    icon: 'chart-bar',
  },
  {
    title: '评估算法',
    value: '24',
    trendValue: '较上月增长 4.3%',
    trend: 'up',
    type: 'danger',
    icon: 'analytics',
  },
])

const selectedRowKeys = ref([])

// 分页设置
const pagination = reactive({
  current: 1,
  pageSize: 5,
  total: 125,
  showTotal: (total) => `共 ${total} 条记录`,
  showSizeChanger: true,
  pageSizeOptions: ['5', '10', '20'],
  showLessItems: true,
})

// 筛选条件
const filters = reactive({
  type: '',
  domain: '',
  createTime: '',
  creator: '',
})

// 生命周期钩子
onMounted(async () => {
  // 初始化数据
  pagination.total = tableData.value.length
  // 可以添加API调用来获取真实数据
})

// 切换高级筛选
function toggleAdvancedFilter() {
  showAdvancedFilter.value = !showAdvancedFilter.value
}

// 重置筛选条件
function resetFilters() {
  searchQuery.value = ''
  filters.type = ''
  filters.domain = ''
  filters.createTime = ''
  filters.creator = ''
}

// 应用筛选条件
function applyFilters() {
  pagination.current = 1
  // 这里应该调用后端API获取筛选后的数据
  message.success('应用筛选条件')
}

// 处理搜索
function handleSearch() {
  pagination.current = 1
  // 这里应该调用后端API进行搜索
  message.info(`搜索: ${searchQuery.value}`)
}

// 表格选择变化
function onSelectChange(selectedKeys) {
  selectedRowKeys.value = selectedKeys
  selectAll.value = selectedKeys.length > 0 && selectedKeys.length === tableData.value.length
}

// 表格分页、排序、筛选变化
function handleTableChange(pag) {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  // 这里应该调用后端API获取对应页码的数据
  message.info(`页码: ${pag.current}, 每页条数: ${pag.pageSize}`)
}

// 删除算法
function deleteAlgorithmItem(record) {
  message.success(`删除算法: ${record.name}`)
  const index = tableData.value.findIndex((item) => item.id === record.id)
  if (index !== -1) {
    tableData.value.splice(index, 1)
  }
}

// 打开上传算法模态窗口
function openUploadAlgorithmModal() {
  message.info('打开上传算法窗口')
}

// 打开编辑算法模态窗口
function openEditAlgorithmModal(record) {
  message.info(`编辑算法: ${record.name}`)
}
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.algorithm-library-container {
  width: 100%;
  background-color: transparent;
  color: #e0e6f0;
  padding: @page-padding;

  // 覆盖 Ant Design 的默认样式
  :deep(.ant-table) {
    background-color: transparent;
    color: #e0e6f0;
    border-radius: 0;
  }

  :deep(.ant-table-container) {
    border-radius: 0;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: rgba(255, 255, 255, 0.02);
    color: #07f6ff;
    border-bottom: 1px solid rgba(25, 164, 255, 0.5);
    padding: 0.8vw @spacing-md-vw;
    font-size: @font-size-md-vw;
    border-radius: 0 !important;
  }

  :deep(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid rgba(25, 164, 255, 0.5);
    color: #e0e6f0;
    background-color: transparent;
    padding: 0.8vw @spacing-md-vw;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: rgba(255, 255, 255, 0.05);
  }

  :deep(.ant-checkbox-inner) {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 164, 255, 0.5);
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: #0096ff;
    border-color: #0096ff;
  }
}

// 统计卡片
.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: @card-margin-bottom-vw;
  margin-bottom: @card-margin-bottom-vw;
}

.stat-card {
  background-color: rgba(25, 164, 255, 0.5);
  border-radius: @card-border-radius-vw;
  padding: @card-padding-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-@spacing-xs-vw);
  box-shadow: 0 @spacing-sm-vw @spacing-lg-vw rgba(0, 0, 0, 0.15);
  border-color: #0096ff;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: @spacing-xs-vw;
  height: 100%;
  background-color: #0096ff;
  border-radius: @spacing-xs-vw 0 0 @spacing-xs-vw;
}

.stat-card.success::before {
  background-color: #00c48c;
}

.stat-card.warning::before {
  background-color: #ffb946;
}

.stat-card.danger::before {
  background-color: #f25767;
}

.stat-card.info::before {
  background-color: #0095ff;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @spacing-base-vw;
}

.stat-title {
  font-size: @font-size-md-vw;
  color: #07f6ff;
  font-weight: 500;
}

.stat-icon {
  width: 2vw;
  height: 2vw;
  border-radius: @spacing-sm-vw;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: @font-size-md-vw;
  background-color: rgba(0, 149, 255, 0.1);
  color: #0096ff;
}

.stat-card.success .stat-icon {
  background-color: rgba(0, 196, 140, 0.1);
  color: #00c48c;
}

.stat-card.warning .stat-icon {
  background-color: rgba(255, 185, 70, 0.1);
  color: #ffb946;
}

.stat-card.danger .stat-icon {
  background-color: rgba(242, 87, 103, 0.1);
  color: #f25767;
}

.stat-value {
  font-size: @font-size-lg-vw;
  font-weight: 600;
  margin-bottom: @spacing-xs-vw;
}

.stat-description {
  font-size: @font-size-sm-vw;
  color: #2affac;
  display: flex;
  align-items: center;
  gap: @spacing-xs-vw;
}

.stat-trend-up {
  color: #00c48c;
}

.stat-trend-down {
  color: #f25767;
}

// 搜索和筛选区域
.filter-section {
  padding: @card-padding-vw;
  margin-bottom: @card-margin-bottom-vw;
  border: 1px solid rgba(25, 164, 255, 0.5);
}

.search-row {
  display: flex;
  align-items: center;
  gap: @spacing-md-vw;
  margin-bottom: @spacing-md-vw;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
  padding: @spacing-sm-vw 2vw @spacing-sm-vw 2.5vw;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(25, 164, 255, 0.5);
  color: #e0e6f0;
  font-size: @font-size-md-vw;
  height: 2.2vw;
  transition: all 0.3s ease;

  &::placeholder {
    color: rgba(224, 230, 240, 0.7);
  }
}

.search-icon {
  position: absolute;
  left: 0.8vw;
  top: 50%;
  transform: translateY(-50%);
  color: #07f6ff;
  z-index: 1;
}

.advanced-filter-toggle {
  white-space: nowrap;
  color: #07f6ff;
  background: none;
  border: none;
  cursor: pointer;
  font-size: @font-size-md-vw;
  display: flex;
  align-items: center;
  gap: @spacing-xs-vw;
  padding: 0 @spacing-sm-vw;
  height: 2.2vw;
}

.advanced-filter-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: @spacing-md-vw;
  margin-top: @spacing-md-vw;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: @spacing-sm-vw;
}

.filter-label {
  font-size: @font-size-md-vw;
  color: #07f6ff;
}

:deep(.filter-select) {
  .ant-select-selector {
    height: 2.2vw !important;
    line-height: 2.2vw;
    padding: 0 @spacing-sm-vw !important;
    font-size: @font-size-md-vw;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(25, 164, 255, 0.5) !important;
  }

  .ant-select-selection-item {
    display: flex;
    align-items: center;
    height: 100%;
    color: #e0e6f0;
    font-size: @font-size-md-vw;
    line-height: 2.2vw;
  }

  .ant-select-selection-placeholder {
    color: rgba(224, 230, 240, 0.7);
    line-height: 2.2vw;
    display: flex;
    align-items: center;
  }

  .ant-select-arrow {
    color: #07f6ff;
  }
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: @spacing-md-vw;

  .action-btn {
    margin-left: @spacing-md-vw;
    // 其余样式已移至全局 .tech-action-btn() 混入
  }
}

// 算法列表表格
.algorithms-section {
  margin-bottom: @card-margin-bottom-vw;
}

.table-responsive {
  overflow: hidden;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: @spacing-md-vw @card-padding-vw;
  border-bottom: 1px solid rgba(25, 164, 255, 0.5);
  background: rgba(25, 164, 255, 0.3);
}

.table-title {
  font-size: @font-size-lg-vw;
  font-family: YouSheBiaoTiHei, sans-serif;
  text-shadow: 1px 0.104167vw 0.208333vw rgba(0, 0, 0, 0.5);
}

.table-actions {
  display: flex;
  gap: @spacing-sm-vw;
}

.bulk-actions {
  display: flex;
  gap: @spacing-sm-vw;
}

// 使用全局样式混入
.bulk-action-btn {
  // 样式已移至全局 .tech-bulk-action-btn() 混入
}

.algorithm-type-badge {
  display: inline-block;
  padding: @spacing-xs-vw @spacing-sm-vw;
  border-radius: @border-radius-xs-vw;
  font-size: @font-size-sm-vw;
  font-weight: 500;
}

.type-network {
  background-color: rgba(0, 149, 255, 0.1);
  color: #0095ff;
}

.type-business {
  background-color: rgba(255, 185, 70, 0.1);
  color: #ffb946;
}

.type-evaluation {
  background-color: rgba(0, 196, 140, 0.1);
  color: #00c48c;
}

.algorithm-name {
  font-size: @font-size-md-vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #2affac;
}

.algorithm-desc {
  font-size: @font-size-xs-vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 0.5vw;
}

.algorithm-actions-cell {
  white-space: nowrap;
}

.algorithm-action {
  .tech-icon-action-btn();
}
</style>
