<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card info" @click="navigateTo('evaluation-task')">
        <div class="stat-header">
          <div class="stat-title">评估任务总数</div>
          <div class="stat-icon">
            <i class="fas fa-tasks"></i>
          </div>
        </div>
        <div class="stat-value">50</div>
        <div class="stat-description">
          <i class="fas fa-arrow-up stat-trend-up"></i>
          <span>较上月增长12个</span>
        </div>
      </div>
      <div class="stat-card primary">
        <div class="stat-header">
          <div class="stat-title">进行中</div>
          <div class="stat-icon">
            <i class="fas fa-spinner"></i>
          </div>
        </div>
        <div class="stat-value">15</div>
        <div class="stat-description">
          <i class="fas fa-arrow-up stat-trend-up"></i>
          <span>较上月增长8个</span>
        </div>
      </div>
      <div class="stat-card success">
        <div class="stat-header">
          <div class="stat-title">已评估</div>
          <div class="stat-icon">
            <i class="fas fa-chart-line"></i>
          </div>
        </div>
        <div class="stat-value">20</div>
        <div class="stat-description">
          <i class="fas fa-arrow-up stat-trend-up"></i>
          <span>较上月增长5个</span>
        </div>
      </div>
      <div class="stat-card warning">
        <div class="stat-header">
          <div class="stat-title">待评估</div>
          <div class="stat-icon">
            <i class="fas fa-tachometer-alt"></i>
          </div>
        </div>
        <div class="stat-value">15</div>
        <div class="stat-description">
          <i class="fas fa-arrow-down stat-trend-down"></i>
          <span>较上月减少5个</span>
        </div>
      </div>
      <div class="stat-card danger">
        <div class="stat-header">
          <div class="stat-title">测评模版总数</div>
          <div class="stat-icon">
            <i class="fas fa-cube"></i>
          </div>
        </div>
        <div class="stat-value">42</div>
        <div class="stat-description">
          <i class="fas fa-arrow-up stat-trend-up"></i>
          <span>较上月增加3个</span>
        </div>
      </div>
    </div>

    <!-- 仪表盘网格内容 -->
    <div class="dashboard-grid">
      <!-- 左侧内容 -->
      <div class="dashboard-left-column">
        <!-- 评估任务状态分布 -->
        <div class="dashboard-card task-status-card">
          <div class="card-header">
            <div class="card-title">评估任务状态分布</div>
            <div class="card-actions">
              <a-button class="card-action" type="text">
                <i class="fas fa-sync-alt"></i>
              </a-button>
              <a-button class="card-action" type="text">
                <i class="fas fa-expand"></i>
              </a-button>
              <a-button class="card-action" type="text">
                <i class="fas fa-ellipsis-v"></i>
              </a-button>
            </div>
          </div>
          <div class="card-body">
            <div class="task-chart-container">
              <div ref="taskDonutChartRef" class="task-donut-chart"></div>
              <div ref="taskBarChartRef" class="task-bar-chart"></div>
            </div>
          </div>
        </div>

        <!-- 单行业仿真度评估次数趋势分析 -->
        <div class="dashboard-card simulation-trend-card">
          <div class="card-header">
            <div class="card-title">单行业仿真度评估次数趋势分析</div>
            <div class="card-actions">
              <a-button class="card-action" type="text">
                <i class="fas fa-sync-alt"></i>
              </a-button>
              <a-button class="card-action" type="text">
                <i class="fas fa-expand"></i>
              </a-button>
              <a-button class="card-action" type="text">
                <i class="fas fa-ellipsis-v"></i>
              </a-button>
            </div>
          </div>
          <div class="card-body">
            <div ref="simulationChartRef" class="simulation-chart-container"></div>
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="dashboard-right-column">
        <!-- 评估场景分布 -->
        <div class="dashboard-card scene-distribution-card">
          <div class="card-header">
            <div class="card-title">评估场景分布</div>
            <div class="card-actions">
              <a-button class="card-action" type="text">
                <i class="fas fa-sync-alt"></i>
              </a-button>
              <a-button class="card-action" type="text">
                <i class="fas fa-expand"></i>
              </a-button>
              <a-button class="card-action" type="text">
                <i class="fas fa-ellipsis-v"></i>
              </a-button>
            </div>
          </div>
          <div class="card-body">
            <div ref="sceneDistributionChartRef" class="scene-chart"></div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="dashboard-card recent-activity-card">
          <div class="card-header">
            <div class="card-title">最近活动</div>
            <div class="card-actions">
              <a-button class="card-action" type="text" @click="refreshActivities">
                <i class="fas fa-sync-alt"></i>
              </a-button>
              <a-button class="card-action" type="text">
                <i class="fas fa-ellipsis-v"></i>
              </a-button>
            </div>
          </div>
          <div class="card-body">
            <div class="activity-timeline">
              <a-timeline>
                <a-timeline-item
                  v-for="(activity, index) in recentActivities"
                  :key="index"
                  :color="getActivityColor(activity.type)"
                >
                  <div class="activity-content">
                    <div class="activity-title">{{ activity.title }}</div>
                    <div class="activity-time">{{ activity.time }}</div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'

// 定义组件名称
defineOptions({
  name: 'DashboardView',
})

// 路由对象
const router = useRouter()

// 图表引用
const taskDonutChartRef = ref(null)
const taskBarChartRef = ref(null)
const simulationChartRef = ref(null)
const sceneDistributionChartRef = ref(null)

// 图表实例
let taskDonutChart: echarts.ECharts | null = null
let taskBarChart: echarts.ECharts | null = null
let simulationChart: echarts.ECharts | null = null
let sceneDistributionChart: echarts.ECharts | null = null

// 最近活动数据
const recentActivities = ref([
  {
    title: '您创建了新的评估任务"供水系统压力仿真评估-2025年Q1"',
    time: '今天 09:32',
    type: 'success'
  },
  {
    title: '小刘更新了"交通流量模拟"算法参数',
    time: '昨天 14:23',
    type: 'warning'
  },
  {
    title: '您审核通过了"燃气管网压力监测"数据采集任务',
    time: '04-02 11:05',
    type: 'success'
  },
  {
    title: '系统管理员发布了新版测评模版',
    time: '04-01 08:30',
    type: 'info'
  },
  {
    title: '数据采集任务"城市交通网络拓扑"执行失败',
    time: '03-31 16:45',
    type: 'danger'
  },
  {
    title: '张工完成了"电力网络仿真"模型验证',
    time: '03-30 13:20',
    type: 'success'
  },
  {
    title: '系统自动备份数据库完成',
    time: '03-30 02:00',
    type: 'info'
  },
  {
    title: '李经理审批了"水务管网评估"项目申请',
    time: '03-29 16:30',
    type: 'success'
  },
  {
    title: '算法库更新：新增机器学习预测模型',
    time: '03-29 10:15',
    type: 'info'
  },
  {
    title: '数据采集服务器连接异常',
    time: '03-28 18:45',
    type: 'danger'
  },
  {
    title: '您提交了"城市燃气管网"安全评估报告',
    time: '03-28 14:20',
    type: 'success'
  },
  {
    title: '王工修改了"交通信号优化"算法配置',
    time: '03-27 11:30',
    type: 'warning'
  }
])

// 页面跳转函数
function navigateTo(path: string) {
  router.push(`/${path}`)
}

// 获取活动颜色
function getActivityColor(type: string) {
  switch (type) {
    case 'success':
      return '#00c48c'
    case 'warning':
      return '#ffb946'
    case 'danger':
      return '#f25767'
    case 'info':
      return '#0096ff'
    default:
      return '#0096ff'
  }
}

// 刷新活动数据
function refreshActivities() {
  // 这里可以调用API获取最新的活动数据
  console.log('刷新活动数据')
}

// 初始化图表函数
function initCharts() {
  // 初始化任务状态饼图
  if (taskDonutChartRef.value) {
    taskDonutChart = echarts.init(taskDonutChartRef.value)
    const taskDonutOption = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        textStyle: {
          color: '#A0A8B8'
        }
      },
      series: [
        {
          name: '任务状态',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#1A202E',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
              color: '#E0E6F0'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 48, name: '已完成' },
            { value: 32, name: '进行中' },
            { value: 12, name: '已暂停' },
            { value: 8, name: '已失败' }
          ]
        }
      ],
      color: ['#00C48C', '#0095FF', '#FFB946', '#F25767']
    }
    taskDonutChart.setOption(taskDonutOption)
  }

  // 初始化任务状态柱状图
  if (taskBarChartRef.value) {
    taskBarChart = echarts.init(taskBarChartRef.value)
    const taskBarOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#2A3142'
          }
        },
        axisLabel: {
          color: '#A0A8B8'
        },
        splitLine: {
          lineStyle: {
            color: '#2A3142',
            type: 'dashed'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: ['通信行业', '政务行业', '交通行业', '电力行业', '通信-电力级联'],
        axisLine: {
          lineStyle: {
            color: '#2A3142'
          }
        },
        axisLabel: {
          color: '#A0A8B8'
        }
      },
      series: [
        {
          name: '已完成',
          type: 'bar',
          stack: '总量',
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [12, 14, 8, 6, 8]
        },
        {
          name: '进行中',
          type: 'bar',
          stack: '总量',
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [8, 6, 6, 7, 5]
        },
        {
          name: '已暂停',
          type: 'bar',
          stack: '总量',
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [3, 2, 4, 2, 1]
        },
        {
          name: '已失败',
          type: 'bar',
          stack: '总量',
          label: {
            show: false
          },
          emphasis: {
            focus: 'series'
          },
          data: [1, 2, 1, 3, 1]
        }
      ],
      color: ['#00C48C', '#0095FF', '#FFB946', '#F25767']
    }
    taskBarChart.setOption(taskBarOption)
  }

  // 初始化仿真趋势图表
  if (simulationChartRef.value) {
    simulationChart = echarts.init(simulationChartRef.value)
    const simulationOption = {
      grid: {
        left: '3%',
        right: '5%',
        bottom: '3%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['电力系统', '交通系统', '水利系统', '燃气系统', '通信系统', '总评估次数'],
        textStyle: {
          color: '#A0A8B8'
        }
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        axisLine: {
          lineStyle: {
            color: '#2A3142'
          }
        },
        axisLabel: {
          color: '#A0A8B8'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '测评次数',
          nameTextStyle: {
            color: '#A0A8B8'
          },
          min: 0,
          max: 150,
          interval: 30,
          axisLine: {
            lineStyle: {
              color: '#2A3142'
            }
          },
          axisLabel: {
            color: '#A0A8B8'
          },
          splitLine: {
            lineStyle: {
              color: '#2A3142',
              type: 'dashed'
            }
          }
        }
      ],
      series: [
        {
          name: '电力系统',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          data: [28, 22, 30, 34, 29, 25, 20, 33, 35, 31, 28, 32]
        },
        {
          name: '交通系统',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          data: [15, 17, 20, 24, 27, 25, 22, 18, 16, 21, 23, 25]
        },
        {
          name: '水利系统',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          data: [12, 14, 16, 18, 20, 22, 23, 19, 17, 15, 13, 14]
        },
        {
          name: '燃气系统',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          data: [18, 16, 15, 13, 16, 19, 22, 24, 21, 18, 20, 23]
        },
        {
          name: '通信系统',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          data: [10, 12, 15, 18, 22, 25, 28, 26, 23, 20, 18, 16]
        },
        {
          name: '总评估次数',
          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#FFB946'
          },
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#FFB946'
          },
          data: [83, 81, 96, 107, 114, 116, 115, 120, 112, 105, 102, 110],
          z: 5,
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderWidth: 2,
              borderColor: '#fff'
            }
          }
        }
      ],
      color: ['#0095FF', '#00C48C', '#FFB946', '#F25767', '#9760FF']
    }
    simulationChart.setOption(simulationOption)
  }

  // 初始化场景分布图表
  if (sceneDistributionChartRef.value) {
    sceneDistributionChart = echarts.init(sceneDistributionChartRef.value)
    const sceneDistributionOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        textStyle: {
          color: '#A0A8B8'
        }
      },
      series: [
        {
          name: '评估场景',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#1A202E',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
              color: '#E0E6F0'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 35, name: '中小企业网场景' },
            { value: 25, name: '轨道交通场景' },
            { value: 20, name: '危险气体缓冲工艺场景' },
            { value: 15, name: '通信-电力级联风险场景' },
            { value: 5, name: '通信-交通级联风险场景' }
          ]
        }
      ],
      color: ['#0095FF', '#00C48C', '#FFB946', '#F25767', '#9760FF']
    }
    sceneDistributionChart.setOption(sceneDistributionOption)
  }
}

// 窗口大小变化时调整图表尺寸
function handleResize() {
  taskDonutChart?.resize()
  taskBarChart?.resize()
  simulationChart?.resize()
  sceneDistributionChart?.resize()
}

// 组件挂载后初始化图表
onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时销毁图表
onUnmounted(() => {
  taskDonutChart?.dispose()
  taskBarChart?.dispose()
  simulationChart?.dispose()
  sceneDistributionChart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.dashboard-container {
  width: 100%;
  background-color: transparent;
  color: #e0e6f0;
  padding: @page-padding;
}

// 统计卡片
.stats-cards {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: @card-margin-bottom-vw;
  margin-bottom: @card-margin-bottom-vw;
}

.stat-card {
  background-color: rgba(25, 164, 255, 0.5);
  border-radius: @card-border-radius-vw;
  padding: @card-padding-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-@spacing-xs-vw);
  box-shadow: 0 @spacing-sm-vw @spacing-lg-vw rgba(0, 0, 0, 0.15);
  border-color: #0096ff;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: @spacing-xs-vw;
  height: 100%;
  background-color: #0096ff;
  border-radius: @spacing-xs-vw 0 0 @spacing-xs-vw;
}

.stat-card.success::before {
  background-color: #00c48c;
}

.stat-card.warning::before {
  background-color: #ffb946;
}

.stat-card.danger::before {
  background-color: #f25767;
}

.stat-card.info::before {
  background-color: #0095ff;
}

.stat-card.primary::before {
  background-color: #0096ff;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @spacing-base-vw;
}

.stat-title {
  font-size: @font-size-md-vw;
  color: #07f6ff;
  font-weight: 500;
}

.stat-icon {
  width: 2vw;
  height: 2vw;
  border-radius: @spacing-sm-vw;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: @font-size-md-vw;
  background-color: rgba(0, 149, 255, 0.1);
  color: #0096ff;
}

.stat-card.success .stat-icon {
  background-color: rgba(0, 196, 140, 0.1);
  color: #00c48c;
}

.stat-card.warning .stat-icon {
  background-color: rgba(255, 185, 70, 0.1);
  color: #ffb946;
}

.stat-card.danger .stat-icon {
  background-color: rgba(242, 87, 103, 0.1);
  color: #f25767;
}

.stat-value {
  font-size: @font-size-lg-vw;
  font-weight: 600;
  margin-bottom: @spacing-xs-vw;
}

.stat-description {
  font-size: @font-size-sm-vw;
  color: #2affac;
  display: flex;
  align-items: center;
  gap: @spacing-xs-vw;
}

.stat-trend-up {
  color: #00c48c;
}

.stat-trend-down {
  color: #f25767;
}

// 仪表盘网格布局
.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: @card-margin-bottom-vw;
}

.dashboard-left-column {
  display: flex;
  flex-direction: column;
  gap: @card-margin-bottom-vw;
}

.dashboard-right-column {
  display: flex;
  flex-direction: column;
  gap: @card-margin-bottom-vw;
}

// 卡片通用样式
.dashboard-card {
  background-color: rgba(25, 164, 255, 0.3);
  border-radius: @card-border-radius-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  overflow: hidden;
}

.card-header {
  padding: @spacing-md-vw @card-padding-vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(25, 164, 255, 0.5);
}

.card-title {
  font-size: @font-size-md-vw;
  font-weight: 500;
  color: #07f6ff;
}

.card-actions {
  display: flex;
  gap: @spacing-sm-vw;
}

:deep(.card-action) {
  background: none;
  border: none;
  color: #a0a8b8;
  cursor: pointer;
  font-size: @font-size-md-vw;
  width: 1.8vw;
  height: 1.8vw;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: @spacing-xs-vw;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: #e0e6f0;
  }
}

.card-body {
  padding: @card-padding-vw;
}

// 任务状态卡片
.task-status-card {
  height: 360px;
}

.task-chart-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: @spacing-md-vw;
  height: 280px;
}

.task-donut-chart, .task-bar-chart {
  height: 100%;
  width: 100%;
}

// 仿真趋势图卡片
.simulation-trend-card {
  height: 360px;
}

.simulation-chart-container {
  width: 100%;
  height: 280px;
}

// 评估场景分布卡片
.scene-distribution-card {
  height: 360px;
}

.scene-chart {
  width: 100%;
  height: 280px;
}

// 最近活动卡片
.recent-activity-card {
  height: 360px;

  .card-body {
    height: calc(100% - 60px); // 减去 header 的高度
    padding: @card-padding-vw;
  }
}

.activity-timeline {
  position: relative;
  height: 100%;
  max-height: 280px; // 设置最大高度以确保滚动
  overflow-y: auto;
  padding-right: 8px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 23, 51, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(25, 164, 255, 0.6);
    border-radius: 3px;

    &:hover {
      background: rgba(25, 164, 255, 0.8);
    }
  }
}

// Ant Design Timeline 样式定制
:deep(.ant-timeline) {
  font-size: @font-size-md-vw;
  color: white;
  min-height: 300px; // 确保有足够的高度触发滚动

  .ant-timeline-item {
    padding-bottom: @spacing-md-vw;
  }

  .ant-timeline-item-content {
    margin-left: @spacing-lg-vw;
    margin-bottom: 0;
  }
}

:deep(.ant-timeline-item-tail) {
  border-inline-start: 2px solid rgba(25, 164, 255, 0.5) !important;
}

.activity-content {
  padding-bottom: @spacing-sm-vw;
  border-bottom: 1px dashed rgba(25, 164, 255, 0.3);
}

:deep(.ant-timeline-item:last-child) .activity-content {
  border-bottom: none;
}

.activity-title {
  font-size: @font-size-md-vw;
  margin-bottom: @spacing-xs-vw;
  color: #e0e6f0;
}

.activity-time {
  font-size: @font-size-sm-vw;
  color: #a0a8b8;
}

// 响应式调整
@media screen and (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .task-chart-container {
    grid-template-columns: 1fr;
  }
}
</style>
