<template>
  <div class="data-collection-container">
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div v-for="(stat, index) in statsList" :key="index" :class="['stat-card', stat.type]">
        <div class="stat-header">
          <div class="stat-title">{{ stat.title }}</div>
          <div class="stat-icon">
            <i :class="['fas', `fa-${stat.icon}`]"></i>
          </div>
        </div>
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-description">
          <i :class="['fas', `fa-arrow-${stat.trend}`, `stat-trend-${stat.trend}`]"></i>
          <span>{{ stat.trendValue }}</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <div class="search-row">
        <div class="search-input-wrapper">
          <i class="fas fa-search search-icon"></i>
          <a-input
            v-model:value="searchQuery"
            class="search-input"
            placeholder="搜索任务名称、ID、创建人..."
            @change="handleSearch"
          />
        </div>
        <a-button class="advanced-filter-toggle" type="link" @click="toggleAdvancedFilter">
          <i :class="showAdvancedFilter ? 'fas fa-chevron-up' : 'fas fa-sliders-h'"></i>
          {{ showAdvancedFilter ? '收起筛选' : '高级筛选' }}
        </a-button>
      </div>
      <div class="advanced-filter-row" v-show="showAdvancedFilter">
        <div class="filter-group">
          <label class="filter-label">任务状态</label>
          <a-select v-model:value="filters.status" class="filter-select" style="width: 100%">
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="running">进行中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="pending">待执行</a-select-option>
            <a-select-option value="failed">失败</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">所属领域</label>
          <a-select v-model:value="filters.domain" class="filter-select" style="width: 100%">
            <a-select-option value="">全部领域</a-select-option>
            <a-select-option value="power">电力系统</a-select-option>
            <a-select-option value="transportation">交通系统</a-select-option>
            <a-select-option value="water">水利系统</a-select-option>
            <a-select-option value="gas">燃气系统</a-select-option>
            <a-select-option value="communication">通信系统</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">创建时间</label>
          <a-select v-model:value="filters.createTime" class="filter-select" style="width: 100%">
            <a-select-option value="">全部时间</a-select-option>
            <a-select-option value="today">今天</a-select-option>
            <a-select-option value="yesterday">昨天</a-select-option>
            <a-select-option value="last7days">最近7天</a-select-option>
            <a-select-option value="last30days">最近30天</a-select-option>
            <a-select-option value="custom">自定义范围</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">创建人</label>
          <a-select v-model:value="filters.creator" class="filter-select" style="width: 100%">
            <a-select-option value="">全部创建人</a-select-option>
            <a-select-option value="self">我创建的</a-select-option>
            <a-select-option value="team">我团队的</a-select-option>
          </a-select>
        </div>
      </div>
      <div class="filter-actions" v-show="showAdvancedFilter">
        <a-button class="action-btn" @click="resetFilters">重置筛选</a-button>
        <a-button type="primary" class="action-btn primary" @click="applyFilters"
          >应用筛选</a-button
        >
      </div>
    </div>

    <!-- 数据采集任务列表 -->
    <div class="tasks-section">
      <div class="table-responsive">
        <div class="table-header">
          <div class="table-title">全部数据采集任务</div>
          <div class="table-actions">
            <div class="bulk-actions">
              <a-button class="bulk-action-btn" @click="openCreateTaskModal">
                <template #icon><i class="fas fa-plus"></i></template>
                新建任务
              </a-button>
              <a-button class="bulk-action-btn">
                <template #icon><i class="fas fa-play"></i></template>
                启动任务
              </a-button>
              <a-button class="bulk-action-btn">
                <template #icon><i class="fas fa-pause"></i></template>
                暂停任务
              </a-button>
              <a-button class="bulk-action-btn delete" danger>
                <template #icon><i class="fas fa-trash-alt"></i></template>
                删除任务
              </a-button>
            </div>
          </div>
        </div>
        <!-- Ant Design 表格 -->
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
          :row-key="(record) => record.id"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }"
        >
          <!-- 任务名称列 -->
          <template #bodyCell="{ column, record }">
            <!-- 任务名称列 -->
            <template v-if="column.key === 'name'">
              <div>
                <span class="task-name">{{ record.name }}</span>
                <div class="task-desc">{{ record.description }}</div>
              </div>
            </template>

            <!-- 状态列 -->
            <template v-else-if="column.key === 'status'">
              <span :class="['task-status-badge', `status-${record.statusClass}`]">
                {{ record.status }}
              </span>
            </template>

            <!-- 进度列 -->
            <template v-else-if="column.key === 'progress'">
              <a-progress :percent="record.progress" size="small" :status="record.progressStatus" />
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <div class="task-actions-cell">
                <a-button
                  class="task-action view"
                  title="查看详情"
                  type="text"
                  @click="viewTaskDetail(record)"
                >
                  <i class="fas fa-eye"></i>
                </a-button>
                <a-button
                  class="task-action edit"
                  title="编辑"
                  type="text"
                  @click="openEditTaskModal(record)"
                >
                  <i class="fas fa-edit"></i>
                </a-button>
                <a-button
                  class="task-action start"
                  title="启动"
                  type="text"
                  v-if="record.statusClass === 'pending' || record.statusClass === 'paused'"
                  @click="startTask(record)"
                >
                  <i class="fas fa-play"></i>
                </a-button>
                <a-button
                  class="task-action pause"
                  title="暂停"
                  type="text"
                  v-if="record.statusClass === 'running'"
                  @click="pauseTask(record)"
                >
                  <i class="fas fa-pause"></i>
                </a-button>
                <a-button
                  class="task-action download"
                  title="下载数据"
                  type="text"
                  v-if="record.statusClass === 'completed'"
                  @click="downloadData(record)"
                >
                  <i class="fas fa-download"></i>
                </a-button>
                <a-popconfirm
                  title="确定要删除该任务吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="deleteTaskItem(record)"
                >
                  <a-button class="task-action delete" title="删除" type="text">
                    <i class="fas fa-trash-alt"></i>
                  </a-button>
                </a-popconfirm>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 定义组件名称
defineOptions({
  name: 'DataCollectionView',
})

// 定义表格列
const columns = [
  {
    title: '任务名称',
    dataIndex: 'name',
    key: 'name',
    width: '25%',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '10%',
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: '15%',
  },
  {
    title: '所属领域',
    dataIndex: 'domain',
    key: 'domain',
    width: '10%',
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: '10%',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: '15%',
  },
  {
    title: '操作',
    key: 'action',
    width: '15%',
  },
]

// 组件状态
const searchQuery = ref('')
const showAdvancedFilter = ref(false)
const selectAll = ref(false)
const loading = ref(false)
const tableData = ref([
  {
    id: '1',
    name: '电力系统数据采集任务A',
    description: '采集主要电力节点的网络流量数据',
    status: '进行中',
    statusClass: 'running',
    progress: 65,
    progressStatus: 'active',
    domain: '电力系统',
    creator: '张三',
    createTime: '2023-09-25 14:30',
  },
  {
    id: '2',
    name: '交通系统数据采集任务B',
    description: '采集交通信号网络的数据包',
    status: '已完成',
    statusClass: 'completed',
    progress: 100,
    progressStatus: 'success',
    domain: '交通系统',
    creator: '李四',
    createTime: '2023-09-23 09:15',
  },
  {
    id: '3',
    name: '水利系统数据采集任务C',
    description: '监控水利系统节点数据流',
    status: '待执行',
    statusClass: 'pending',
    progress: 0,
    progressStatus: 'normal',
    domain: '水利系统',
    creator: '王五',
    createTime: '2023-09-20 16:45',
  },
  {
    id: '4',
    name: '燃气系统数据采集任务D',
    description: '收集燃气管道监控网络数据',
    status: '已暂停',
    statusClass: 'paused',
    progress: 38,
    progressStatus: 'exception',
    domain: '燃气系统',
    creator: '赵六',
    createTime: '2023-09-18 10:20',
  },
  {
    id: '5',
    name: '通信系统数据采集任务E',
    description: '采集通信网络关键节点数据',
    status: '失败',
    statusClass: 'failed',
    progress: 42,
    progressStatus: 'exception',
    domain: '通信系统',
    creator: '钱七',
    createTime: '2023-09-15 11:05',
  },
])

const statsList = ref([
  {
    title: '采集任务总数',
    icon: 'tasks',
    value: '68',
    trend: 'up',
    trendValue: '较上月增加8个',
    type: 'info',
  },
  {
    title: '进行中任务',
    icon: 'play-circle',
    value: '24',
    trend: 'up',
    trendValue: '较上月增加3个',
    type: 'success',
  },
  {
    title: '待执行任务',
    icon: 'hourglass-start',
    value: '15',
    trend: 'up',
    trendValue: '较上月增加2个',
    type: 'warning',
  },
  {
    title: '已完成任务',
    icon: 'check-circle',
    value: '29',
    trend: 'up',
    trendValue: '较上月增加3个',
    type: 'danger',
  },
])

const selectedRowKeys = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 5,
  total: 0,
  showTotal: (total) => `共 ${total} 条记录`,
  showSizeChanger: true,
  pageSizeOptions: ['5', '10', '20'],
  showLessItems: true,
})

// 筛选条件
const filters = reactive({
  status: '',
  domain: '',
  createTime: '',
  creator: '',
})

// 生命周期钩子
onMounted(async () => {
  // 初始化数据
  pagination.total = tableData.value.length
  // 可以添加API调用来获取真实数据
})

// 切换高级筛选
function toggleAdvancedFilter() {
  showAdvancedFilter.value = !showAdvancedFilter.value
}

// 重置筛选条件
function resetFilters() {
  searchQuery.value = ''
  filters.status = ''
  filters.domain = ''
  filters.createTime = ''
  filters.creator = ''
}

// 应用筛选条件
function applyFilters() {
  pagination.current = 1
  // 在实际应用中，这里会调用API进行筛选
  message.success('筛选条件已应用')
}

// 处理搜索
function handleSearch() {
  pagination.current = 1
  // 在实际应用中，这里会调用API进行搜索
  message.success('搜索查询已应用')
}

// 表格选择变化
function onSelectChange(selectedKeys) {
  selectedRowKeys.value = selectedKeys
  selectAll.value = selectedKeys.length > 0 && selectedKeys.length === tableData.value.length
}

// 表格分页、排序、筛选变化
function handleTableChange(pag) {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  // 这里应该调用后端API获取对应页码的数据
  message.info(`页码: ${pag.current}, 每页条数: ${pag.pageSize}`)
}

// 删除任务
function deleteTaskItem(record) {
  message.success(`删除任务: ${record.name}`)
  const index = tableData.value.findIndex((item) => item.id === record.id)
  if (index !== -1) {
    tableData.value.splice(index, 1)
  }
}

// 打开创建任务模态窗口
function openCreateTaskModal() {
  message.info('打开创建任务窗口')
}

// 打开编辑任务模态窗口
function openEditTaskModal(record) {
  message.info(`编辑任务: ${record.name}`)
}

// 查看任务详情
function viewTaskDetail(record) {
  message.info(`查看任务详情: ${record.name}`)
}

// 启动任务
function startTask(record) {
  message.success(`任务 ${record.name} 已启动`)
  const task = tableData.value.find((item) => item.id === record.id)
  if (task) {
    task.status = '进行中'
    task.statusClass = 'running'
    task.progressStatus = 'active'
  }
}

// 暂停任务
function pauseTask(record) {
  message.success(`任务 ${record.name} 已暂停`)
  const task = tableData.value.find((item) => item.id === record.id)
  if (task) {
    task.status = '已暂停'
    task.statusClass = 'paused'
    task.progressStatus = 'exception'
  }
}

// 下载数据
function downloadData(record) {
  message.success(`正在下载 ${record.name} 的数据`)
}
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.data-collection-container {
  width: 100%;
  background-color: transparent;
  color: #e0e6f0;
  padding: @page-padding;

  // 覆盖 Ant Design 的默认样式
  :deep(.ant-table) {
    background-color: transparent;
    color: #e0e6f0;
    border-radius: 0;
  }

  :deep(.ant-table-container) {
    border-radius: 0;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: rgba(255, 255, 255, 0.02);
    color: #07f6ff;
    border-bottom: 1px solid rgba(25, 164, 255, 0.5);
    padding: 0.8vw @spacing-md-vw;
    font-size: @font-size-md-vw;
    border-radius: 0 !important;
  }

  :deep(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid rgba(25, 164, 255, 0.5);
    color: #e0e6f0;
    background-color: transparent;
    padding: 0.8vw @spacing-md-vw;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: rgba(255, 255, 255, 0.05);
  }

  :deep(.ant-progress) {
    .ant-progress-bg {
      background-color: #00c48c;
    }
  }

  :deep(.ant-progress-status-exception) {
    .ant-progress-bg {
      background-color: #f25767;
    }
  }

  :deep(.ant-checkbox-inner) {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 164, 255, 0.5);
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: #0096ff;
    border-color: #0096ff;
  }
}

// 统计卡片
.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: @card-margin-bottom-vw;
  margin-bottom: @card-margin-bottom-vw;
}

.stat-card {
  background-color: rgba(25, 164, 255, 0.5);
  border-radius: @card-border-radius-vw;
  padding: @card-padding-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-@spacing-xs-vw);
  box-shadow: 0 @spacing-sm-vw @spacing-lg-vw rgba(0, 0, 0, 0.15);
  border-color: #0096ff;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: @spacing-xs-vw;
  height: 100%;
  background-color: #0096ff;
  border-radius: @spacing-xs-vw 0 0 @spacing-xs-vw;
}

.stat-card.success::before {
  background-color: #00c48c;
}

.stat-card.warning::before {
  background-color: #ffb946;
}

.stat-card.danger::before {
  background-color: #f25767;
}

.stat-card.info::before {
  background-color: #0095ff;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @spacing-base-vw;
}

.stat-title {
  font-size: @font-size-md-vw;
  color: #07f6ff;
  font-weight: 500;
}

.stat-icon {
  width: 2vw;
  height: 2vw;
  border-radius: @spacing-sm-vw;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: @font-size-md-vw;
  background-color: rgba(0, 149, 255, 0.1);
  color: #0096ff;
}

.stat-card.success .stat-icon {
  background-color: rgba(0, 196, 140, 0.1);
  color: #00c48c;
}

.stat-card.warning .stat-icon {
  background-color: rgba(255, 185, 70, 0.1);
  color: #ffb946;
}

.stat-card.danger .stat-icon {
  background-color: rgba(242, 87, 103, 0.1);
  color: #f25767;
}

.stat-value {
  font-size: @font-size-lg-vw;
  font-weight: 600;
  margin-bottom: @spacing-xs-vw;
}

.stat-description {
  font-size: @font-size-sm-vw;
  color: #2affac;
  display: flex;
  align-items: center;
  gap: @spacing-xs-vw;
}

.stat-trend-up {
  color: #00c48c;
}

.stat-trend-down {
  color: #f25767;
}

// 搜索和筛选区域
.filter-section {
  padding: @card-padding-vw;
  margin-bottom: @card-margin-bottom-vw;
  border: 1px solid rgba(25, 164, 255, 0.5);
}

.search-row {
  display: flex;
  align-items: center;
  gap: @spacing-md-vw;
  margin-bottom: @spacing-md-vw;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
  padding: @spacing-sm-vw 2vw @spacing-sm-vw 2.5vw;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(25, 164, 255, 0.5);
  color: #e0e6f0;
  font-size: @font-size-md-vw;
  height: 2.2vw;
  transition: all 0.3s ease;

  &::placeholder {
    color: rgba(224, 230, 240, 0.7);
  }
}

.search-icon {
  position: absolute;
  left: 0.8vw;
  top: 50%;
  transform: translateY(-50%);
  color: #07f6ff;
  z-index: 1;
}

.advanced-filter-toggle {
  white-space: nowrap;
  color: #07f6ff;
  background: none;
  border: none;
  cursor: pointer;
  font-size: @font-size-md-vw;
  display: flex;
  align-items: center;
  gap: @spacing-xs-vw;
  padding: 0 @spacing-sm-vw;
  height: 2.2vw;
}

.advanced-filter-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: @spacing-md-vw;
  margin-top: @spacing-md-vw;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: @spacing-sm-vw;
}

.filter-label {
  font-size: @font-size-md-vw;
  color: #07f6ff;
}

:deep(.filter-select) {
  .ant-select-selector {
    height: 2.2vw !important;
    line-height: 2.2vw;
    padding: 0 @spacing-sm-vw !important;
    font-size: @font-size-md-vw;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(25, 164, 255, 0.5) !important;
  }

  .ant-select-selection-item {
    display: flex;
    align-items: center;
    height: 100%;
    color: #e0e6f0;
    font-size: @font-size-md-vw;
    line-height: 2.2vw;
  }

  .ant-select-selection-placeholder {
    color: rgba(224, 230, 240, 0.7);
    line-height: 2.2vw;
    display: flex;
    align-items: center;
  }

  .ant-select-arrow {
    color: #07f6ff;
  }
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: @spacing-md-vw;

  .action-btn {
    margin-left: @spacing-md-vw;
    // 其余样式已移至全局 .tech-action-btn() 混入
  }
}

// 任务列表表格
.tasks-section {
  margin-bottom: @card-margin-bottom-vw;
}

.table-responsive {
  overflow: hidden;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: @spacing-md-vw @card-padding-vw;
  border-bottom: 1px solid rgba(25, 164, 255, 0.5);
  background: rgba(25, 164, 255, 0.3);
}

.table-title {
  font-size: @font-size-lg-vw;
  font-family: YouSheBiaoTiHei, sans-serif;
  text-shadow: 1px 0.104167vw 0.208333vw rgba(0, 0, 0, 0.5);
}

.table-actions {
  display: flex;
  gap: @spacing-sm-vw;
}

.bulk-actions {
  display: flex;
  gap: @spacing-sm-vw;
}

// 使用全局样式混入
.bulk-action-btn {
  // 样式已移至全局 .tech-bulk-action-btn() 混入
}

.task-name {
  font-size: @font-size-md-vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #2affac;
}

.task-desc {
  font-size: @font-size-xs-vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 0.5vw;
}

.task-status-badge {
  display: inline-block;
  padding: @spacing-xs-vw @spacing-sm-vw;
  border-radius: @border-radius-xs-vw;
  font-size: @font-size-sm-vw;
  font-weight: 500;
}

.status-running {
  background-color: rgba(0, 196, 140, 0.1);
  color: #00c48c;
}

.status-completed {
  background-color: rgba(0, 149, 255, 0.1);
  color: #0095ff;
}

.status-pending {
  background-color: rgba(255, 185, 70, 0.1);
  color: #ffb946;
}

.status-failed,
.status-paused {
  background-color: rgba(242, 87, 103, 0.1);
  color: #f25767;
}

.task-actions-cell {
  white-space: nowrap;
}

.task-action {
  .tech-icon-action-btn();

  &.delete:hover {
    color: #f25767;
  }

  &.start:hover {
    color: #00c48c;
  }

  &.pause:hover {
    color: #ffb946;
  }

  &.download:hover {
    color: #0095ff;
  }
}
</style>
