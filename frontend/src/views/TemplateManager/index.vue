<template>
  <div class="template-manager-container">
    <!-- 页面标题和操作按钮 -->
    <!-- <div class="page-header">
      <div class="template-actions">
        <a-button class="action-btn" @click="openImportExportModal()">
          <template #icon><i class="fas fa-file-import"></i></template>
          导入模版
        </a-button>
        <a-button class="action-btn" @click="openImportExportModal('export')">
          <template #icon><i class="fas fa-file-export"></i></template>
          导出模版
        </a-button>
        <a-button type="primary" class="action-btn primary" @click="openCreateTemplateModal()">
          <template #icon><i class="fas fa-plus"></i></template>
          新建模版
        </a-button>
      </div>
    </div> -->

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div v-for="(stat, index) in statsList" :key="index" :class="['stat-card', stat.type]">
        <div class="stat-header">
          <div class="stat-title">{{ stat.title }}</div>
          <div class="stat-icon">
            <i :class="['fas', `fa-${stat.icon}`]"></i>
          </div>
        </div>
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-description">
          <i :class="['fas', `fa-arrow-${stat.trend}`, `stat-trend-${stat.trend}`]"></i>
          <span>{{ stat.trendValue }}</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <div class="search-row">
        <div class="search-input-wrapper">
          <i class="fas fa-search search-icon"></i>
          <a-input
            v-model:value="searchQuery"
            class="search-input"
            placeholder="搜索模版名称、ID、创建人..."
            @change="handleSearch"
          />
        </div>
        <a-button class="advanced-filter-toggle" type="link" @click="toggleAdvancedFilter">
          <i :class="showAdvancedFilter ? 'fas fa-chevron-up' : 'fas fa-sliders-h'"></i>
          {{ showAdvancedFilter ? '收起筛选' : '高级筛选' }}
        </a-button>
      </div>
      <div class="advanced-filter-row" v-show="showAdvancedFilter">
        <div class="filter-group">
          <label class="filter-label">模版状态</label>
          <a-select v-model:value="filters.status" class="filter-select" style="width: 100%">
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="active">已发布</a-select-option>
            <a-select-option value="draft">草稿</a-select-option>
            <a-select-option value="review">审核中</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">所属领域</label>
          <a-select v-model:value="filters.domain" class="filter-select" style="width: 100%">
            <a-select-option value="">全部领域</a-select-option>
            <a-select-option value="power">电力系统</a-select-option>
            <a-select-option value="transportation">交通系统</a-select-option>
            <a-select-option value="water">水利系统</a-select-option>
            <a-select-option value="gas">燃气系统</a-select-option>
            <a-select-option value="communication">通信系统</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">创建时间</label>
          <a-select v-model:value="filters.createTime" class="filter-select" style="width: 100%">
            <a-select-option value="">全部时间</a-select-option>
            <a-select-option value="today">今天</a-select-option>
            <a-select-option value="yesterday">昨天</a-select-option>
            <a-select-option value="last7days">最近7天</a-select-option>
            <a-select-option value="last30days">最近30天</a-select-option>
            <a-select-option value="custom">自定义范围</a-select-option>
          </a-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">创建人</label>
          <a-select v-model:value="filters.creator" class="filter-select" style="width: 100%">
            <a-select-option value="">全部创建人</a-select-option>
            <a-select-option value="self">我创建的</a-select-option>
            <a-select-option value="team">我团队的</a-select-option>
          </a-select>
        </div>
      </div>
      <div class="filter-actions" v-show="showAdvancedFilter">
        <a-button class="action-btn" @click="resetFilters">重置筛选</a-button>
        <a-button type="primary" class="action-btn primary" @click="applyFilters"
          >应用筛选</a-button
        >
      </div>
    </div>

    <!-- 模版列表 -->
    <div class="templates-section">
      <div class="table-responsive">
        <div class="table-header">
          <div class="table-title">全部模版</div>
          <div class="table-actions">
            <div class="bulk-actions">
              <a-button class="bulk-action-btn" @click="openCreateTemplateModal()">
                <template #icon><i class="fas fa-plus"></i></template>
                新建模版
              </a-button>
              <a-button class="bulk-action-btn">
                <template #icon><i class="fas fa-archive"></i></template>
                导入模版
              </a-button>
              <a-button class="bulk-action-btn">
                <template #icon><i class="fas fa-download"></i></template>
                导出模版
              </a-button>
              <a-button class="bulk-action-btn delete" danger>
                <template #icon><i class="fas fa-trash-alt"></i></template>
                删除模版
              </a-button>
            </div>
          </div>
        </div>
        <!-- Ant Design 表格 -->
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
          :row-key="(record) => record.id"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }"
        >
          <!-- 模版名称列 -->
          <template #bodyCell="{ column, record }">
            <!-- 模版名称列 -->
            <template v-if="column.key === 'name'">
              <div>
                <!-- <a-button
                  class="favorite-action"
                  :class="{ active: record.favorite }"
                  type="text"
                  @click="toggleFavorite(record)"
                >
                  <i class="fas fa-star star-icon" :class="{ 'star-active': record.favorite }"></i>
                </a-button> -->
                <span class="template-name">{{ record.name }}</span>
                <div class="template-desc">{{ record.description }}</div>
              </div>
            </template>

            <!-- 状态列 -->
            <template v-else-if="column.key === 'status'">
              <span :class="['template-status-badge', `status-${record.statusClass}`]">
                {{ record.status }}
              </span>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <div class="template-actions-cell">
                <a-button class="template-action view" title="查看" type="text">
                  <i class="fas fa-eye"></i>
                </a-button>
                <a-button
                  class="template-action edit"
                  title="编辑"
                  type="text"
                  @click="openEditTemplateModal(record)"
                >
                  <i class="fas fa-edit"></i>
                </a-button>
                <a-button class="template-action copy" title="复制" type="text">
                  <i class="fas fa-copy"></i>
                </a-button>
                <a-button
                  class="template-action compare"
                  title="版本对比"
                  type="text"
                  @click="openCompareModal(record)"
                >
                  <i class="fas fa-code-compare"></i>
                </a-button>
                <a-popconfirm
                  title="确定要删除该模版吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="deleteTemplateItem(record)"
                >
                  <a-button class="template-action delete" title="删除" type="text">
                    <i class="fas fa-trash-alt"></i>
                  </a-button>
                </a-popconfirm>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 最近模版活动卡片 -->
    <div class="recent-activity-card">
      <div class="card-header">
        <div class="card-title">模版活动日志</div>
        <div class="card-actions">
          <a-button class="card-action" type="text" @click="fetchActivities">
            <i class="fas fa-sync-alt"></i>
          </a-button>
          <a-button class="card-action" type="text">
            <i class="fas fa-ellipsis-v"></i>
          </a-button>
        </div>
      </div>
      <div class="card-body">
        <div class="activity-timeline">
          <a-timeline>
            <a-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :color="getTimelineItemColor(activity.type)"
            >
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </div>

    <!-- 模态窗口将在实际实现时添加 -->
  </div>

  <!-- 新建模版弹窗 -->
  <a-modal
    v-model:visible="createModalVisible"
    title="新建仿真度测评模版"
    width="95%"
    :footer="null"
    :maskClosable="false"
    :destroyOnClose="true"
    :style="{
      backgroundColor: 'transparent',
      top: '2.5vh',
    }"
    :wrapClassName="'custom-modal-wrap'"
    :class="'custom-modal'"
    :rootClassName="'modal-root-custom'"
  >
    <template #header>
      <div class="custom-modal-header">
        <span>新建仿真度测评模版</span>
      </div>
    </template>
    <create-template ref="createTemplateRef" />
    <div class="modal-footer">
      <a-button class="modal-btn" @click="closeCreateModal">取消</a-button>
      <a-button class="modal-btn" @click="saveAsDraft">保存为草稿</a-button>
      <a-button type="primary" class="modal-btn primary" @click="saveAndPublish"
        >保存并发布</a-button
      >
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { message, Timeline } from 'ant-design-vue'
import {
  getTemplateList,
  getTemplateStats,
  getTemplateActivities,
  toggleTemplateFavorite,
  deleteTemplate,
} from '@/api/modules/templateManager'
import CreateTemplate from '@/components/template/CreateTemplate.vue'

// 定义组件名称
defineOptions({
  name: 'TemplateManagerView',
})

// 定义表格列
const columns = [
  {
    title: '模版名称',
    dataIndex: 'name',
    key: 'name',
    width: '30%',
    ellipsis: true,
  },
  // {
  //   title: 'ID',
  //   dataIndex: 'id',
  //   key: 'id',
  //   width: '12%',
  //   customRender: ({ text }) => {
  //     return h('span', { class: 'template-id' }, text)
  //   },
  // },
  {
    title: '领域',
    dataIndex: 'domain',
    key: 'domain',
    width: '10%',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '12%',
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: '10%',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: '13%',
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount',
    width: '8%',
  },
  {
    title: '操作',
    key: 'action',
    width: '18%',
  },
]

// 组件状态
const searchQuery = ref('')
const showAdvancedFilter = ref(false)
const selectAll = ref(false)
const loading = ref(false)
const tableData = ref([])
const statsList = ref([])
const activities = ref([])
const selectedRowKeys = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 5,
  total: 0,
  showTotal: (total) => `共 ${total} 条记录`,
  showSizeChanger: true,
  pageSizeOptions: ['5', '10', '20'],
  // showQuickJumper: true,
  showLessItems: true,
})

// 筛选条件
const filters = reactive({
  status: '',
  domain: '',
  createTime: '',
  creator: '',
})

// 新建模版弹窗相关
const createModalVisible = ref(false)
const createTemplateRef = ref(null)

// 生命周期钩子
onMounted(async () => {
  // 确保数据加载顺序
  await fetchTemplates()
  await fetchStats()
  await fetchActivities()
})

// 获取模版列表
async function fetchTemplates() {
  loading.value = true
  try {
    const params = {
      pageSize: pagination.pageSize,
      pageNum: pagination.current,
      ...filters,
    }

    if (searchQuery.value) {
      params.keyword = searchQuery.value
    }

    const res = await getTemplateList(params)
    console.log('获取模版列表返回:', res)
    // 判断是否有data包装层
    if (res.data) {
      tableData.value = res.data.list
      pagination.total = res.data.total
    } else {
      // 直接使用返回数据
      tableData.value = res.list
      pagination.total = res.total
    }
  } catch (error) {
    console.error('获取模版列表失败:', error)
    message.error('获取模版列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
async function fetchStats() {
  try {
    const res = await getTemplateStats()
    console.log('获取统计数据返回:', res)
    // 判断是否有data包装层
    statsList.value = res.data || res
  } catch (error) {
    console.error('获取统计数据失败:', error)
    message.error('获取统计数据失败')
  }
}

// 获取活动日志
async function fetchActivities() {
  try {
    const res = await getTemplateActivities()
    console.log('获取活动日志返回:', res)
    // 判断是否有data包装层
    activities.value = res.data || res
  } catch (error) {
    console.error('获取活动日志失败:', error)
    message.error('获取活动日志失败')
  }
}

// 切换高级筛选
function toggleAdvancedFilter() {
  showAdvancedFilter.value = !showAdvancedFilter.value
}

// 重置筛选条件
function resetFilters() {
  searchQuery.value = ''
  filters.status = ''
  filters.domain = ''
  filters.createTime = ''
  filters.creator = ''
}

// 应用筛选条件
function applyFilters() {
  pagination.current = 1
  fetchTemplates()
}

// 处理搜索
function handleSearch() {
  pagination.current = 1
  fetchTemplates()
}

// 切换全选
function toggleSelectAll() {
  if (selectAll.value) {
    selectedRowKeys.value = tableData.value.map((item) => item.id)
  } else {
    selectedRowKeys.value = []
  }
}

// 表格选择变化
function onSelectChange(selectedKeys) {
  selectedRowKeys.value = selectedKeys
  selectAll.value = selectedKeys.length > 0 && selectedKeys.length === tableData.value.length
}

// 表格分页、排序、筛选变化
function handleTableChange(pag) {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchTemplates()
}

// 切换收藏状态
async function toggleFavorite(record) {
  try {
    const newStatus = !record.favorite
    await toggleTemplateFavorite(record.id, newStatus)
    record.favorite = newStatus
    message.success(newStatus ? '收藏成功' : '取消收藏成功')
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败')
  }
}

// 删除模版
async function deleteTemplateItem(record) {
  try {
    await deleteTemplate(record.id)
    message.success('删除成功')
    fetchTemplates()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

// 打开创建模版模态窗口
function openCreateTemplateModal() {
  console.log('打开创建模版窗口')
  createModalVisible.value = true
}

// 关闭创建模版模态窗口
function closeCreateModal() {
  createModalVisible.value = false
}

// 保存为草稿
function saveAsDraft() {
  if (createTemplateRef.value) {
    const formData = createTemplateRef.value.formData
    console.log('保存为草稿', formData)
    message.success('模版已保存为草稿')
    closeCreateModal()
  }
}

// 保存并发布
function saveAndPublish() {
  if (createTemplateRef.value) {
    const formData = createTemplateRef.value.formData
    console.log('保存并发布', formData)
    message.success('模版已保存并发布')
    closeCreateModal()
  }
}

// 打开编辑模版模态窗口
function openEditTemplateModal(record) {
  console.log('编辑模版:', record.name)
}

// 打开版本对比模态窗口
function openCompareModal(record) {
  console.log('版本对比:', record.name)
}

// 打开导入/导出模态窗口
function openImportExportModal(tab = 'import') {
  console.log('打开导入/导出窗口', tab)
}

// 获取时间线项目颜色
function getTimelineItemColor(type) {
  switch (type) {
    case 'success':
      return '#00c48c'
    case 'warning':
      return '#ffb946'
    case 'danger':
      return '#f25767'
    default:
      return '#0096ff'
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.template-manager-container {
  width: 100%;
  background-color: transparent;
  color: #e0e6f0;
  padding: @page-padding;

  // 覆盖 Ant Design 的默认样式
  :deep(.ant-table) {
    background-color: transparent;
    color: #e0e6f0;
    border-radius: 0;
  }

  :deep(.ant-table-container) {
    border-radius: 0;
  }

  :deep(.ant-table-thead) {
    // background-color: rgba(25, 164, 255, .5);
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: rgba(255, 255, 255, 0.02);
    color: #07f6ff;
    border-bottom: 1px solid rgba(25, 164, 255, 0.5);
    padding: 0.8vw @spacing-md-vw;
    font-size: @font-size-md-vw;
    border-radius: 0 !important;
  }

  :deep(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid rgba(25, 164, 255, 0.5);
    color: #e0e6f0;
    background-color: transparent;
    padding: 0.8vw @spacing-md-vw;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: rgba(255, 255, 255, 0.05);
  }

  :deep(.ant-pagination-item) {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 164, 255, 0.5);
    a {
      color: #e0e6f0;
    }
  }

  :deep(.ant-pagination-item-active) {
    background-color: #0096ff;
    border-color: #0096ff;
    a {
      color: white;
    }
  }

  // :deep(.ant-select-dropdown) {
  //   background-color: #0f1520;
  //   color: #e0e6f0;
  //   border: 1px solid rgba(25, 164, 255, 0.5);

  //   .ant-select-item {
  //     color: #e0e6f0;
  //   }

  //   .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  //     background-color: rgba(0, 150, 255, 0.1);
  //     color: #0096ff;
  //   }

  //   .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  //     background-color: rgba(255, 255, 255, 0.05);
  //   }
  // }

  :deep(.ant-pagination-options-quick-jumper input),
  :deep(.ant-select-selector) {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 164, 255, 0.5);
    color: #e0e6f0;
  }

  :deep(.ant-checkbox-inner) {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 164, 255, 0.5);
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: #0096ff;
    border-color: #0096ff;
  }

  // 按钮覆盖样式
  :deep(.ant-btn) {
    &.action-btn {
      // background-color: rgba(25, 164, 255, 0.5);
      // border: 1px solid rgba(25, 164, 255, 0.5);
      color: #e0e6f0;
      text-shadow: 1px 0.104167vw 0.208333vw rgba(0, 0, 0, 0.5);
      font-size: @font-size-md-vw;
    }
  }

  :deep(.ant-table-wrapper) {
    border-radius: 0;
  }

  :deep(.ant-table-content) {
    border-radius: 0;
  }

  :deep(.ant-table-tbody > tr:first-child > td) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  :deep(.ant-table-tbody > tr:last-child > td) {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}

// 页面标题和操作按钮
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: @card-margin-bottom-vw;
}

.template-actions {
  display: flex;
  gap: @spacing-md-vw;
}

// 统计卡片
.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: @card-margin-bottom-vw;
  margin-bottom: @card-margin-bottom-vw;
}

.stat-card {
  background-color: rgba(25, 164, 255, 0.5);
  border-radius: @card-border-radius-vw;
  padding: @card-padding-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-@spacing-xs-vw);
  box-shadow: 0 @spacing-sm-vw @spacing-lg-vw rgba(0, 0, 0, 0.15);
  border-color: #0096ff;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: @spacing-xs-vw;
  height: 100%;
  background-color: #0096ff;
  border-radius: @spacing-xs-vw 0 0 @spacing-xs-vw;
}

.stat-card.success::before {
  background-color: #00c48c;
}

.stat-card.warning::before {
  background-color: #ffb946;
}

.stat-card.danger::before {
  background-color: #f25767;
}

.stat-card.info::before {
  background-color: #0095ff;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @spacing-base-vw;
}

.stat-title {
  font-size: @font-size-md-vw;
  color: #07f6ff;
  font-weight: 500;
}

.stat-icon {
  width: 2vw;
  height: 2vw;
  border-radius: @spacing-sm-vw;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: @font-size-md-vw;
  background-color: rgba(0, 149, 255, 0.1);
  color: #0096ff;
}

.stat-card.success .stat-icon {
  background-color: rgba(0, 196, 140, 0.1);
  color: #00c48c;
}

.stat-card.warning .stat-icon {
  background-color: rgba(255, 185, 70, 0.1);
  color: #ffb946;
}

.stat-card.danger .stat-icon {
  background-color: rgba(242, 87, 103, 0.1);
  color: #f25767;
}

.stat-value {
  font-size: @font-size-lg-vw;
  font-weight: 600;
  margin-bottom: @spacing-xs-vw;
}

.stat-description {
  font-size: @font-size-sm-vw;
  color: #2affac;
  display: flex;
  align-items: center;
  gap: @spacing-xs-vw;
}

.stat-trend-up {
  color: #00c48c;
}

.stat-trend-down {
  color: #f25767;
}

// 搜索和筛选区域
.filter-section {
  // border-radius: @card-border-radius-vw;
  padding: @card-padding-vw;
  margin-bottom: @card-margin-bottom-vw;
  border: 1px solid rgba(25, 164, 255, 0.5);
}

.search-row {
  display: flex;
  align-items: center;
  gap: @spacing-md-vw;
  margin-bottom: @spacing-md-vw;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
  padding: @spacing-sm-vw 2vw @spacing-sm-vw 2.5vw;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(25, 164, 255, 0.5);
  // border-radius: @border-radius-md-vw;
  color: #e0e6f0;
  font-size: @font-size-md-vw;
  height: 2.2vw;
  transition: all 0.3s ease;

  &::placeholder {
    color: rgba(224, 230, 240, 0.7);
  }
}

.search-icon {
  position: absolute;
  left: 0.8vw;
  top: 50%;
  transform: translateY(-50%);
  color: #07f6ff;
  z-index: 1;
}

.advanced-filter-toggle {
  white-space: nowrap;
  color: #07f6ff;
  background: none;
  border: none;
  cursor: pointer;
  font-size: @font-size-md-vw;
  display: flex;
  align-items: center;
  gap: @spacing-xs-vw;
  padding: 0 @spacing-sm-vw;
  height: 2.2vw;
}

.advanced-filter-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: @spacing-md-vw;
  margin-top: @spacing-md-vw;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: @spacing-sm-vw;
}

.filter-label {
  font-size: @font-size-md-vw;
  color: #07f6ff;
}

:deep(.filter-select) {
  .ant-select-selector {
    height: 2.2vw !important;
    line-height: 2.2vw;
    padding: 0 @spacing-sm-vw !important;
    font-size: @font-size-md-vw;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(25, 164, 255, 0.5) !important;
  }

  .ant-select-selection-item {
    display: flex;
    align-items: center;
    height: 100%;
    color: #e0e6f0;
    font-size: @font-size-md-vw;
    line-height: 2.2vw;
  }

  .ant-select-selection-placeholder {
    color: rgba(224, 230, 240, 0.7);
    line-height: 2.2vw;
    display: flex;
    align-items: center;
  }

  .ant-select-arrow {
    color: #07f6ff;
  }
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: @spacing-md-vw;

  .action-btn {
    margin-left: @spacing-md-vw;
    // 其余样式已移至全局 .tech-action-btn() 混入
  }
}

// 模版列表表格
.templates-section {
  margin-bottom: @card-margin-bottom-vw;
}

.table-responsive {
  // border-radius: @card-border-radius-vw;
  overflow: hidden;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: @spacing-md-vw @card-padding-vw;
  border-bottom: 1px solid rgba(25, 164, 255, 0.5);
  background: rgba(25, 164, 255, 0.3);
}

.table-title {
  font-size: @font-size-lg-vw;
  font-family: YouSheBiaoTiHei, sans-serif;
  text-shadow: 1px 0.104167vw 0.208333vw rgba(0, 0, 0, 0.5);
}

.table-actions {
  display: flex;
  gap: @spacing-sm-vw;
}

.bulk-select {
  display: flex;
  align-items: center;
  gap: @spacing-sm-vw;
  margin-right: @spacing-md-vw;
}

.bulk-actions {
  display: flex;
  gap: @spacing-sm-vw;
}

// 按钮内文字样式已移至全局混入

// 使用全局样式混入
.bulk-action-btn {
  // 样式已移至全局 .tech-bulk-action-btn() 混入
}

.bulk-action-btn.primary {
  background-color: #0096ff;
  border-color: #0096ff;
  color: white;
  box-shadow: 0 @spacing-xs-vw @spacing-sm-vw rgba(0, 150, 255, 0.4);
  padding: @spacing-sm-vw 1.2vw;
  position: relative;
  overflow: hidden;
}

.bulk-action-btn.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.bulk-action-btn.primary:hover {
  background-color: #3e9bff;
  border-color: #3e9bff;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 150, 255, 0.6);
  transform: translateY(-@spacing-xs-vw);
}

.bulk-action-btn.primary:hover::before {
  left: 100%;
}

.bulk-action-btn.delete {
  color: #46fff4;
  border-color: rgba(242, 87, 103, 0.3);
}

.bulk-action-btn.delete:hover {
  background-color: rgba(242, 87, 103, 0.1);
  border-color: #f25767;
}

.template-id {
  font-family: monospace;
  color: #07f6ff;
}

.template-name {
  font-size: @font-size-md-vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #2affac;
}

.template-desc {
  font-size: @font-size-xs-vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 0.5vw;
}

.template-status-badge {
  display: inline-block;
  padding: @spacing-xs-vw @spacing-sm-vw;
  border-radius: @border-radius-xs-vw;
  font-size: @font-size-sm-vw;
  font-weight: 500;
}

.status-active {
  background-color: rgba(0, 196, 140, 0.1);
  color: #00c48c;
}

.status-draft {
  background-color: rgba(255, 185, 70, 0.1);
  color: #ffb946;
}

.status-review {
  background-color: rgba(0, 149, 255, 0.1);
  color: #0095ff;
}

.template-actions-cell {
  white-space: nowrap;
}

.template-action {
  .tech-icon-action-btn();

  &.delete:hover {
    color: #ff4d4f;
  }
}

.template-action:hover {
  background-color: rgba(62, 155, 255, 0.1);
  border-color: #0096ff;
  color: #0096ff;
}

.template-action.edit:hover {
  color: #0095ff;
  border-color: #0095ff;
  background-color: rgba(0, 149, 255, 0.1);
}

.template-action.copy:hover {
  color: #ffb946;
  border-color: #ffb946;
  background-color: rgba(255, 185, 70, 0.1);
}

.template-action.delete:hover {
  color: #f25767;
  border-color: #f25767;
  background-color: rgba(242, 87, 103, 0.1);
}

.favorite-action {
  color: #07f6ff;
  background: none;
  border: none;
  cursor: pointer;
  font-size: @font-size-md-vw;
  transition: all 0.2s ease;
}

.favorite-action:hover,
.favorite-action.active {
  color: #ffb946;
}

.star-icon {
  transition: all 0.3s ease;
}

.star-active {
  color: #ffb946;
}

// 最近活动卡片
.recent-activity-card {
  // border-radius: @card-border-radius-vw;
  box-shadow: 0 @shadow-offset-vw @shadow-blur-vw rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(25, 164, 255, 0.5);
  overflow: hidden;
}

.card-header {
  padding: @spacing-base-vw @card-padding-vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(25, 164, 255, 0.5);
  background: rgba(25, 164, 255, 0.3);
}

.card-title {
  font-size: @font-size-lg-vw;
  font-family: YouSheBiaoTiHei, sans-serif;
  text-shadow: 1px 0.104167vw 0.208333vw rgba(0, 0, 0, 0.5);
}

.card-actions {
  display: flex;
  gap: @spacing-sm-vw;
}

.card-action {
  background: none;
  border: none;
  color: #07f6ff;
  cursor: pointer;
  font-size: @font-size-md-vw;
  width: 2vw;
  height: 2vw;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: @border-radius-md-vw;
  transition: all 0.2s ease;
}

.card-action:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: #e0e6f0;
}

.card-body {
  padding: @card-padding-vw;
}

.activity-timeline {
  position: relative;
  // padding-left: @spacing-md-vw;
  max-height: 18.229167vw; // 350px
  overflow-y: auto;
}

:deep(.ant-timeline) {
  font-size: @font-size-md-vw;
  color: white;

  .ant-timeline-item {
    padding-bottom: @spacing-md-vw;
  }

  .ant-timeline-item-content {
    margin-left: @spacing-lg-vw;
    margin-bottom: 0;
  }
}

:deep(.ant-timeline-item-tail) {
  border-inline-start: 2px solid #2affac8c !important;
}

.activity-item {
  position: relative;
  padding-bottom: @card-padding-vw;
}

.activity-item:last-child {
  padding-bottom: 0;
}

.activity-dot {
  position: absolute;
  left: -@timeline-padding-left-vw;
  top: 0;
  width: @dot-size-vw;
  height: @dot-size-vw;
  border-radius: 50%;
  background-color: #0096ff;
  border: @spacing-xs-vw solid rgba(25, 164, 255, 0.5);
  z-index: 1;
}

.activity-item.success .activity-dot {
  background-color: #00c48c;
}

.activity-item.warning .activity-dot {
  background-color: #ffb946;
}

.activity-item.danger .activity-dot {
  background-color: #f25767;
}

.activity-content {
  padding-bottom: @spacing-sm-vw;
  border-bottom: 1px dashed rgba(25, 164, 255, 0.5);
}

:deep(.ant-timeline-item:last-child) .activity-content {
  border-bottom: none;
}

.activity-title {
  font-size: @font-size-md-vw;
  margin-bottom: @spacing-xs-vw;
}

.activity-time {
  font-size: @font-size-xs-vw;
  color: #07f6ff;
}
</style>
