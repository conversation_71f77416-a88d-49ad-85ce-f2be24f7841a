// 导入全局样式
import './styles/global.less'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// 导入Font Awesome
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { fas } from '@fortawesome/free-solid-svg-icons'
import { far } from '@fortawesome/free-regular-svg-icons'
import '@fortawesome/fontawesome-free/css/all.css'

import App from './App.vue'
import router from './router'

// 添加图标到库中
library.add(fas, far)

// 根据环境变量决定是否启用Mock服务
const useMock = import.meta.env.VITE_MOCK_ENABLED === 'true'
console.log('是否启用Mock数据服务:', useMock)

// 导出一个Promise，表示Mock服务是否已准备就绪
export const mockReadyPromise = useMock
  ? import('./mock/index')
      .then((mock) => {
        console.log('Mock数据服务已成功启用')
        // 测试Mock服务
        return import('./testMock')
          .then(() => {
            console.log('Mock测试脚本已加载')
            return true
          })
          .catch((err) => {
            console.error('Mock测试脚本加载失败', err)
            return true // 即使测试脚本加载失败，Mock服务仍然可用
          })
      })
      .catch((err) => {
        console.error('Mock数据服务加载失败', err)
        return false
      })
  : Promise.resolve(false)

const app = createApp(App)
const pinia = createPinia()

// 注册Font Awesome组件
app.component('font-awesome-icon', FontAwesomeIcon)

app.use(pinia)
app.use(router)
app.use(Antd)

app.mount('#app')
