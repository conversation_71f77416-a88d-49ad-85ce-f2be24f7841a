// 测试Mock服务是否正常工作
import axios from 'axios'

// 先导入mock，确保mock服务已初始化
console.log('开始初始化Mock服务...')
import './mock/index'

console.log('开始测试Mock服务...')

// 测试获取模版列表
async function testGetTemplateList() {
  try {
    console.log('测试模版列表接口...')
    const response = await axios({
      method: 'get',
      url: '/api/template/list',
      params: { pageSize: 7, pageNum: 1 },
    })
    console.log('获取模版列表响应:', response.data)
    return response.data
  } catch (error) {
    console.error('测试获取模版列表失败:', error)
    return null
  }
}

// 测试获取统计数据
async function testGetStats() {
  try {
    console.log('测试统计数据接口...')
    const response = await axios.get('/api/template/stats')
    console.log('获取统计数据响应:', response.data)
    return response.data
  } catch (error) {
    console.error('测试获取统计数据失败:', error)
    return null
  }
}

// 测试获取活动日志
async function testGetActivities() {
  try {
    console.log('测试活动日志接口...')
    const response = await axios.get('/api/template/activities')
    console.log('获取活动日志响应:', response.data)
    return response.data
  } catch (error) {
    console.error('测试获取活动日志失败:', error)
    return null
  }
}

// 执行测试
async function runTests() {
  const results = {
    templateList: await testGetTemplateList(),
    stats: await testGetStats(),
    activities: await testGetActivities(),
  }
  console.log('Mock服务测试完成，结果汇总:', results)
  return results
}

// 导出测试函数
export { runTests, testGetTemplateList, testGetStats, testGetActivities }

// 延迟自动执行测试，确保Mock服务已完全初始化
setTimeout(() => {
  console.log('开始执行Mock服务测试...')
  runTests().catch((err) => console.error('测试执行出错:', err))
}, 1000)
