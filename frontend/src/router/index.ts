import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@/views/Dashboard/index.vue'),
      meta: {
        title: '仪表盘'
      }
    },
    {
      path: '/template-manager',
      name: 'templateManager',
      component: () => import('@/views/TemplateManager/index.vue'),
      meta: {
        title: '模版管理'
      }
    },
    {
      path: '/algorithm-library',
      name: 'algorithmLibrary',
      component: () => import('@/views/AlgorithmLibrary/index.vue'),
      meta: {
        title: '算法库'
      }
    },
    {
      path: '/data-collection',
      name: 'dataCollection',
      component: () => import('@/views/DataCollection/index.vue'),
      meta: {
        title: '数据采集'
      }
    },
    {
      path: '/evaluation-task',
      name: 'evaluationTask',
      component: () => import('@/views/EvaluationTask/index.vue'),
      meta: {
        title: '评估任务'
      }
    },
    {
      path: '/evaluation-detail/:id',
      name: 'evaluationDetail',
      component: () => import('@/components/evaluation/EvaluationDetail.vue'),
      meta: {
        hideSidebar: true,
        title: '评估详情'
      }
    },
    // 简化布局页面 (无侧边栏)
    {
      path: '/template-detail/:id',
      name: 'templateDetail',
      component: () => import('@/components/template/CreateTemplate.vue'),
      meta: {
        hideSidebar: true,
        title: '模版详情'
      }
    }
  ]
})

export default router
