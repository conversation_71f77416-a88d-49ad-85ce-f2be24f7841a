<template>
  <div style="padding: 20px; background: #0F1520; color: white; min-height: 100vh;">
    <h2>滚动条测试</h2>
    
    <div class="test-container">
      <h3>测试容器 (高度: 200px)</h3>
      <div class="scroll-test">
        <a-timeline>
          <a-timeline-item
            v-for="(activity, index) in testActivities"
            :key="index"
            :color="getActivityColor(activity.type)"
          >
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const testActivities = ref([
  {
    title: '您创建了新的评估任务"供水系统压力仿真评估-2025年Q1"',
    time: '今天 09:32',
    type: 'success'
  },
  {
    title: '小刘更新了"交通流量模拟"算法参数',
    time: '昨天 14:23',
    type: 'warning'
  },
  {
    title: '您审核通过了"燃气管网压力监测"数据采集任务',
    time: '04-02 11:05',
    type: 'success'
  },
  {
    title: '系统管理员发布了新版测评模版',
    time: '04-01 08:30',
    type: 'info'
  },
  {
    title: '数据采集任务"城市交通网络拓扑"执行失败',
    time: '03-31 16:45',
    type: 'danger'
  },
  {
    title: '张工完成了"电力网络仿真"模型验证',
    time: '03-30 13:20',
    type: 'success'
  },
  {
    title: '系统自动备份数据库完成',
    time: '03-30 02:00',
    type: 'info'
  },
  {
    title: '李经理审批了"水务管网评估"项目申请',
    time: '03-29 16:30',
    type: 'success'
  },
  {
    title: '算法库更新：新增机器学习预测模型',
    time: '03-29 10:15',
    type: 'info'
  },
  {
    title: '数据采集服务器连接异常',
    time: '03-28 18:45',
    type: 'danger'
  }
])

function getActivityColor(type: string) {
  switch (type) {
    case 'success':
      return '#00c48c'
    case 'warning':
      return '#ffb946'
    case 'danger':
      return '#f25767'
    case 'info':
      return '#0096ff'
    default:
      return '#0096ff'
  }
}
</script>

<style lang="less" scoped>
.test-container {
  margin: 20px 0;
  border: 1px solid #333;
  padding: 20px;
}

.scroll-test {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #555;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 23, 51, 0.2);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(25, 164, 255, 0.6);
    border-radius: 3px;
    
    &:hover {
      background: rgba(25, 164, 255, 0.8);
    }
  }
}

:deep(.ant-timeline) {
  font-size: 14px;
  color: white;

  .ant-timeline-item {
    padding-bottom: 16px;
  }

  .ant-timeline-item-content {
    margin-left: 24px;
    margin-bottom: 0;
  }
}

:deep(.ant-timeline-item-tail) {
  border-inline-start: 2px solid rgba(25, 164, 255, 0.5) !important;
}

.activity-content {
  padding-bottom: 8px;
  border-bottom: 1px dashed rgba(25, 164, 255, 0.3);
}

:deep(.ant-timeline-item:last-child) .activity-content {
  border-bottom: none;
}

.activity-title {
  font-size: 14px;
  margin-bottom: 4px;
  color: #e0e6f0;
}

.activity-time {
  font-size: 12px;
  color: #a0a8b8;
}
</style>
