// templateManager.js - 模版管理相关接口
import request from '../request'

// 获取模版列表
export function getTemplateList(params) {
  return request({
    url: '/template/list',
    method: 'get',
    params,
  })
}

// 获取模版详情
export function getTemplateDetail(id) {
  return request({
    url: `/template/detail/${id}`,
    method: 'get',
  })
}

// 创建模版
export function createTemplate(data) {
  return request({
    url: '/template/create',
    method: 'post',
    data,
  })
}

// 更新模版
export function updateTemplate(data) {
  return request({
    url: '/template/update',
    method: 'put',
    data,
  })
}

// 删除模版
export function deleteTemplate(id) {
  return request({
    url: `/template/delete/${id}`,
    method: 'delete',
  })
}

// 获取模版活动日志
export function getTemplateActivities() {
  return request({
    url: '/template/activities',
    method: 'get',
  })
}

// 获取模版统计数据
export function getTemplateStats() {
  return request({
    url: '/template/stats',
    method: 'get',
  })
}

// 切换模版收藏状态
export function toggleTemplateFavorite(id, status) {
  return request({
    url: '/template/favorite',
    method: 'post',
    data: {
      id,
      isFavorite: status,
    },
  })
}
