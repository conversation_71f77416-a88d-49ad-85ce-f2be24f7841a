import axios from 'axios'
import { mockReadyPromise } from '@/main'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 600000,
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么，例如添加token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers['Authorization'] = `Bearer ${token}`;
    // }
    return config
  },
  (error) => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data
    console.log('响应数据:', res)

    // 判断是否为Mock数据，Mock数据已经包含了code/data/message结构
    if (res.code !== undefined) {
      if (res.code !== 200) {
        console.error('接口错误:', res.message || '未知错误')
        return Promise.reject(new Error(res.message || '未知错误'))
      } else {
        return res.data // 直接返回data字段内容
      }
    } else {
      // 直接返回数据（没有code/data/message结构）
      return res
    }
  },
  (error) => {
    // 对响应错误做点什么
    console.error('响应错误:', error)
    return Promise.reject(error)
  },
)

// 封装请求方法
const request = async (config) => {
  // 判断是否启用mock
  if (import.meta.env.VITE_MOCK_ENABLED === 'true') {
    // 等待Mock服务初始化完成
    await mockReadyPromise

    // mock请求时，使用mockjs处理
    console.log('使用Mock数据')
    return service(config)
  }

  // 使用真实请求
  return service(config)
}

export default request
